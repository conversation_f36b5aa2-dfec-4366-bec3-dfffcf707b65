# Network Error Fix - Property Management

## 🔍 **Issue Identified**

The backend is working perfectly, but the frontend is getting "Network error" when trying to add properties. This is a common issue in React Native/Expo apps.

## ✅ **Backend Status: WORKING**
- All endpoints are accessible and responding correctly
- Authentication is working (returns 401 as expected)
- Validation is working
- CORS is configured properly

## 🚨 **Frontend Issue: API Configuration**

The problem is likely one of these common React Native network issues:

### **1. Platform-Specific URL Issues**
React Native requires different URLs for different platforms:
- **Web/Browser**: `http://localhost:3000`
- **iOS Simulator**: `http://localhost:3000`
- **Android Emulator**: `http://********:3000`
- **Physical Device**: `http://YOUR_COMPUTER_IP:3000`

### **2. Axios Interceptor Error Handling**
The frontend API service has aggressive error handling that converts all errors to "Network error".

## 🔧 **Solutions**

### **Solution 1: Fix API Configuration for React Native**

Update `zakmakelaar-frontend/config/api.ts`:

```typescript
import { Platform } from 'react-native';

// Get the correct base URL for the platform
const getBaseUrl = () => {
  if (__DEV__) {
    if (Platform.OS === 'android') {
      // Android emulator
      return 'http://********:3000/api';
    } else if (Platform.OS === 'ios') {
      // iOS simulator
      return 'http://localhost:3000/api';
    } else {
      // Web or other platforms
      return 'http://localhost:3000/api';
    }
  } else {
    // Production
    return 'https://your-production-api.com/api';
  }
};

export const API_CONFIG = {
  DEV_BASE_URL: getBaseUrl(),
  PROD_BASE_URL: "https://your-production-api.com/api",
  TIMEOUT: 10000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
};
```

### **Solution 2: Improve Error Handling in API Service**

Update the axios response interceptor in `zakmakelaar-frontend/services/api.ts`:

```typescript
// Response interceptor for error handling
this.client.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    console.log('API Error Details:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      url: error.config?.url,
      method: error.config?.method
    });

    const originalRequest = error.config as any;

    // Don't convert auth errors to network errors
    if (error.response?.status === 401) {
      return Promise.reject({
        message: 'Authentication required',
        status: 401,
        code: 'UNAUTHORIZED'
      });
    }

    // Don't convert validation errors to network errors
    if (error.response?.status === 400) {
      return Promise.reject({
        message: error.response.data?.message || 'Validation error',
        status: 400,
        code: 'VALIDATION_ERROR'
      });
    }

    // Only convert actual network errors
    if (!error.response && (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR')) {
      return Promise.reject({
        code: 'NETWORK_ERROR',
        message: 'Network error. Please check your connection.',
        status: 0
      });
    }

    // Pass through other errors with more detail
    return Promise.reject(this.handleError(error));
  }
);
```

### **Solution 3: Use Consistent API Methods**

Update `propertyOwnerService.ts` to use consistent API methods:

```typescript
async addProperty(propertyData: PropertyData) {
  try {
    console.log('Adding property with data:', propertyData);
    const response = await apiService.post('/property-owner/properties', propertyData);
    console.log('Add property response:', response);
    return response;
  } catch (error) {
    console.error('Add property error details:', error);
    throw error;
  }
}
```

### **Solution 4: Test Different URLs**

If you're testing on a physical device, you need to use your computer's IP address:

1. Find your computer's IP address:
   ```bash
   # Windows
   ipconfig
   
   # Mac/Linux
   ifconfig
   ```

2. Update the API config:
   ```typescript
   DEV_BASE_URL: "http://192.168.1.XXX:3000/api"
   ```

## 🧪 **Testing Steps**

### **1. Check Platform**
```javascript
import { Platform } from 'react-native';
console.log('Platform:', Platform.OS);
console.log('API Base URL:', getApiBaseUrl());
```

### **2. Test Network Connectivity**
```javascript
// Add this to your component
const testConnection = async () => {
  try {
    const response = await fetch('http://localhost:3000/health');
    console.log('Connection test:', response.status);
  } catch (error) {
    console.log('Connection failed:', error.message);
  }
};
```

### **3. Enable Detailed Logging**
Add more logging to see exactly what's happening:

```typescript
// In propertyOwnerService.ts
async addProperty(propertyData: PropertyData) {
  try {
    console.log('=== ADD PROPERTY DEBUG ===');
    console.log('API Base URL:', this.baseUrl);
    console.log('Property Data:', JSON.stringify(propertyData, null, 2));
    
    const response = await apiService.post('/property-owner/properties', propertyData);
    
    console.log('Response received:', response);
    return response;
  } catch (error) {
    console.log('=== ADD PROPERTY ERROR ===');
    console.log('Error type:', typeof error);
    console.log('Error message:', error.message);
    console.log('Error code:', error.code);
    console.log('Error status:', error.status);
    console.log('Full error:', error);
    throw error;
  }
}
```

## 🎯 **Most Likely Fix**

Based on the error pattern, the most likely fix is **Solution 1** - updating the API configuration for the correct platform URL.

If you're running on:
- **Android Emulator**: Change to `http://********:3000/api`
- **Physical Device**: Change to `http://YOUR_COMPUTER_IP:3000/api`

## 📱 **Quick Test**

Try this in your React Native app:

```javascript
// Test direct fetch
const testAPI = async () => {
  try {
    const response = await fetch('http://********:3000/api/property-owner/properties', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test'
      },
      body: JSON.stringify({ title: 'Test' })
    });
    console.log('Direct fetch result:', response.status);
  } catch (error) {
    console.log('Direct fetch error:', error.message);
  }
};
```

The backend is working perfectly - this is purely a frontend configuration issue! 🚀