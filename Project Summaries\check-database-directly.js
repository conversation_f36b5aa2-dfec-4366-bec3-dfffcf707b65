/**
 * Direct Database Check Script
 * 
 * This script provides MongoDB commands to directly check what's in your database
 * and identify the exact issue
 */

console.log('🔍 === DIRECT DATABASE INVESTIGATION ===');
console.log('');
console.log('Run these commands in MongoDB Compass to see exactly what\'s in your database:');
console.log('');

console.log('📋 1. CHECK IF TARGET PROPERTY EXISTS:');
console.log('```javascript');
console.log('db.properties.findOne({ _id: ObjectId("68a9b3262e90c2ad7de52303") })');
console.log('```');
console.log('This will show if the property exists and who owns it.');
console.log('');

console.log('📋 2. CHECK IF TARGET APPLICATION EXISTS:');
console.log('```javascript');
console.log('db.applications.findOne({ _id: ObjectId("68a9d0b27cc6c69855b7b593") })');
console.log('```');
console.log('This will show if the application exists and which property it\'s linked to.');
console.log('');

console.log('📋 3. FIND ALL PROPERTIES OWNED BY OUR USER:');
console.log('```javascript');
console.log('db.properties.find({ "owner.userId": ObjectId("68a9cd0c01eb3f61f005a71c") })');
console.log('```');
console.log('This will show all properties owned by the logged-in user.');
console.log('');

console.log('📋 4. FIND ALL APPLICATIONS IN DATABASE:');
console.log('```javascript');
console.log('db.applications.find({}, { "applicant.snapshot.email": 1, "property.propertyId": 1, status: 1 })');
console.log('```');
console.log('This will show all applications and their property links.');
console.log('');

console.log('📋 5. CHECK USER ID FROM LOGIN:');
console.log('```javascript');
console.log('db.users.findOne({ email: "<EMAIL>" }, { _id: 1, email: 1 })');
console.log('```');
console.log('This will confirm the user ID we\'re using is correct.');
console.log('');

console.log('🎯 WHAT TO LOOK FOR:');
console.log('');
console.log('From command 1 (property):');
console.log('- Does the property exist?');
console.log('- What is the owner.userId value?');
console.log('- Should be: ObjectId("68a9cd0c01eb3f61f005a71c")');
console.log('');
console.log('From command 2 (application):');
console.log('- Does the application exist?');
console.log('- What is the property.propertyId value?');
console.log('- Should be: ObjectId("68a9b3262e90c2ad7de52303")');
console.log('- What is the applicant.snapshot.email?');
console.log('- Should be: "<EMAIL>"');
console.log('');
console.log('From command 3 (user properties):');
console.log('- How many properties does the user own?');
console.log('- Does it include 68a9b3262e90c2ad7de52303?');
console.log('');
console.log('From command 4 (all applications):');
console.log('- How many applications exist total?');
console.log('- Are any linked to properties owned by our user?');
console.log('');

console.log('🔧 BASED ON RESULTS, RUN THESE FIXES:');
console.log('');
console.log('If property exists but wrong owner:');
console.log('```javascript');
console.log('db.properties.updateOne(');
console.log('  { _id: ObjectId("68a9b3262e90c2ad7de52303") },');
console.log('  { $set: { "owner.userId": ObjectId("68a9cd0c01eb3f61f005a71c") } }');
console.log(')');
console.log('```');
console.log('');
console.log('If application exists but wrong property link:');
console.log('```javascript');
console.log('db.applications.updateOne(');
console.log('  { _id: ObjectId("68a9d0b27cc6c69855b7b593") },');
console.log('  { $set: { "property.propertyId": ObjectId("68a9b3262e90c2ad7de52303") } }');
console.log(')');
console.log('```');
console.log('');
console.log('If property doesn\'t exist, create it:');
console.log('```javascript');
console.log('db.properties.insertOne({');
console.log('  _id: ObjectId("68a9b3262e90c2ad7de52303"),');
console.log('  title: "Modern appartement 2",');
console.log('  owner: { userId: ObjectId("68a9cd0c01eb3f61f005a71c") },');
console.log('  status: "active"');
console.log('})');
console.log('```');
console.log('');

console.log('📞 AFTER RUNNING INVESTIGATION:');
console.log('Please share the results of commands 1-5 so I can help identify the exact issue!');
console.log('');

// Also run a quick API test
const axios = require('axios');

async function quickAPITest() {
  try {
    console.log('🧪 === QUICK API TEST ===');
    console.log('Testing the backend with debug logging...');
    
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.token || loginResponse.data.data?.token;
    console.log('✅ Login successful');
    
    console.log('📊 Calling applications endpoint (check backend console for debug logs)...');
    const appsResponse = await axios.get('http://localhost:3000/api/property-owner/applications', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('📋 Response received - check backend server console for debug output');
    console.log('📊 Applications in response:', appsResponse.data.data?.length || 0);
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

if (require.main === module) {
  console.log('Running API test in 3 seconds...');
  setTimeout(quickAPITest, 3000);
}