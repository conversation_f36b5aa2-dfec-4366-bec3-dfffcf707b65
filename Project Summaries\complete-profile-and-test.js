const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
    email: '<EMAIL>',
    password: 'Password123'
};

const TEST_PROPERTY = {
    "_id": "68aa22092a8c01773a0e9eaf",
    "title": "Acaciastraat 1",
    "price": "€ 1.000 per maand",
    "location": "Utrecht",
    "url": "https://www.funda.nl/detail/huur/utrecht/huis-acaciastraat-1/89474643/",
    "rooms": "1",
    "propertyType": "woning",
    "description": "Per direct beschikbaar\nKamer voor 1 persoon, de woning wordt gedeeld met 4 andere huurders\nHuurprijs: € 1000,- (inclusief gas, water en licht)\nBorg  € 1000,-\nVoor bezichtiging neem telefonisch contact met ons op.",
    "source": "funda.nl"
};

let authToken = null;
let userId = null;

async function login() {
    try {
        console.log('🔐 Logging in user...');
        const response = await axios.post(`${BASE_URL}/api/auth/login`, TEST_USER);
        
        authToken = response.data.token;
        userId = response.data.user?.id || response.data.data?.user?.id;
        
        console.log('✅ Login successful');
        console.log(`👤 User ID: ${userId}`);
        return response.data;
    } catch (error) {
        console.error('❌ Login failed:', error.response?.data || error.message);
        throw error;
    }
}

async function getAutoApplicationSettings() {
    try {
        console.log('\n⚙️ Getting current auto application settings...');
        const response = await axios.get(`${BASE_URL}/api/auto-application/settings`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        const settings = response.data.data;
        console.log('✅ Settings retrieved');
        console.log(`- Enabled: ${settings.enabled}`);
        console.log(`- Profile Complete: ${settings.isProfileComplete}`);
        console.log(`- Documents Complete: ${settings.documentsComplete}`);
        console.log(`- Can Auto Apply: ${settings.canAutoApply}`);
        
        return settings;
    } catch (error) {
        console.error('❌ Failed to get settings:', error.response?.data || error.message);
        throw error;
    }
}

async function completePersonalInfo(settingsUserId) {
    try {
        console.log('\n📝 Completing personal information...');
        
        const personalInfo = {
            personalInfo: {
                fullName: "Wellishant Test User",
                email: "<EMAIL>",
                phone: "+31612345678",
                dateOfBirth: "1990-01-01T00:00:00.000Z",
                nationality: "Dutch",
                occupation: "Software Developer",
                employer: "Tech Company BV",
                monthlyIncome: 4500,
                moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                leaseDuration: 12,
                numberOfOccupants: 1,
                hasGuarantor: false,
                guarantorInfo: {
                    name: "John Guarantor",
                    email: "<EMAIL>",
                    phone: "+31612345679",
                    relationship: "Parent",
                    monthlyIncome: 6000
                },
                emergencyContact: {
                    name: "Jane Emergency",
                    phone: "+31612345680",
                    email: "<EMAIL>",
                    relationship: "Sibling"
                }
            }
        };
        
        const response = await axios.put(`${BASE_URL}/api/auto-application/settings/${settingsUserId}`, personalInfo, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        console.log('✅ Personal information completed');
        return response.data;
    } catch (error) {
        console.error('❌ Failed to update personal info:', error.response?.data || error.message);
        console.error('Error details:', error.response?.status, error.response?.statusText);
        return null;
    }
}

async function updateApplicationCriteria(settingsUserId) {
    try {
        console.log('\n🎯 Updating application criteria...');
        
        const criteria = {
            criteria: {
                maxPrice: 1500, // Match our test property price
                minRooms: 1,
                maxRooms: 3,
                propertyTypes: ["apartment", "woning", "house"],
                locations: ["Utrecht", "Amsterdam"], // Include Utrecht for our test property
                excludeKeywords: ["student only", "no pets"],
                includeKeywords: ["furnished", "available"],
                minSize: 0,
                maxSize: 1000,
                furnished: false,
                petsAllowed: false,
                smokingAllowed: false
            }
        };
        
        const response = await axios.put(`${BASE_URL}/api/auto-application/settings/${settingsUserId}`, criteria, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        console.log('✅ Application criteria updated');
        return response.data;
    } catch (error) {
        console.error('❌ Failed to update criteria:', error.response?.data || error.message);
        return null;
    }
}

async function simulateDocumentUpload() {
    try {
        console.log('\n📄 Simulating document uploads...');
        
        // Since we can't actually upload files in this test, let's check what documents are required
        const response = await axios.get(`${BASE_URL}/api/auto-application/documents`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        console.log('✅ Required documents retrieved:');
        const requiredDocs = response.data.data.requiredDocuments;
        requiredDocs.forEach(doc => {
            console.log(`- ${doc.type}: ${doc.description} (Required: ${doc.required})`);
        });
        
        console.log('\n⚠️ Note: Document upload simulation - in a real scenario, you would upload:');
        console.log('- Income proof (salary slip)');
        console.log('- Employment contract');
        console.log('- Bank statements (last 3 months)');
        console.log('- Valid ID document');
        
        return response.data;
    } catch (error) {
        console.error('❌ Failed to get documents:', error.response?.data || error.message);
        return null;
    }
}

async function enableAutoApplication() {
    try {
        console.log('\n🔄 Enabling auto application...');
        const response = await axios.post(`${BASE_URL}/api/auto-application/enable`, {}, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        console.log('✅ Auto application enabled');
        return response.data;
    } catch (error) {
        console.error('❌ Failed to enable auto application:', error.response?.data || error.message);
        return null;
    }
}

async function addPropertyToQueue() {
    try {
        console.log('\n📋 Adding Funda property to queue...');
        console.log(`Property: ${TEST_PROPERTY.title} - ${TEST_PROPERTY.location}`);
        
        const queueItem = {
            listingId: TEST_PROPERTY._id,
            listingTitle: TEST_PROPERTY.title,
            listingUrl: TEST_PROPERTY.url,
            price: parseInt(TEST_PROPERTY.price.replace(/[^\d]/g, '')) || 1000,
            location: TEST_PROPERTY.location,
            propertyType: TEST_PROPERTY.propertyType === 'woning' ? 'house' : 'apartment',
            rooms: parseInt(TEST_PROPERTY.rooms) || 1,
            description: TEST_PROPERTY.description,
            priority: 5,
            scheduledFor: new Date(Date.now() + 60000) // 1 minute from now
        };
        
        console.log('Queue item data:', JSON.stringify(queueItem, null, 2));
        
        const response = await axios.post(`${BASE_URL}/api/auto-application/queue`, queueItem, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        console.log('✅ Property added to queue successfully');
        console.log('Queue response:', JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        console.error('❌ Failed to add property to queue');
        console.error('Status:', error.response?.status);
        console.error('Error:', error.response?.data || error.message);
        
        // Let's try to get more details about why it failed
        if (error.response?.status === 500) {
            console.log('\n🔍 Investigating 500 error...');
            console.log('This might be due to:');
            console.log('- Backend queue manager issues');
            console.log('- Database connection problems');
            console.log('- Missing required fields in queue item');
            console.log('- Profile/document validation failures');
        }
        
        return null;
    }
}

async function checkQueueStatus() {
    try {
        console.log('\n📋 Checking queue status...');
        const response = await axios.get(`${BASE_URL}/api/auto-application/queue`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        const queue = response.data.data;
        console.log(`✅ Queue retrieved - ${queue.applications?.length || 0} items`);
        
        if (queue.applications && queue.applications.length > 0) {
            queue.applications.forEach((item, index) => {
                console.log(`${index + 1}. ${item.listingTitle || 'Unknown'} - Status: ${item.status}`);
            });
        }
        
        return response.data;
    } catch (error) {
        console.error('❌ Failed to check queue:', error.response?.data || error.message);
        return null;
    }
}

async function getApplicationResults() {
    try {
        console.log('\n📊 Getting application results...');
        const response = await axios.get(`${BASE_URL}/api/auto-application/results`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        const results = response.data.data;
        console.log(`✅ Results retrieved - ${results.results?.length || 0} applications`);
        
        if (results.results && results.results.length > 0) {
            results.results.forEach((result, index) => {
                console.log(`${index + 1}. Status: ${result.status} - ${result.listingTitle || 'Unknown'}`);
            });
        }
        
        return response.data;
    } catch (error) {
        console.error('❌ Failed to get results:', error.response?.data || error.message);
        return null;
    }
}

async function getApplicationStatistics() {
    try {
        console.log('\n📈 Getting application statistics...');
        const response = await axios.get(`${BASE_URL}/api/auto-application/stats`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        const stats = response.data.data;
        console.log('✅ Statistics retrieved:');
        console.log(`- Total Applications: ${stats.totalApplications}`);
        console.log(`- Successful: ${stats.successfulApplications}`);
        console.log(`- Failed: ${stats.failedApplications}`);
        console.log(`- Success Rate: ${stats.successRate}%`);
        console.log(`- Applications Today: ${stats.applicationsToday}`);
        
        return response.data;
    } catch (error) {
        console.error('❌ Failed to get statistics:', error.response?.data || error.message);
        return null;
    }
}

async function runCompleteTest() {
    console.log('🚀 Complete Auto Application Profile Setup and Test');
    console.log('='.repeat(70));
    
    try {
        // Step 1: Login
        await login();
        
        // Step 2: Get current settings
        const initialSettings = await getAutoApplicationSettings();
        
        // Step 3: Complete personal information
        if (!initialSettings.isProfileComplete) {
            console.log('\n⚠️ Profile incomplete - completing now...');
            await completePersonalInfo(initialSettings.userId);
            
            // Update criteria as well
            await updateApplicationCriteria(initialSettings.userId);
        } else {
            console.log('\n✅ Profile already complete');
        }
        
        // Step 4: Handle documents
        await simulateDocumentUpload();
        
        // Step 5: Enable auto application
        await enableAutoApplication();
        
        // Step 6: Check updated settings
        console.log('\n🔄 Checking updated settings...');
        const updatedSettings = await getAutoApplicationSettings();
        
        // Step 7: Try to add property to queue
        const queueResult = await addPropertyToQueue();
        
        // Step 8: Check queue status
        await checkQueueStatus();
        
        // Step 9: Get results and statistics
        await getApplicationResults();
        await getApplicationStatistics();
        
        // Final summary
        console.log('\n📋 Test Summary');
        console.log('='.repeat(50));
        
        if (updatedSettings.canAutoApply) {
            console.log('✅ Auto application is fully configured and ready');
        } else {
            console.log('⚠️ Auto application setup incomplete:');
            if (!updatedSettings.isProfileComplete) {
                console.log('  - Profile needs completion');
            }
            if (!updatedSettings.documentsComplete) {
                console.log('  - Documents need to be uploaded and verified');
            }
        }
        
        if (queueResult) {
            console.log('✅ Successfully added Funda property to queue');
        } else {
            console.log('❌ Failed to add property to queue (backend issue)');
        }
        
        console.log('\n💡 Next Steps:');
        console.log('- Upload actual documents for full functionality');
        console.log('- Monitor queue processing');
        console.log('- Check application results regularly');
        console.log('- Adjust criteria based on success rates');
        
        console.log('\n✅ Complete test finished!');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run the complete test
runCompleteTest();