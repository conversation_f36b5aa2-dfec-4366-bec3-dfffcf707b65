// Debug network error when adding property
async function debugNetworkError() {
  console.log('=== DEBUGGING NETWORK ERROR ===\n');
  
  const baseUrl = 'http://localhost:3000/api/property-owner';
  
  // Test data similar to what frontend would send
  const propertyData = {
    title: 'Test Property from Debug',
    description: 'Test description for debugging network error',
    address: {
      street: 'Test Street',
      houseNumber: '123',
      postalCode: '1234AB',
      city: 'Amsterdam',
      province: 'Noord-Holland'
    },
    propertyType: 'apartment',
    size: 75,
    rooms: 3,
    bedrooms: 2,
    bathrooms: 1,
    rent: {
      amount: 1500,
      currency: 'EUR',
      deposit: 3000,
      additionalCosts: {
        utilities: 150,
        serviceCharges: 50,
        parking: 0,
        other: 0
      }
    },
    features: {
      furnished: false,
      interior: 'gestoffeerd',
      parking: true,
      balcony: true,
      garden: false,
      elevator: true,
      energyLabel: 'B'
    },
    policies: {
      petsAllowed: false,
      smokingAllowed: false,
      studentsAllowed: true,
      expatFriendly: true,
      minimumIncome: 4500,
      maximumOccupants: 2
    },
    status: 'draft',
    images: [],
    availabilityDate: '2024-03-01'
  };
  
  console.log('1. Testing basic connectivity...');
  try {
    const healthResponse = await fetch('http://localhost:3000/health');
    console.log('✅ Health check:', healthResponse.status);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return;
  }
  
  console.log('\n2. Testing property owner endpoint without auth...');
  try {
    const noAuthResponse = await fetch(`${baseUrl}/properties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(propertyData)
    });
    console.log('Response status (no auth):', noAuthResponse.status);
    console.log('Expected: 401 (Unauthorized)');
  } catch (error) {
    console.log('❌ Network error (no auth):', error.message);
    console.log('This suggests a connection problem');
    return;
  }
  
  console.log('\n3. Testing with mock auth token...');
  try {
    const authResponse = await fetch(`${baseUrl}/properties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token-for-testing'
      },
      body: JSON.stringify(propertyData)
    });
    
    console.log('Response status (with auth):', authResponse.status);
    
    const responseText = await authResponse.text();
    console.log('Response body:', responseText);
    
    if (authResponse.status === 401) {
      console.log('✅ Endpoint accessible, auth working (401 expected with mock token)');
    } else if (authResponse.status === 400) {
      console.log('✅ Endpoint accessible, validation working (400 expected)');
    } else {
      console.log('⚠️ Unexpected status, but connection working');
    }
    
  } catch (error) {
    console.log('❌ Network error (with auth):', error.message);
    console.log('Error details:', error);
    
    // Check specific error types
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Backend server is not running or not accessible');
    } else if (error.code === 'ENOTFOUND') {
      console.log('💡 DNS resolution failed - check the URL');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('💡 Request timed out - server may be overloaded');
    } else {
      console.log('💡 Unknown network error - check firewall/proxy settings');
    }
    
    return;
  }
  
  console.log('\n4. Testing different URLs...');
  const testUrls = [
    'http://localhost:3000/api/property-owner/properties',
    'http://127.0.0.1:3000/api/property-owner/properties',
    'http://************:3000/api/property-owner/properties'
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`Testing: ${url}`);
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer test'
        }
      });
      console.log(`✅ ${url} - Status: ${response.status}`);
    } catch (error) {
      console.log(`❌ ${url} - Error: ${error.message}`);
    }
  }
  
  console.log('\n5. Frontend API Configuration Check...');
  console.log('Current frontend config should be: http://localhost:3000/api');
  console.log('Make sure the frontend is using the correct base URL');
  
  console.log('\n=== DEBUGGING COMPLETE ===');
  console.log('If all tests pass but frontend still has network errors:');
  console.log('1. Check frontend API configuration');
  console.log('2. Check if frontend is running on different port');
  console.log('3. Check browser console for CORS errors');
  console.log('4. Verify authentication token is being sent correctly');
}

debugNetworkError().catch(console.error);