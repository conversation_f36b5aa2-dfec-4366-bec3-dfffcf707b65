const axios = require('axios');

async function debugScraperState() {
  try {
    console.log('🔍 Debugging Scraper State...\n');

    // Check scraper status
    console.log('1. Checking scraper status...');
    const scraperStatus = await axios.get('http://localhost:3000/api/scraper/status');
    console.log('Scraper Status:', JSON.stringify(scraperStatus.data, null, 2));
    console.log();

    // Check agent status
    console.log('2. Checking agent status...');
    const agentStatus = await axios.get('http://localhost:3000/api/scraper/agent/status');
    console.log('Agent Status:', JSON.stringify(agentStatus.data, null, 2));
    console.log();

    // If agent is running, stop it
    if (agentStatus.data.data.isRunning) {
      console.log('3. Stopping agent...');
      const stopResult = await axios.post('http://localhost:3000/api/scraper/agent/stop');
      console.log('Stop Result:', JSON.stringify(stopResult.data, null, 2));
    } else {
      console.log('3. Agent is not running');
    }

    console.log('\n✅ Debug complete');

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

debugScraperState();