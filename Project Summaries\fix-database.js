/**
 * Database Fix Script
 * 
 * This script provides the exact MongoDB commands to fix the database
 * and then tests to verify the fixes worked
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

class DatabaseFixer {
  constructor() {
    this.token = null;
    this.userId = '68a9cd0c01eb3f61f005a71c';
    this.targetPropertyId = '68a9b3262e90c2ad7de52303';
    this.targetApplicationId = '68a9d0b27cc6c69855b7b593';
  }

  async apiRequest(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${API_BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...(this.token && { 'Authorization': `Bearer ${this.token}` })
        },
        ...(data && { data })
      };

      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`❌ ${method.toUpperCase()} ${endpoint} failed:`, error.response?.data?.message || error.message);
      throw error;
    }
  }

  async login() {
    console.log('🔐 Logging in as property owner...');
    const response = await this.apiRequest('POST', '/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    this.token = response.token || response.data?.token;
    console.log('✅ Logged in successfully');
  }

  showMongoDBCommands() {
    console.log('\n🗄️  === MONGODB COMMANDS TO RUN ===');
    console.log('');
    console.log('📋 Copy and paste these commands into MongoDB Compass or MongoDB shell:');
    console.log('');
    
    console.log('🔧 STEP 1: Fix Property Ownership');
    console.log('```javascript');
    console.log(`db.properties.updateOne(`);
    console.log(`  { _id: ObjectId("${this.targetPropertyId}") },`);
    console.log(`  { $set: { "owner.userId": ObjectId("${this.userId}") } }`);
    console.log(`)`);
    console.log('```');
    console.log('');
    
    console.log('🔧 STEP 2: Fix Application Property Link');
    console.log('```javascript');
    console.log(`db.applications.updateOne(`);
    console.log(`  { _id: ObjectId("${this.targetApplicationId}") },`);
    console.log(`  { $set: { "property.propertyId": ObjectId("${this.targetPropertyId}") } }`);
    console.log(`)`);
    console.log('```');
    console.log('');
    
    console.log('🔍 STEP 3: Verify Property Ownership');
    console.log('```javascript');
    console.log(`db.properties.findOne(`);
    console.log(`  { _id: ObjectId("${this.targetPropertyId}") },`);
    console.log(`  { title: 1, "owner.userId": 1, status: 1 }`);
    console.log(`)`);
    console.log('```');
    console.log('Expected: owner.userId should be ObjectId("68a9cd0c01eb3f61f005a71c")');
    console.log('');
    
    console.log('🔍 STEP 4: Verify Application Link');
    console.log('```javascript');
    console.log(`db.applications.findOne(`);
    console.log(`  { _id: ObjectId("${this.targetApplicationId}") },`);
    console.log(`  { "applicant.snapshot.email": 1, "property.propertyId": 1, status: 1 }`);
    console.log(`)`);
    console.log('```');
    console.log('Expected: property.propertyId should be ObjectId("68a9b3262e90c2ad7de52303")');
    console.log('');
    
    console.log('🎯 EXPECTED RESULTS:');
    console.log('- Step 1 should return: { acknowledged: true, matchedCount: 1, modifiedCount: 1 }');
    console.log('- Step 2 should return: { acknowledged: true, matchedCount: 1, modifiedCount: 1 }');
    console.log('- Step 3 should show the property with correct owner.userId');
    console.log('- Step 4 should show the application with correct property.propertyId');
    console.log('');
  }

  async testBeforeFix() {
    console.log('\n🧪 === TESTING BEFORE FIX ===');
    
    try {
      const response = await this.apiRequest('GET', '/property-owner/applications');
      const applications = response.data || [];
      
      console.log(`📊 Applications found BEFORE fix: ${applications.length}`);
      
      if (applications.length === 0) {
        console.log('❌ No applications found - this confirms the issue exists');
      } else {
        console.log('✅ Applications found:');
        applications.forEach((app, index) => {
          console.log(`   ${index + 1}. ${app.applicantName} (${app.applicantEmail})`);
        });
      }
      
      return applications.length;
    } catch (error) {
      console.log('❌ Failed to test before fix:', error.message);
      return 0;
    }
  }

  async testAfterFix() {
    console.log('\n🧪 === TESTING AFTER FIX ===');
    
    try {
      const response = await this.apiRequest('GET', '/property-owner/applications');
      const applications = response.data || [];
      
      console.log(`📊 Applications found AFTER fix: ${applications.length}`);
      
      if (applications.length === 0) {
        console.log('❌ Still no applications found - fix may not have worked');
        console.log('💡 Please verify you ran the MongoDB commands correctly');
        return false;
      } else {
        console.log('🎉 SUCCESS! Applications found:');
        applications.forEach((app, index) => {
          console.log(`\n   ${index + 1}. Application Details:`);
          console.log(`      🆔 ID: ${app._id}`);
          console.log(`      👤 Name: ${app.applicantName}`);
          console.log(`      📧 Email: ${app.applicantEmail}`);
          console.log(`      📱 Phone: ${app.applicantPhone}`);
          console.log(`      🏠 Property: ${app.propertyAddress}`);
          console.log(`      🏠 Property ID: ${app.propertyId}`);
          console.log(`      📊 Status: ${app.status}`);
          console.log(`      💳 Credit Score: ${app.creditScore}`);
          console.log(`      📄 Documents: ${app.documents?.length || 0} files`);
        });
        
        // Look for the specific real application
        const realApp = applications.find(app => 
          app.applicantEmail === '<EMAIL>' || 
          app._id === this.targetApplicationId
        );
        
        if (realApp) {
          console.log('\n🎊 REAL APPLICATION FROM DATABASE FOUND!');
          console.log(`👤 Applicant: ${realApp.applicantName}`);
          console.log(`📧 Email: ${realApp.applicantEmail}`);
          console.log(`🏠 Property: ${realApp.propertyAddress}`);
          console.log(`📊 Status: ${realApp.status}`);
          
          // Test status update on real application
          await this.testStatusUpdate(realApp._id);
        }
        
        return true;
      }
    } catch (error) {
      console.log('❌ Failed to test after fix:', error.message);
      return false;
    }
  }

  async testStatusUpdate(applicationId) {
    console.log(`\n🔄 === TESTING STATUS UPDATE ON REAL APPLICATION ===`);
    console.log(`🎯 Testing with application: ${applicationId}`);
    
    try {
      console.log('🟢 Testing approval...');
      const approvalResponse = await this.apiRequest('PUT', `/property-owner/applications/${applicationId}/status`, {
        status: 'approved',
        notes: 'Approved real application from database - testing complete functionality!'
      });
      
      console.log('✅ Real application approved successfully!');
      console.log(`   Previous Status: ${approvalResponse.data.previousStatus}`);
      console.log(`   New Status: ${approvalResponse.data.newStatus}`);
      console.log(`   Updated At: ${approvalResponse.data.updatedAt}`);

      // Wait and reset
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('🔄 Resetting to under_review...');
      await this.apiRequest('PUT', `/property-owner/applications/${applicationId}/status`, {
        status: 'under_review',
        notes: 'Reset after testing - ready for property owner to review'
      });
      console.log('✅ Status reset successfully');

    } catch (error) {
      console.log('❌ Status update test failed:', error.message);
    }
  }

  async waitForUserInput() {
    console.log('\n⏳ === WAITING FOR DATABASE FIX ===');
    console.log('');
    console.log('🔧 Please run the MongoDB commands shown above, then press any key to continue...');
    console.log('');
    console.log('💡 Steps:');
    console.log('1. Open MongoDB Compass');
    console.log('2. Connect to your database');
    console.log('3. Open the MongoDB shell (>_MONGOSH at bottom)');
    console.log('4. Copy and paste the commands from STEP 1 and STEP 2');
    console.log('5. Verify with STEP 3 and STEP 4 commands');
    console.log('6. Come back here and press Enter');
    console.log('');
    
    // Simple way to wait for user input in Node.js
    return new Promise((resolve) => {
      process.stdin.setRawMode(true);
      process.stdin.resume();
      process.stdin.on('data', () => {
        process.stdin.setRawMode(false);
        process.stdin.pause();
        resolve();
      });
    });
  }

  async runFix() {
    console.log('🔧 === DATABASE FIX SCRIPT ===');
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    console.log('');
    console.log('🎯 Target Property: 68a9b3262e90c2ad7de52303 (Modern appartement 2)');
    console.log('🎯 Target Application: 68a9d0b27cc6c69855b7b593 (<EMAIL>)');
    console.log('🎯 Target User: 68a9cd0c01eb3f61f005a71c (<EMAIL>)');
    
    try {
      // Step 1: Login
      await this.login();
      
      // Step 2: Test current state
      const beforeCount = await this.testBeforeFix();
      
      // Step 3: Show MongoDB commands
      this.showMongoDBCommands();
      
      // Step 4: Wait for user to run commands
      await this.waitForUserInput();
      
      // Step 5: Test after fix
      console.log('\n🔄 Testing after database fix...');
      const success = await this.testAfterFix();
      
      // Summary
      console.log('\n🎉 === FIX RESULTS ===');
      console.log(`✅ Login: Success`);
      console.log(`📊 Applications Before: ${beforeCount}`);
      console.log(`${success ? '✅' : '❌'} Applications After: ${success ? 'Found' : 'Not Found'}`);
      console.log(`${success ? '✅' : '❌'} Database Fix: ${success ? 'Success' : 'Failed'}`);
      
      if (success) {
        console.log('\n🎊 DATABASE FIX SUCCESSFUL!');
        console.log('\n📱 Ready for mobile app testing:');
        console.log('1. Open the React Native app');
        console.log('2. Login as property owner (<EMAIL>)');
        console.log('3. Navigate to "Screening" tab');
        console.log('4. You should see the real <NAME_EMAIL>');
        console.log('5. Test approve/reject functionality');
        console.log('6. Verify status changes are saved to database');
        
        console.log('\n🎯 The tenant screening dashboard is now fully functional with real database data!');
      } else {
        console.log('\n⚠️  DATABASE FIX FAILED');
        console.log('Please check:');
        console.log('1. MongoDB commands were executed correctly');
        console.log('2. Database connection is working');
        console.log('3. Property and application IDs are correct');
        console.log('4. Backend server is running and updated');
      }
      
    } catch (error) {
      console.error('\n💥 FIX FAILED:', error.message);
    }
    
    console.log(`\n📅 Completed at: ${new Date().toISOString()}`);
  }
}

// Run the fix
if (require.main === module) {
  const fixer = new DatabaseFixer();
  fixer.runFix().catch(console.error);
}

module.exports = DatabaseFixer;