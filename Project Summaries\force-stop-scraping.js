const axios = require('axios');

async function forceStopScraping() {
  try {
    console.log('🛑 Force stopping all scraping processes...\n');

    // 1. Check current status
    console.log('1. Current status:');
    const status = await axios.get('http://localhost:3000/api/scraper/status');
    console.log('Scrapers:', status.data.data.activeScrapers);
    
    const agentStatus = await axios.get('http://localhost:3000/api/scraper/agent/status');
    console.log('Agent running:', agentStatus.data.data.isRunning);
    console.log('Agent config active scrapers:', agentStatus.data.data.config.activeScrapers);
    console.log();

    // 2. Disable all scrapers if not already disabled
    if (status.data.data.totalActive > 0) {
      console.log('2. Disabling all scrapers...');
      await axios.post('http://localhost:3000/api/scraper/disable/funda').catch(() => {});
      await axios.post('http://localhost:3000/api/scraper/disable/pararius').catch(() => {});
      await axios.post('http://localhost:3000/api/scraper/disable/huurwoningen').catch(() => {});
      console.log('✅ All scrapers disabled');
    } else {
      console.log('2. All scrapers already disabled');
    }

    // 3. Stop agent if running
    if (agentStatus.data.data.isRunning) {
      console.log('3. Stopping agent...');
      const stopResult = await axios.post('http://localhost:3000/api/scraper/agent/stop');
      console.log('✅ Agent stopped:', stopResult.data.message);
    } else {
      console.log('3. Agent already stopped');
    }

    // 4. Final verification
    console.log('\n4. Final verification:');
    const finalStatus = await axios.get('http://localhost:3000/api/scraper/status');
    const finalAgentStatus = await axios.get('http://localhost:3000/api/scraper/agent/status');
    
    console.log('Active scrapers:', finalStatus.data.data.activeScrapers);
    console.log('Agent running:', finalAgentStatus.data.data.isRunning);
    
    if (finalStatus.data.data.totalActive === 0 && !finalAgentStatus.data.data.isRunning) {
      console.log('\n✅ SUCCESS: All scraping should now be stopped!');
      console.log('If scraping continues, it may be from:');
      console.log('- Manual API calls to /api/scraper');
      console.log('- Other scheduled processes');
      console.log('- Already running browser instances that need time to close');
    } else {
      console.log('\n❌ WARNING: Some processes may still be active');
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

forceStopScraping();