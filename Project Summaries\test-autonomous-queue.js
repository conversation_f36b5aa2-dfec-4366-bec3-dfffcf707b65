#!/usr/bin/env node

const axios = require("axios");

// Test configuration
const baseURL = process.env.BASE_URL || "http://localhost:3000";
const testUser = {
  email: "<EMAIL>",
  password: "Password123",
};

let authToken = "";

// Helper function to log with timestamp
function log(message) {
  console.log(`[${new Date().toISOString()}] ${message}`);
}

// Test 1: Login and get auth token
async function loginUser() {
  try {
    log("🔐 Logging in test user...");

    const response = await axios.post(`${baseURL}/api/auth/login`, testUser);

    if (response.data && response.data.token) {
      authToken = response.data.token;
      log("✅ Login successful");
      return true;
    } else {
      log("❌ Login failed - no token in response");
      return false;
    }
  } catch (error) {
    log(`❌ Login failed: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

// Test 2: Test autonomous application submission
async function testAutonomousApplicationSubmission() {
  try {
    log("🚀 Testing autonomous application submission...");

    const applicationData = {
      applicationId: `test_app_${Date.now()}`,
      method: "autonomous",
      applicationData: {
        listingId: `test_listing_${Date.now()}`,
        message: "This is a test autonomous application message.",
        subject: "Application for Test Property",
        propertyTitle: "Test Property for Autonomous Application",
      },
    };

    const response = await axios.post(
      `${baseURL}/api/ai/application/submit`,
      applicationData,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        timeout: 15000,
      }
    );

    if (response.data && response.data.success) {
      log("✅ Autonomous application submitted successfully");
      log(`📋 Submission ID: ${response.data.data?.submissionId}`);
      log(`📋 Application DB ID: ${response.data.data?.applicationDbId}`);
      log(`📋 Queue Item ID: ${response.data.data?.queueItemId}`);
      log(`📋 Method: ${response.data.data?.method}`);

      if (response.data.data?.queueItemId) {
        log("✅ Application was successfully added to the autonomous queue!");
        return {
          success: true,
          queueItemId: response.data.data.queueItemId,
          submissionId: response.data.data.submissionId,
        };
      } else {
        log("⚠️ Application was submitted but NOT added to queue");
        return { success: false, reason: "Not added to queue" };
      }
    } else {
      log("❌ Application submission failed - response not successful");
      return { success: false, reason: "Response not successful" };
    }
  } catch (error) {
    log(
      `❌ Autonomous application submission failed: ${
        error.response?.data?.error || error.message
      }`
    );
    if (error.response?.data?.details) {
      log(`📋 Details: ${error.response.data.details}`);
    }
    return { success: false, error: error.message };
  }
}

// Test 3: Check auto-application queue
async function checkAutoApplicationQueue() {
  try {
    log("📋 Checking auto-application queue...");

    const response = await axios.get(`${baseURL}/api/auto-application/queue`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (response.data && response.data.status === "success") {
      const queueItems = response.data.data || [];
      log(
        `✅ Queue retrieved successfully - ${queueItems.length} items in queue`
      );

      if (queueItems.length > 0) {
        log("📋 Recent queue items:");
        queueItems.slice(-3).forEach((item, index) => {
          log(
            `   ${index + 1}. ID: ${item._id} | Listing: ${
              item.listingTitle
            } | Status: ${item.status}`
          );
        });
      }

      return { success: true, queueCount: queueItems.length };
    } else {
      log("❌ Failed to retrieve queue");
      return { success: false };
    }
  } catch (error) {
    log(
      `❌ Failed to check queue: ${
        error.response?.data?.message || error.message
      }`
    );
    return { success: false, error: error.message };
  }
}

// Test 4: Test manual application submission (for comparison)
async function testManualApplicationSubmission() {
  try {
    log("📝 Testing manual application submission for comparison...");

    const applicationData = {
      applicationId: `test_manual_app_${Date.now()}`,
      method: "manual",
      applicationData: {
        listingId: `test_manual_listing_${Date.now()}`,
        message: "This is a test manual application message.",
        subject: "Manual Application for Test Property",
        propertyTitle: "Test Property for Manual Application",
      },
    };

    const response = await axios.post(
      `${baseURL}/api/ai/application/submit`,
      applicationData,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data && response.data.success) {
      log("✅ Manual application submitted successfully");
      log(
        `📋 Queue Item ID: ${
          response.data.data?.queueItemId || "N/A (expected for manual)"
        }`
      );

      if (!response.data.data?.queueItemId) {
        log("✅ Manual application correctly NOT added to queue");
        return { success: true, addedToQueue: false };
      } else {
        log("⚠️ Manual application was unexpectedly added to queue");
        return { success: true, addedToQueue: true };
      }
    } else {
      log("❌ Manual application submission failed");
      return { success: false };
    }
  } catch (error) {
    log(`❌ Manual application submission failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Main test runner
async function runTests() {
  console.log("🧪 Testing Autonomous Application Queue Functionality");
  console.log("=".repeat(60));

  // Test 1: Login
  const loginSuccess = await loginUser();
  if (!loginSuccess) {
    log("❌ Cannot continue without authentication");
    return;
  }

  console.log("\n" + "-".repeat(40));

  // Test 2: Check initial queue state
  const initialQueue = await checkAutoApplicationQueue();
  const initialCount = initialQueue.queueCount || 0;
  log(`📊 Initial queue count: ${initialCount}`);

  console.log("\n" + "-".repeat(40));

  // Test 3: Submit autonomous application
  const autonomousResult = await testAutonomousApplicationSubmission();

  console.log("\n" + "-".repeat(40));

  // Test 4: Check queue after autonomous submission
  if (autonomousResult.success) {
    log("⏳ Waiting 2 seconds for queue update...");
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const postAutonomousQueue = await checkAutoApplicationQueue();
    const newCount = postAutonomousQueue.queueCount || 0;

    if (newCount > initialCount) {
      log(
        `✅ Queue count increased from ${initialCount} to ${newCount} - autonomous application added!`
      );
    } else {
      log(
        `⚠️ Queue count did not increase (${initialCount} -> ${newCount}) - check if autonomous application was added`
      );
    }
  }

  console.log("\n" + "-".repeat(40));

  // Test 5: Submit manual application for comparison
  const manualResult = await testManualApplicationSubmission();

  console.log("\n" + "-".repeat(40));

  // Test 6: Final queue check
  const finalQueue = await checkAutoApplicationQueue();
  const finalCount = finalQueue.queueCount || 0;

  console.log("\n" + "=".repeat(60));
  console.log("📊 FINAL RESULTS:");
  console.log(`   Initial queue count: ${initialCount}`);
  console.log(`   Final queue count: ${finalCount}`);
  console.log(
    `   Autonomous submission: ${
      autonomousResult.success ? "SUCCESS" : "FAILED"
    }`
  );
  console.log(
    `   Manual submission: ${manualResult.success ? "SUCCESS" : "FAILED"}`
  );

  if (autonomousResult.success && autonomousResult.queueItemId) {
    console.log("\n✅ AUTONOMOUS APPLICATION QUEUE IS WORKING CORRECTLY!");
    console.log("   - Autonomous applications are being added to the queue");
    console.log("   - Manual applications are correctly NOT added to queue");
  } else {
    console.log("\n❌ ISSUE DETECTED:");
    console.log(
      "   - Autonomous applications may not be getting added to the queue"
    );
    console.log("   - Check the backend logs for more details");
  }

  console.log("\n" + "=".repeat(60));
}

// Run the tests
runTests().catch((error) => {
  console.error("❌ Test runner failed:", error);
  process.exit(1);
});
