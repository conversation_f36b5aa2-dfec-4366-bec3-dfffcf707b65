/**
 * Debug Script: Check Property Ownership and Database State
 * 
 * This script debugs why applications aren't showing up by checking:
 * 1. What properties the logged-in user owns
 * 2. What applications exist in the database
 * 3. Property ownership linkage
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

class OwnershipDebugTest {
  constructor() {
    this.token = null;
    this.userId = null;
  }

  async apiRequest(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${API_BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...(this.token && { 'Authorization': `Bearer ${this.token}` })
        },
        ...(data && { data })
      };

      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`❌ ${method.toUpperCase()} ${endpoint} failed:`, error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.error('   Response data:', JSON.stringify(error.response.data, null, 2));
      }
      throw error;
    }
  }

  async login() {
    console.log('🔐 Logging in as property owner...');
    const response = await this.apiRequest('POST', '/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    this.token = response.token || response.data?.token;
    this.userId = response.user?.id || response.data?.user?.id;
    console.log('✅ Logged in successfully');
    console.log(`👤 User ID: ${this.userId}`);
  }

  async checkUserProperties() {
    console.log('\n🏠 === CHECKING USER PROPERTIES ===');
    
    try {
      const response = await this.apiRequest('GET', '/property-owner/properties');
      const properties = response.data || [];
      
      console.log(`🏠 Properties owned by user: ${properties.length}`);
      
      if (properties.length === 0) {
        console.log('❌ User owns no properties - this explains why no applications are found');
        console.log('💡 The application query filters by owned properties only');
        return [];
      }

      console.log('\n🏠 Owned Properties:');
      properties.forEach((property, index) => {
        console.log(`\n   ${index + 1}. Property ID: ${property._id}`);
        console.log(`      📝 Title: ${property.title}`);
        console.log(`      📍 Address: ${property.address?.street} ${property.address?.houseNumber}, ${property.address?.city}`);
        console.log(`      📊 Status: ${property.status}`);
        console.log(`      💰 Rent: €${property.rent?.amount}/month`);
        console.log(`      👤 Owner ID: ${property.owner?.userId}`);
      });

      // Check if the target property is owned
      const targetPropertyId = '68a9b3262e90c2ad7de52303';
      const ownsTargetProperty = properties.some(prop => prop._id === targetPropertyId);
      
      console.log(`\n🎯 Target Property (${targetPropertyId}): ${ownsTargetProperty ? 'OWNED' : 'NOT OWNED'}`);
      
      return properties;
    } catch (error) {
      console.log('❌ Failed to fetch properties:', error.message);
      return [];
    }
  }

  async checkDatabaseDirectly() {
    console.log('\n🗄️  === CHECKING DATABASE DIRECTLY ===');
    
    // Since we can't directly query MongoDB from here, let's create a debug endpoint
    // or check what we can infer from the API responses
    
    console.log('📊 Attempting to get all applications (should be empty due to ownership filter)...');
    try {
      const response = await this.apiRequest('GET', '/property-owner/applications');
      console.log('📋 Applications response:', JSON.stringify(response, null, 2));
    } catch (error) {
      console.log('❌ Applications request failed');
    }
  }

  async testPropertyCreation() {
    console.log('\n🏗️  === TESTING PROPERTY CREATION ===');
    
    console.log('📝 Creating the target property to establish ownership...');
    
    const propertyData = {
      title: 'Modern appartement 2',
      description: 'Beautiful appartement on the street of endhoven',
      address: {
        street: 'Test Street',
        houseNumber: '123',
        postalCode: '1012AB',
        city: 'Amsterdam',
        province: 'Noord-Holland'
      },
      propertyType: 'apartment',
      size: 75,
      rooms: 3,
      bedrooms: 2,
      bathrooms: 1,
      rent: {
        amount: 1200,
        currency: 'EUR',
        deposit: 2400,
        additionalCosts: {
          utilities: 100,
          serviceCharges: 50,
          parking: 0,
          other: 0
        }
      },
      features: {
        furnished: false,
        interior: 'kaal',
        parking: false,
        balcony: true,
        garden: false,
        elevator: true,
        energyLabel: 'B'
      },
      policies: {
        petsAllowed: false,
        smokingAllowed: false,
        studentsAllowed: true,
        expatFriendly: true,
        minimumIncome: 3600,
        maximumOccupants: 2
      },
      status: 'active',
      availabilityDate: '2025-09-01'
    };

    try {
      const response = await this.apiRequest('POST', '/property-owner/properties', propertyData);
      console.log('✅ Property created successfully');
      console.log('📋 Response:', JSON.stringify(response, null, 2));
      
      const newPropertyId = response.data?.propertyId || response.propertyId;
      console.log(`🆔 New Property ID: ${newPropertyId}`);
      
      return newPropertyId;
    } catch (error) {
      console.log('❌ Property creation failed:', error.message);
      return null;
    }
  }

  async createTestApplication(propertyId) {
    console.log('\n📝 === CREATING TEST APPLICATION ===');
    
    if (!propertyId) {
      console.log('❌ No property ID available for application');
      return;
    }

    console.log(`📋 Creating application for property: ${propertyId}`);
    
    // Since we don't have a direct application creation endpoint,
    // we'll need to manually add it to the database or use a different approach
    console.log('💡 In a real scenario, a tenant would submit an application through the tenant app');
    console.log('💡 For testing, you would need to:');
    console.log('   1. Use the tenant mobile app to submit an application');
    console.log('   2. Or manually insert an application document in MongoDB');
    console.log('   3. Or create a test endpoint for application submission');
  }

  async runDebug() {
    console.log('🔍 === OWNERSHIP DEBUG TEST ===');
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    
    try {
      // Step 1: Login and get user info
      await this.login();
      
      // Step 2: Check what properties the user owns
      const properties = await this.checkUserProperties();
      
      // Step 3: Check database state
      await this.checkDatabaseDirectly();
      
      // Step 4: If no properties, create one for testing
      if (properties.length === 0) {
        console.log('\n💡 === SOLUTION: CREATE PROPERTY FOR TESTING ===');
        const newPropertyId = await this.testPropertyCreation();
        
        if (newPropertyId) {
          // Step 5: Explain how to create applications
          await this.createTestApplication(newPropertyId);
        }
      }
      
      // Summary and recommendations
      console.log('\n🎉 === DEBUG RESULTS ===');
      console.log(`✅ Login: Success`);
      console.log(`📊 Properties Owned: ${properties.length}`);
      
      if (properties.length === 0) {
        console.log('\n🔍 ROOT CAUSE IDENTIFIED:');
        console.log('❌ The logged-in user owns NO properties');
        console.log('❌ The application query filters by owned properties');
        console.log('❌ Therefore, no applications are returned');
        
        console.log('\n💡 SOLUTIONS:');
        console.log('1. 🏠 Create a property owned by this user');
        console.log('2. 📱 Use tenant app to apply to that property');
        console.log('3. 🔄 Then applications will appear in screening dashboard');
        
        console.log('\n🚀 QUICK FIX:');
        console.log('1. Run this script again - it created a test property');
        console.log('2. Use the tenant mobile app to apply to the new property');
        console.log('3. Check the screening dashboard again');
      } else {
        console.log('\n✅ User owns properties - applications should work');
        console.log('💡 If still no applications, check that tenants have applied to these properties');
      }
      
    } catch (error) {
      console.error('\n💥 DEBUG FAILED:', error.message);
    }
    
    console.log(`\n📅 Completed at: ${new Date().toISOString()}`);
  }
}

// Run the debug
if (require.main === module) {
  const test = new OwnershipDebugTest();
  test.runDebug().catch(console.error);
}

module.exports = OwnershipDebugTest;