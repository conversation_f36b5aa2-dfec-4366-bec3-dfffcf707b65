#!/usr/bin/env node

const axios = require("axios");

async function testApplicationSubmission() {
  try {
    console.log("🔐 Logging in...");
    // First login to get token
    const loginResponse = await axios.post(
      "http://localhost:3000/api/auth/login",
      {
        email: "<EMAIL>",
        password: "Password123",
      }
    );

    const token = loginResponse.data.token;
    console.log("✅ Login successful");

    // Generate an application first
    console.log("🤖 Generating AI application...");
    const applicationRequest = {
      listing: {
        _id: "507f1f77bcf86cd799439011", // Valid ObjectId format
        title: "Test Property for Debug",
        location: "Amsterdam",
        price: "1800",
        url: "https://example.com/listing/507f1f77bcf86cd799439011",
      },
      userProfile: {
        name: "Test User",
        income: 4000,
        occupation: "Software Developer",
      },
      template: "professional",
    };

    const appResponse = await axios.post(
      "http://localhost:3000/api/ai/application/generate",
      applicationRequest,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (appResponse.data && appResponse.data.status === "success") {
      console.log("✅ Application generated successfully");

      // Now test autonomous submission
      console.log("🚀 Testing AUTONOMOUS submission...");

      const submissionData = {
        applicationId: `app_${Date.now()}`,
        method: "autonomous",
        applicationData: {
          listingId: "507f1f77bcf86cd799439011",
          listingUrl: "https://example.com/listing/507f1f77bcf86cd799439011",
          message: appResponse.data.data.message,
          subject: appResponse.data.data.subject,
          propertyTitle: "Test Property for Debug",
        },
      };

      console.log(
        "📤 Sending submission data:",
        JSON.stringify(submissionData, null, 2)
      );

      const submitResponse = await axios.post(
        "http://localhost:3000/api/ai/application/submit",
        submissionData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      console.log(
        "✅ Submission response:",
        JSON.stringify(submitResponse.data, null, 2)
      );
    } else {
      console.error("❌ Application generation failed");
      console.log(JSON.stringify(appResponse.data, null, 2));
    }
  } catch (error) {
    console.error("❌ Test failed:", error.response?.data || error.message);
    if (error.response?.data) {
      console.log(
        "📋 Full error response:",
        JSON.stringify(error.response.data, null, 2)
      );
    }
  }
}

testApplicationSubmission();
