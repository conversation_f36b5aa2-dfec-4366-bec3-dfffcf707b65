#!/usr/bin/env node

const axios = require("axios");

// Test configuration
const baseURL = "http://localhost:3000";
const testUser = {
  email: "<EMAIL>",
  password: "Password123",
  firstName: "Wellish",
  lastName: "Ant",
};

let authToken = "";

// Helper function to log with timestamp
function log(message) {
  console.log(`[${new Date().toISOString()}] ${message}`);
}

// Step 1: Register a new test user
async function registerTestUser() {
  try {
    log("👤 Registering new test user...");

    const response = await axios.post(`${baseURL}/api/auth/register`, {
      email: testUser.email,
      password: testUser.password,
      firstName: testUser.firstName,
      lastName: testUser.lastName,
      confirmPassword: testUser.password,
    });

    if (response.data && response.data.status === "success") {
      log("✅ User registration successful");
      return true;
    } else {
      log("❌ User registration failed - unexpected response format");
      return false;
    }
  } catch (error) {
    if (
      error.response?.status === 400 &&
      error.response?.data?.message?.includes("already exists")
    ) {
      log("✅ User already exists - proceeding to login");
      return true;
    }
    log(
      `❌ User registration failed: ${
        error.response?.data?.message || error.message
      }`
    );
    return false;
  }
}

// Step 2: Login the test user
async function loginTestUser() {
  try {
    log("🔐 Logging in test user...");

    const response = await axios.post(`${baseURL}/api/auth/login`, {
      email: testUser.email,
      password: testUser.password,
    });

    if (response.data && response.data.token) {
      authToken = response.data.token;
      log("✅ Login successful");
      log(`📋 User ID: ${response.data.user?._id || response.data.user?.id}`);
      return true;
    } else {
      log("❌ Login failed - no token in response");
      return false;
    }
  } catch (error) {
    log(`❌ Login failed: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

// Step 3: Generate an AI application
async function generateApplication() {
  try {
    log("🤖 Generating AI application...");

    const applicationRequest = {
      listing: {
        title: "Modern 2-Bedroom Apartment in Amsterdam",
        location: "Amsterdam",
        price: "1800",
      },
      userProfile: {
        name: `${testUser.firstName} ${testUser.lastName}`,
        income: 4000,
        occupation: "Software Developer",
      },
      template: "professional",
    };

    const response = await axios.post(
      `${baseURL}/api/ai/application/generate`,
      applicationRequest,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data && response.data.status === "success") {
      log("✅ Application generated successfully");
      log(`📋 Subject: ${response.data.data.subject}`);
      log(`📋 Template: ${response.data.data.template}`);
      return {
        success: true,
        application: response.data.data,
        applicationId: `app_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`,
      };
    } else {
      log("❌ Application generation failed");
      return { success: false };
    }
  } catch (error) {
    log(
      `❌ Application generation failed: ${
        error.response?.data?.error || error.message
      }`
    );
    return { success: false };
  }
}

// Step 4: Test autonomous application submission
async function testAutonomousSubmission(applicationId, generatedApp) {
  try {
    log("🚀 Testing AUTONOMOUS application submission...");

    const submissionData = {
      applicationId: applicationId,
      method: "autonomous", // This is the key part!
      applicationData: {
        listingId: `507f1f77bcf86cd799439011`, // Valid ObjectId format
        message: generatedApp.message,
        subject: generatedApp.subject,
        propertyTitle: "Modern 2-Bedroom Apartment in Amsterdam",
      },
    };

    log("📤 Submitting with autonomous method...");
    const response = await axios.post(
      `${baseURL}/api/ai/application/submit`,
      submissionData,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        timeout: 15000,
      }
    );

    if (response.data && response.data.success) {
      log("✅ AUTONOMOUS submission successful!");
      log(`📋 Submission ID: ${response.data.data?.submissionId}`);
      log(`📋 Application DB ID: ${response.data.data?.applicationDbId}`);
      log(`📋 Queue Item ID: ${response.data.data?.queueItemId}`);
      log(`📋 Method: ${response.data.data?.method}`);
      log(`📋 Message: ${response.data.data?.message}`);

      if (response.data.data?.queueItemId) {
        log("🎯 SUCCESS: Application was added to the autonomous queue!");
        return {
          success: true,
          queueItemId: response.data.data.queueItemId,
          submissionId: response.data.data.submissionId,
        };
      } else {
        log("⚠️  Application submitted but NOT added to autonomous queue");
        return { success: false, reason: "Not added to queue" };
      }
    } else {
      log("❌ Autonomous submission failed - response not successful");
      log("📋 Response:", JSON.stringify(response.data, null, 2));
      return { success: false };
    }
  } catch (error) {
    log(
      `❌ Autonomous submission failed: ${
        error.response?.data?.error || error.message
      }`
    );
    if (error.response?.data) {
      log(
        "📋 Full error response:",
        JSON.stringify(error.response.data, null, 2)
      );
    }
    return { success: false, error: error.message };
  }
}

// Step 5: Test manual application submission (for comparison)
async function testManualSubmission(applicationId, generatedApp) {
  try {
    log("📝 Testing MANUAL application submission (for comparison)...");

    const submissionData = {
      applicationId: `${applicationId}_manual`,
      method: "manual", // Manual method
      applicationData: {
        listingId: `507f1f77bcf86cd799439012`, // Valid ObjectId format for manual test
        message: generatedApp.message,
        subject: generatedApp.subject,
        propertyTitle: "Test Property for Manual Application",
      },
    };

    const response = await axios.post(
      `${baseURL}/api/ai/application/submit`,
      submissionData,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data && response.data.success) {
      log("✅ Manual submission successful");
      log(
        `📋 Queue Item ID: ${
          response.data.data?.queueItemId || "N/A (expected for manual)"
        }`
      );

      if (!response.data.data?.queueItemId) {
        log("✅ Manual application correctly NOT added to queue");
        return { success: true, addedToQueue: false };
      } else {
        log("⚠️  Manual application was unexpectedly added to queue");
        return { success: true, addedToQueue: true };
      }
    } else {
      log("❌ Manual submission failed");
      return { success: false };
    }
  } catch (error) {
    log(`❌ Manual submission failed: ${error.message}`);
    return { success: false };
  }
}

// Step 6: Check the queue
async function checkQueue() {
  try {
    log("📋 Checking auto-application queue...");

    const response = await axios.get(`${baseURL}/api/auto-application/queue`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (response.data && response.data.status === "success") {
      const queue = response.data.data || [];
      log(`✅ Queue check successful - ${queue.length} items in queue`);

      if (queue.length > 0) {
        log("📋 Recent queue items:");
        queue.slice(-3).forEach((item, index) => {
          log(
            `   ${index + 1}. ID: ${item._id} | Title: ${
              item.listingTitle
            } | Status: ${item.status}`
          );
        });
      }

      return { success: true, queueCount: queue.length, items: queue };
    } else {
      log("❌ Queue check failed");
      return { success: false };
    }
  } catch (error) {
    log(
      `❌ Queue check failed: ${error.response?.data?.message || error.message}`
    );
    return { success: false };
  }
}

// Main test runner
async function runE2ETest() {
  console.log("🧪 END-TO-END AUTONOMOUS APPLICATION TEST");
  console.log("=".repeat(60));

  // Step 1: Register user
  const registerSuccess = await registerTestUser();
  if (!registerSuccess) {
    log("❌ Cannot continue without user registration");
    return;
  }

  console.log("\n" + "-".repeat(40));

  // Step 2: Login
  const loginSuccess = await loginTestUser();
  if (!loginSuccess) {
    log("❌ Cannot continue without authentication");
    return;
  }

  console.log("\n" + "-".repeat(40));

  // Step 3: Generate application
  const appResult = await generateApplication();
  if (!appResult.success) {
    log("❌ Cannot continue without generated application");
    return;
  }

  console.log("\n" + "-".repeat(40));

  // Step 4: Check initial queue state
  const initialQueue = await checkQueue();
  const initialCount = initialQueue.queueCount || 0;
  log(`📊 Initial queue count: ${initialCount}`);

  console.log("\n" + "-".repeat(40));

  // Step 5: Test autonomous submission
  const autonomousResult = await testAutonomousSubmission(
    appResult.applicationId,
    appResult.application
  );

  console.log("\n" + "-".repeat(40));

  // Step 6: Check queue after autonomous submission
  if (autonomousResult.success) {
    log("⏳ Waiting 2 seconds for queue update...");
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const postAutonomousQueue = await checkQueue();
    const newCount = postAutonomousQueue.queueCount || 0;

    if (newCount > initialCount) {
      log(
        `🎯 SUCCESS! Queue count increased from ${initialCount} to ${newCount}`
      );
      log("✅ Autonomous application was successfully added to the queue!");
    } else {
      log(`⚠️  Queue count unchanged (${initialCount} -> ${newCount})`);
    }
  }

  console.log("\n" + "-".repeat(40));

  // Step 7: Test manual submission for comparison
  const manualResult = await testManualSubmission(
    appResult.applicationId,
    appResult.application
  );

  console.log("\n" + "-".repeat(40));

  // Step 8: Final queue check
  const finalQueue = await checkQueue();
  const finalCount = finalQueue.queueCount || 0;

  // Final results
  console.log("\n" + "=".repeat(60));
  console.log("📊 FINAL TEST RESULTS:");
  console.log(
    `   User Registration: ${registerSuccess ? "SUCCESS" : "FAILED"}`
  );
  console.log(`   User Login: ${loginSuccess ? "SUCCESS" : "FAILED"}`);
  console.log(
    `   Application Generation: ${appResult.success ? "SUCCESS" : "FAILED"}`
  );
  console.log(
    `   Autonomous Submission: ${
      autonomousResult.success ? "SUCCESS" : "FAILED"
    }`
  );
  console.log(
    `   Manual Submission: ${manualResult.success ? "SUCCESS" : "FAILED"}`
  );
  console.log(`   Initial Queue Count: ${initialCount}`);
  console.log(`   Final Queue Count: ${finalCount}`);

  if (autonomousResult.success && autonomousResult.queueItemId) {
    console.log("\n🎉 AUTONOMOUS APPLICATION QUEUE IS WORKING!");
    console.log("✅ End-to-end test PASSED");
    console.log("   - User can register and login");
    console.log("   - AI can generate applications");
    console.log("   - Autonomous submissions are added to queue");
    console.log("   - Manual submissions are not added to queue");
    console.log("\n💡 The feature is working correctly!");
  } else {
    console.log("\n❌ AUTONOMOUS APPLICATION TEST FAILED");
    console.log("🔍 Issues detected:");
    if (!autonomousResult.success) {
      console.log("   - Autonomous submission failed");
    }
    if (autonomousResult.success && !autonomousResult.queueItemId) {
      console.log(
        "   - Autonomous submission succeeded but not added to queue"
      );
    }
    console.log("\n🛠️  Check backend logs for more details");
  }

  console.log("\n" + "=".repeat(60));
}

// Run the comprehensive test
runE2ETest().catch((error) => {
  console.error("❌ E2E Test failed:", error);
  process.exit(1);
});
