// Test frontend API service directly
const axios = require('axios');

// Simulate the frontend API service configuration
const apiConfig = {
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

async function testFrontendAPI() {
  console.log('=== TESTING FRONTEND API SERVICE ===\n');
  
  // Create axios client similar to frontend
  const client = axios.create(apiConfig);
  
  // Add request interceptor (similar to frontend)
  client.interceptors.request.use(
    async (config) => {
      // Mock token for testing
      const token = 'mock-test-token';
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      console.log(`Making request to: ${config.baseURL}${config.url}`);
      console.log('Headers:', config.headers);
      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );
  
  // Add response interceptor (similar to frontend)
  client.interceptors.response.use(
    (response) => {
      console.log('Response received:', response.status);
      return response;
    },
    async (error) => {
      console.error('Response interceptor error:', error.message);
      
      if (error.code === 'ECONNREFUSED') {
        console.error('Connection refused - backend not running?');
      } else if (error.code === 'ETIMEDOUT') {
        console.error('Request timeout');
      } else if (error.code === 'ENOTFOUND') {
        console.error('DNS resolution failed');
      }
      
      return Promise.reject({
        code: 'NETWORK_ERROR',
        message: 'Network error. Please check your connection.',
        status: 0,
        originalError: error
      });
    }
  );
  
  // Test property data
  const propertyData = {
    title: 'Test Property API',
    description: 'Testing API service directly',
    address: {
      street: 'Test Street',
      houseNumber: '123',
      postalCode: '1234AB',
      city: 'Amsterdam'
    },
    propertyType: 'apartment',
    rent: {
      amount: 1500
    }
  };
  
  console.log('1. Testing health endpoint...');
  try {
    const healthResponse = await client.get('/health');
    console.log('✅ Health check successful:', healthResponse.status);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    console.log('Error details:', error);
    return;
  }
  
  console.log('\n2. Testing property owner properties endpoint...');
  try {
    const response = await client.post('/property-owner/properties', propertyData);
    console.log('✅ Request successful:', response.status);
    console.log('Response data:', response.data);
  } catch (error) {
    console.log('❌ Request failed:', error.message);
    console.log('Error code:', error.code);
    console.log('Error status:', error.status);
    
    if (error.originalError) {
      console.log('Original axios error:', error.originalError.message);
      console.log('Original error code:', error.originalError.code);
      
      if (error.originalError.response) {
        console.log('Response status:', error.originalError.response.status);
        console.log('Response data:', error.originalError.response.data);
      }
    }
  }
  
  console.log('\n3. Testing with direct fetch (no interceptors)...');
  try {
    const directResponse = await fetch('http://localhost:3000/api/property-owner/properties', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-test-token'
      },
      body: JSON.stringify(propertyData)
    });
    
    console.log('✅ Direct fetch successful:', directResponse.status);
    const responseText = await directResponse.text();
    console.log('Response:', responseText);
    
  } catch (error) {
    console.log('❌ Direct fetch failed:', error.message);
  }
  
  console.log('\n=== DIAGNOSIS ===');
  console.log('If direct fetch works but axios fails:');
  console.log('- Issue is with axios configuration or interceptors');
  console.log('- Check timeout settings');
  console.log('- Check request/response interceptors');
  console.log('\nIf both fail:');
  console.log('- Backend connection issue');
  console.log('- CORS configuration problem');
  console.log('- Network/firewall issue');
}

testFrontendAPI().catch(console.error);