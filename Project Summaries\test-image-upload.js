/**
 * Test script for property image upload functionality
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3000/api';

// Test configuration
const TEST_CONFIG = {
  // You'll need to replace this with a valid JWT token
  authToken: 'your-jwt-token-here',
  propertyId: 'new', // Use 'new' for new properties or actual property ID
  testImagePath: path.join(__dirname, 'test-image.jpg') // You'll need to create this
};

async function testImageUpload() {
  console.log('🧪 Testing Property Image Upload...\n');

  try {
    // Check if test image exists
    if (!fs.existsSync(TEST_CONFIG.testImagePath)) {
      console.log('❌ Test image not found. Creating a placeholder...');
      
      // Create a simple test image (1x1 pixel PNG)
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);
      
      fs.writeFileSync(TEST_CONFIG.testImagePath.replace('.jpg', '.png'), testImageBuffer);
      TEST_CONFIG.testImagePath = TEST_CONFIG.testImagePath.replace('.jpg', '.png');
      console.log('✅ Created test image:', TEST_CONFIG.testImagePath);
    }

    // Create form data
    const formData = new FormData();
    const imageStream = fs.createReadStream(TEST_CONFIG.testImagePath);
    formData.append('images', imageStream, {
      filename: 'test-property-image.png',
      contentType: 'image/png'
    });

    console.log('📤 Uploading image...');
    console.log('Endpoint:', `${API_BASE_URL}/property-owner/properties/${TEST_CONFIG.propertyId}/images`);

    // Make the upload request
    const response = await fetch(
      `${API_BASE_URL}/property-owner/properties/${TEST_CONFIG.propertyId}/images`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_CONFIG.authToken}`,
          ...formData.getHeaders()
        },
        body: formData
      }
    );

    console.log('Response Status:', response.status);
    console.log('Response Headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response Body:', responseText);

    if (response.ok) {
      const result = JSON.parse(responseText);
      console.log('\n✅ Image Upload Successful!');
      console.log('📊 Upload Results:');
      console.log(`   - Status: ${result.status}`);
      console.log(`   - Message: ${result.message}`);
      console.log(`   - Images uploaded: ${result.data.images.length}`);
      
      result.data.images.forEach((img, index) => {
        console.log(`   - Image ${index + 1}:`);
        console.log(`     * URL: ${img.url}`);
        console.log(`     * Filename: ${img.filename}`);
        console.log(`     * Size: ${img.size} bytes`);
      });

      // Test accessing the uploaded image
      console.log('\n🔍 Testing image accessibility...');
      const imageUrl = `http://localhost:3000${result.data.images[0].url}`;
      const imageResponse = await fetch(imageUrl);
      
      if (imageResponse.ok) {
        console.log('✅ Image is accessible at:', imageUrl);
      } else {
        console.log('❌ Image not accessible:', imageResponse.status);
      }

    } else {
      console.log('\n❌ Image Upload Failed!');
      console.log('Error Response:', responseText);
      
      if (response.status === 401) {
        console.log('\n💡 Tip: Make sure to set a valid JWT token in TEST_CONFIG.authToken');
      } else if (response.status === 413) {
        console.log('\n💡 Tip: Image file is too large (max 10MB)');
      }
    }

  } catch (error) {
    console.error('\n💥 Test Error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Tip: Make sure the backend server is running on http://localhost:3000');
    }
  }
}

async function testDirectoryStructure() {
  console.log('\n📁 Checking upload directory structure...');
  
  const uploadDir = path.join(__dirname, 'zakmakelaar-backend', 'uploads', 'property-images');
  
  if (fs.existsSync(uploadDir)) {
    console.log('✅ Upload directory exists:', uploadDir);
    
    const files = fs.readdirSync(uploadDir);
    console.log(`📄 Files in upload directory: ${files.length}`);
    
    files.forEach(file => {
      const filePath = path.join(uploadDir, file);
      const stats = fs.statSync(filePath);
      console.log(`   - ${file} (${stats.size} bytes, ${stats.mtime.toISOString()})`);
    });
  } else {
    console.log('❌ Upload directory does not exist:', uploadDir);
    console.log('💡 The directory will be created automatically on first upload');
  }
}

// Main test execution
async function runTests() {
  console.log('🚀 Property Image Upload Test Suite');
  console.log('=====================================\n');

  await testDirectoryStructure();
  await testImageUpload();

  console.log('\n🏁 Test Complete!');
  console.log('\n📝 Next Steps:');
  console.log('1. Update TEST_CONFIG.authToken with a valid JWT token');
  console.log('2. Test with actual property images');
  console.log('3. Test the frontend image upload flow');
  console.log('4. Verify images appear in property listings');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testImageUpload,
  testDirectoryStructure,
  runTests
};