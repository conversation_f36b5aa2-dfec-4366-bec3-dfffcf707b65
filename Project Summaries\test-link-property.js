/**
 * Script: Link Existing Property to Current User
 * 
 * This script updates the existing property (68a9b3262e90c2ad7de52303) 
 * to be owned by the current logged-in user so the real application will appear
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const TARGET_PROPERTY_ID = '68a9b3262e90c2ad7de52303';

class PropertyLinkTest {
  constructor() {
    this.token = null;
    this.userId = null;
  }

  async apiRequest(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${API_BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...(this.token && { 'Authorization': `Bearer ${this.token}` })
        },
        ...(data && { data })
      };

      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`❌ ${method.toUpperCase()} ${endpoint} failed:`, error.response?.data?.message || error.message);
      throw error;
    }
  }

  async login() {
    console.log('🔐 Logging in as property owner...');
    const response = await this.apiRequest('POST', '/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    this.token = response.token || response.data?.token;
    this.userId = response.user?.id || response.data?.user?.id;
    console.log('✅ Logged in successfully');
    console.log(`👤 User ID: ${this.userId}`);
  }

  async updatePropertyOwnership() {
    console.log('\n🔗 === LINKING PROPERTY TO USER ===');
    console.log(`🎯 Target Property: ${TARGET_PROPERTY_ID}`);
    console.log(`👤 New Owner: ${this.userId}`);
    
    // Since we don't have a direct "change ownership" endpoint,
    // we'll need to update the property through the property update endpoint
    // or directly in the database
    
    console.log('💡 To link the property, we need to update the property document in MongoDB');
    console.log('💡 This would typically be done through an admin interface or database update');
    
    // Let's try to update the property if we can access it
    try {
      console.log('🔄 Attempting to update property ownership...');
      
      // First, let's see if we can get the property details
      const propertyResponse = await this.apiRequest('GET', `/property-owner/properties/${TARGET_PROPERTY_ID}`);
      console.log('📋 Property found:', JSON.stringify(propertyResponse, null, 2));
      
    } catch (error) {
      console.log('❌ Cannot access property - it\'s not owned by current user');
      console.log('💡 This confirms the ownership issue');
    }
  }

  async createDirectDatabaseUpdate() {
    console.log('\n🗄️  === DATABASE UPDATE NEEDED ===');
    
    console.log('📝 To fix this issue, you need to update the property in MongoDB:');
    console.log('');
    console.log('🔧 MongoDB Update Command:');
    console.log('```javascript');
    console.log('db.properties.updateOne(');
    console.log(`  { _id: ObjectId("${TARGET_PROPERTY_ID}") },`);
    console.log('  { $set: { ');
    console.log(`    "owner.userId": ObjectId("${this.userId}")`);
    console.log('  } }');
    console.log(')');
    console.log('```');
    console.log('');
    console.log('🔧 Alternative: MongoDB Compass Update:');
    console.log('1. Open MongoDB Compass');
    console.log('2. Connect to your database');
    console.log('3. Navigate to the "properties" collection');
    console.log(`4. Find the property with _id: ${TARGET_PROPERTY_ID}`);
    console.log('5. Edit the document');
    console.log(`6. Change owner.userId to: ${this.userId}`);
    console.log('7. Save the changes');
  }

  async testAfterUpdate() {
    console.log('\n🧪 === TESTING AFTER UPDATE ===');
    
    console.log('📊 Checking applications after property ownership update...');
    try {
      const response = await this.apiRequest('GET', '/property-owner/applications');
      const applications = response.data || [];
      
      console.log(`📋 Applications found: ${applications.length}`);
      
      if (applications.length > 0) {
        console.log('\n🎉 SUCCESS! Applications are now visible:');
        applications.forEach((app, index) => {
          console.log(`\n   ${index + 1}. ${app.applicantName} (${app._id})`);
          console.log(`      📧 ${app.applicantEmail}`);
          console.log(`      🏠 ${app.propertyAddress}`);
          console.log(`      📊 Status: ${app.status}`);
        });
        
        // Look for the specific real application
        const realApp = applications.find(app => app.applicantEmail === '<EMAIL>');
        if (realApp) {
          console.log('\n🎊 REAL APPLICATION FOUND!');
          console.log(`👤 Applicant: ${realApp.applicantName}`);
          console.log(`📧 Email: ${realApp.applicantEmail}`);
          console.log(`🏠 Property: ${realApp.propertyAddress}`);
          console.log(`📊 Status: ${realApp.status}`);
        }
      } else {
        console.log('⚠️  Still no applications - property ownership may not be updated yet');
      }
    } catch (error) {
      console.log('❌ Failed to test applications:', error.message);
    }
  }

  async runLinkTest() {
    console.log('🔗 === PROPERTY LINKING TEST ===');
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    
    try {
      // Step 1: Login
      await this.login();
      
      // Step 2: Try to update property ownership
      await this.updatePropertyOwnership();
      
      // Step 3: Show database update instructions
      await this.createDirectDatabaseUpdate();
      
      // Step 4: Test applications (will work after manual DB update)
      await this.testAfterUpdate();
      
      // Summary
      console.log('\n🎉 === SUMMARY ===');
      console.log('✅ Identified the root cause: Property ownership mismatch');
      console.log('📝 Provided MongoDB update commands to fix the issue');
      console.log('🔄 After updating the database, the real application will appear');
      
      console.log('\n🚀 NEXT STEPS:');
      console.log('1. 🗄️  Update the property ownership in MongoDB (commands shown above)');
      console.log('2. 🔄 Run the test again to verify applications appear');
      console.log('3. 📱 Open the mobile app to see the real application');
      console.log('4. ✅ Test approve/reject functionality');
      
    } catch (error) {
      console.error('\n💥 TEST FAILED:', error.message);
    }
    
    console.log(`\n📅 Completed at: ${new Date().toISOString()}`);
  }
}

// Run the test
if (require.main === module) {
  const test = new PropertyLinkTest();
  test.runLinkTest().catch(console.error);
}

module.exports = PropertyLinkTest;