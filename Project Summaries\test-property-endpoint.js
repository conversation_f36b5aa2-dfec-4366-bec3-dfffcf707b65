// Test the exact property owner endpoint
async function testPropertyEndpoint() {
  console.log('=== TESTING PROPERTY OWNER ENDPOINT ===\n');
  
  const url = 'http://localhost:3000/api/property-owner/properties';
  const testData = {
    title: 'Test Property',
    description: 'Test description',
    address: {
      street: 'Test Street',
      houseNumber: '123',
      postalCode: '1234AB',
      city: 'Amsterdam'
    },
    propertyType: 'apartment',
    rent: {
      amount: 1500
    }
  };
  
  console.log('Testing POST to:', url);
  console.log('Data:', JSON.stringify(testData, null, 2));
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify(testData)
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    if (response.status === 401) {
      console.log('✅ Endpoint is working (401 = auth required, expected with mock token)');
    } else if (response.status === 400) {
      console.log('✅ Endpoint is working (400 = validation error, expected)');
    } else if (response.status >= 200 && response.status < 300) {
      console.log('✅ Endpoint is working (success)');
    } else {
      console.log('⚠️ Unexpected status but endpoint is accessible');
    }
    
  } catch (error) {
    console.log('❌ Network error:', error.message);
    console.log('Error details:', error);
  }
}

testPropertyEndpoint();