// Test all property management routes
async function testPropertyRoutes() {
  console.log('=== TESTING PROPERTY MANAGEMENT ROUTES ===\n');
  
  const baseUrl = 'http://localhost:3000/api/property-owner';
  const mockToken = 'Bearer mock-token';
  
  const routes = [
    // Profile & Registration
    { method: 'GET', path: '/profile', description: 'Get property owner profile' },
    { method: 'PUT', path: '/profile', description: 'Update property owner profile', body: { companyName: 'Test Company' } },
    { method: 'POST', path: '/register', description: 'Register as property owner', body: { businessRegistration: '12345678' } },
    { method: 'GET', path: '/verification-status', description: 'Get verification status' },
    
    // Dashboard & Statistics
    { method: 'GET', path: '/dashboard', description: 'Get property owner dashboard' },
    { method: 'GET', path: '/statistics', description: 'Get property owner statistics' },
    
    // Property Management
    { method: 'GET', path: '/properties', description: 'List all properties' },
    { method: 'POST', path: '/properties', description: 'Add new property', body: {
      title: 'Test Property',
      description: 'Test description',
      address: {
        street: 'Test Street',
        houseNumber: '123',
        postalCode: '1234AB',
        city: 'Amsterdam'
      },
      propertyType: 'apartment',
      rent: { amount: 1500 }
    }},
    { method: 'GET', path: '/properties/507f1f77bcf86cd799439011', description: 'Get property details' },
    { method: 'PUT', path: '/properties/507f1f77bcf86cd799439011', description: 'Update property', body: { title: 'Updated Property' } },
    { method: 'DELETE', path: '/properties/507f1f77bcf86cd799439011', description: 'Delete property' },
    
    // Property Status
    { method: 'PUT', path: '/properties/507f1f77bcf86cd799439011/activate', description: 'Activate property' },
    { method: 'PUT', path: '/properties/507f1f77bcf86cd799439011/deactivate', description: 'Deactivate property' },
    
    // Applications & Screening
    { method: 'GET', path: '/properties/507f1f77bcf86cd799439011/applications', description: 'Get property applications' },
    { method: 'POST', path: '/screen-tenants/507f1f77bcf86cd799439011', description: 'Screen tenants', body: { applicationIds: [] } },
    { method: 'POST', path: '/rank-applicants/507f1f77bcf86cd799439011', description: 'Rank applicants', body: {} },
    { method: 'PUT', path: '/applications/507f1f77bcf86cd799439011/status', description: 'Update application status', body: { status: 'approved' } },
    
    // Reporting
    { method: 'GET', path: '/properties/507f1f77bcf86cd799439011/report?type=comprehensive', description: 'Generate property report' },
  ];
  
  const results = {
    total: routes.length,
    accessible: 0,
    working: 0,
    errors: 0,
    details: []
  };
  
  for (const route of routes) {
    try {
      console.log(`Testing: ${route.method} ${route.path}`);
      console.log(`Description: ${route.description}`);
      
      const options = {
        method: route.method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': mockToken
        }
      };
      
      if (route.body) {
        options.body = JSON.stringify(route.body);
      }
      
      const response = await fetch(`${baseUrl}${route.path}`, options);
      const status = response.status;
      
      let result = {
        route: `${route.method} ${route.path}`,
        description: route.description,
        status: status,
        accessible: false,
        working: false,
        error: null
      };
      
      if (status === 401) {
        result.accessible = true;
        result.note = 'Auth required (expected)';
        results.accessible++;
        console.log('✅ Route accessible (401 = auth required)');
      } else if (status >= 200 && status < 300) {
        result.accessible = true;
        result.working = true;
        results.accessible++;
        results.working++;
        console.log('✅ Route working');
      } else if (status === 400) {
        result.accessible = true;
        result.note = 'Validation error (expected with mock data)';
        results.accessible++;
        console.log('✅ Route accessible (400 = validation error)');
      } else if (status === 403) {
        result.accessible = true;
        result.note = 'Forbidden (may need property owner role)';
        results.accessible++;
        console.log('⚠️ Route accessible but forbidden');
      } else if (status === 404) {
        result.accessible = true;
        result.note = 'Not found (expected for non-existent resources)';
        results.accessible++;
        console.log('⚠️ Route accessible but resource not found');
      } else {
        result.error = `Unexpected status: ${status}`;
        results.errors++;
        console.log(`❌ Unexpected status: ${status}`);
      }
      
      results.details.push(result);
      
    } catch (error) {
      console.log(`❌ Network error: ${error.message}`);
      results.errors++;
      results.details.push({
        route: `${route.method} ${route.path}`,
        description: route.description,
        error: error.message,
        accessible: false,
        working: false
      });
    }
    
    console.log(''); // Empty line for readability
  }
  
  // Summary
  console.log('=== SUMMARY ===');
  console.log(`Total routes tested: ${results.total}`);
  console.log(`Accessible routes: ${results.accessible}/${results.total} (${Math.round(results.accessible/results.total*100)}%)`);
  console.log(`Working routes: ${results.working}/${results.total} (${Math.round(results.working/results.total*100)}%)`);
  console.log(`Errors: ${results.errors}`);
  
  if (results.accessible >= results.total * 0.8) {
    console.log('\n✅ PROPERTY MANAGEMENT ROUTES ARE WELL IMPLEMENTED!');
  } else {
    console.log('\n⚠️ Some routes may need attention');
  }
  
  // Detailed results
  console.log('\n=== DETAILED RESULTS ===');
  results.details.forEach(detail => {
    const status = detail.working ? '✅ WORKING' : 
                   detail.accessible ? '⚠️ ACCESSIBLE' : '❌ ERROR';
    console.log(`${status} - ${detail.route}`);
    if (detail.note) console.log(`   Note: ${detail.note}`);
    if (detail.error) console.log(`   Error: ${detail.error}`);
  });
}

// Run the test
testPropertyRoutes().catch(console.error);