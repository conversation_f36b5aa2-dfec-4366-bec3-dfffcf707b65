#!/usr/bin/env node

const axios = require("axios");

async function testQueueEndpoint() {
  try {
    console.log("🔐 Logging in...");
    // First login to get token
    const loginResponse = await axios.post(
      "http://localhost:3000/api/auth/login",
      {
        email: "<EMAIL>",
        password: "Password123",
      }
    );

    const token = loginResponse.data.token;
    console.log("✅ Login successful");

    // Test queue endpoint directly
    const queueData = {
      listingId: "test_listing_123",
      listingUrl: "https://example.com/listing/test_listing_123",
      listingTitle: "Test Property",
      priority: 5,
    };

    console.log("🧪 Testing queue endpoint with data:", queueData);

    const queueResponse = await axios.post(
      "http://localhost:3000/api/auto-application/queue",
      queueData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("✅ Queue endpoint successful!");
    console.log("📋 Response:", JSON.stringify(queueResponse.data, null, 2));
  } catch (error) {
    console.error(
      "❌ Queue test failed:",
      error.response?.data || error.message
    );
    console.error("📋 Status:", error.response?.status);
    if (error.response?.data?.errors) {
      console.error("📋 Validation errors:", error.response.data.errors);
    }
  }
}

testQueueEndpoint();
