/**
 * Test Script: Verify Real Database Applications
 * 
 * This script tests that the backend is now fetching real applications
 * from the database instead of mock data
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Expected real application data from the database
const EXPECTED_APPLICATION = {
  _id: '68a9d0b27cc6c69855b7b593',
  applicantEmail: '<EMAIL>',
  propertyId: '68a9b3262e90c2ad7de52303',
  status: 'submitted'
};

class RealDatabaseTest {
  constructor() {
    this.token = null;
  }

  async apiRequest(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${API_BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...(this.token && { 'Authorization': `Bearer ${this.token}` })
        },
        ...(data && { data })
      };

      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`❌ ${method.toUpperCase()} ${endpoint} failed:`, error.response?.data?.message || error.message);
      throw error;
    }
  }

  async login() {
    console.log('🔐 Logging in as property owner...');
    const response = await this.apiRequest('POST', '/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    this.token = response.token || response.data?.token;
    console.log('✅ Logged in successfully');
  }

  async testRealApplications() {
    console.log('\n📋 === TESTING REAL DATABASE APPLICATIONS ===');
    
    console.log('📊 Fetching applications from database...');
    const response = await this.apiRequest('GET', '/property-owner/applications');
    const applications = response.data || [];
    
    console.log(`📋 Total applications found: ${applications.length}`);
    
    if (applications.length === 0) {
      console.log('❌ No applications found - database might be empty or query failed');
      return false;
    }

    console.log('\n📋 All Applications:');
    applications.forEach((app, index) => {
      console.log(`\n   ${index + 1}. Application ID: ${app._id}`);
      console.log(`      👤 Name: ${app.applicantName}`);
      console.log(`      📧 Email: ${app.applicantEmail}`);
      console.log(`      📱 Phone: ${app.applicantPhone}`);
      console.log(`      🏠 Property: ${app.propertyAddress}`);
      console.log(`      🏠 Property ID: ${app.propertyId}`);
      console.log(`      📅 Applied: ${app.applicationDate}`);
      console.log(`      📊 Status: ${app.status}`);
      console.log(`      💳 Credit Score: ${app.creditScore}`);
      console.log(`      ✅ Income Verified: ${app.incomeVerified}`);
      console.log(`      🔍 Background Check: ${app.backgroundCheckPassed}`);
      console.log(`      📄 Documents: ${app.documents?.length || 0} files`);
      
      if (app.personalMessage) {
        console.log(`      💬 Message: ${app.personalMessage.substring(0, 100)}...`);
      }
      if (app.monthlyIncome) {
        console.log(`      💰 Income: €${app.monthlyIncome}/month`);
      }
    });

    // Look for the specific real application
    const realApp = applications.find(app => 
      app._id === EXPECTED_APPLICATION._id ||
      app.applicantEmail === EXPECTED_APPLICATION.applicantEmail ||
      app.propertyId === EXPECTED_APPLICATION.propertyId
    );

    if (realApp) {
      console.log('\n🎉 ✅ REAL DATABASE APPLICATION FOUND!');
      console.log('📋 Real Application Details:');
      console.log(`   🆔 Database ID: ${realApp._id}`);
      console.log(`   👤 Name: ${realApp.applicantName}`);
      console.log(`   📧 Email: ${realApp.applicantEmail}`);
      console.log(`   🏠 Property ID: ${realApp.propertyId}`);
      console.log(`   📊 Status: ${realApp.status}`);
      console.log(`   📅 Applied: ${realApp.applicationDate}`);
      
      // Test status update on real application
      await this.testRealStatusUpdate(realApp._id);
      
      return true;
    } else {
      console.log('\n⚠️  Expected real application not found');
      console.log('🔍 Looking for:');
      console.log(`   - ID: ${EXPECTED_APPLICATION._id}`);
      console.log(`   - Email: ${EXPECTED_APPLICATION.applicantEmail}`);
      console.log(`   - Property ID: ${EXPECTED_APPLICATION.propertyId}`);
      return false;
    }
  }

  async testRealStatusUpdate(applicationId) {
    console.log(`\n🔄 === TESTING REAL STATUS UPDATE ===`);
    console.log(`🎯 Testing with real application: ${applicationId}`);
    
    try {
      console.log('🟢 Testing approval...');
      const approvalResponse = await this.apiRequest('PUT', `/property-owner/applications/${applicationId}/status`, {
        status: 'approved',
        notes: 'Approved real application from database - testing functionality'
      });
      
      console.log('✅ Real application approved successfully');
      console.log(`   Previous Status: ${approvalResponse.data.previousStatus}`);
      console.log(`   New Status: ${approvalResponse.data.newStatus}`);
      console.log(`   Updated At: ${approvalResponse.data.updatedAt}`);

      // Wait and reset to original status
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('🔄 Resetting to submitted...');
      await this.apiRequest('PUT', `/property-owner/applications/${applicationId}/status`, {
        status: 'under_review',
        notes: 'Reset to under_review after testing'
      });
      console.log('✅ Status reset successfully');

    } catch (error) {
      console.log('❌ Real status update failed:', error.message);
    }
  }

  async compareMockVsReal() {
    console.log('\n🔍 === COMPARING MOCK VS REAL DATA ===');
    
    const response = await this.apiRequest('GET', '/property-owner/applications');
    const applications = response.data || [];
    
    // Check if we're getting mock data or real data
    const mockIndicators = [
      'John Doe',
      'Jane Smith', 
      'Robert Johnson',
      'Sarah van der Berg',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    const hasMockData = applications.some(app => 
      mockIndicators.includes(app.applicantName) || 
      mockIndicators.includes(app.applicantEmail)
    );
    
    const hasRealData = applications.some(app => 
      app.applicantEmail === '<EMAIL>' ||
      app._id === '68a9d0b27cc6c69855b7b593'
    );
    
    console.log('📊 Data Analysis:');
    console.log(`   🎭 Mock Data Present: ${hasMockData ? 'Yes' : 'No'}`);
    console.log(`   🗄️  Real Data Present: ${hasRealData ? 'Yes' : 'No'}`);
    console.log(`   📋 Total Applications: ${applications.length}`);
    
    if (hasRealData && !hasMockData) {
      console.log('🎉 ✅ SUCCESS: Backend is using REAL database data!');
    } else if (hasRealData && hasMockData) {
      console.log('⚠️  MIXED: Backend has both real and mock data');
    } else if (!hasRealData && hasMockData) {
      console.log('❌ STILL MOCK: Backend is still using mock data only');
    } else {
      console.log('❓ UNKNOWN: No recognizable data patterns found');
    }
    
    return hasRealData;
  }

  async runTest() {
    console.log('🗄️  === REAL DATABASE TEST ===');
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    console.log(`🎯 Looking for real application: <EMAIL>`);
    console.log(`🏠 Property: Modern appartement 2 (68a9b3262e90c2ad7de52303)`);
    
    try {
      // Step 1: Login
      await this.login();
      
      // Step 2: Compare mock vs real data
      const hasRealData = await this.compareMockVsReal();
      
      // Step 3: Test real applications
      const realAppFound = await this.testRealApplications();
      
      // Summary
      console.log('\n🎉 === TEST RESULTS ===');
      console.log(`✅ Login: Success`);
      console.log(`${hasRealData ? '✅' : '❌'} Real Data: ${hasRealData ? 'Found' : 'Not Found'}`);
      console.log(`${realAppFound ? '✅' : '❌'} Real Application: ${realAppFound ? 'Found' : 'Not Found'}`);
      
      if (hasRealData && realAppFound) {
        console.log('\n🎊 SUCCESS! Backend is now using real database!');
        console.log('\n📱 Ready for mobile app testing:');
        console.log('1. Open the React Native app');
        console.log('2. Login as property owner (<EMAIL>)');
        console.log('3. Navigate to "Screening" tab');
        console.log('4. You should see the real <NAME_EMAIL>');
        console.log('5. Test approve/reject functionality');
        console.log('6. Verify status changes are saved to database');
      } else {
        console.log('\n⚠️  Backend may still be using mock data or database is empty');
        console.log('Please check:');
        console.log('1. Database connection is working');
        console.log('2. Application collection has data');
        console.log('3. Property ownership is correctly linked');
      }
      
    } catch (error) {
      console.error('\n💥 TEST FAILED:', error.message);
    }
    
    console.log(`\n📅 Completed at: ${new Date().toISOString()}`);
  }
}

// Run the test
if (require.main === module) {
  const test = new RealDatabaseTest();
  test.runTest().catch(console.error);
}

module.exports = RealDatabaseTest;