const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/scraper';

async function testScraperEnableDisable() {
    console.log('🧪 Testing Scraper Enable/Disable Functionality\n');

    try {
        // Test 1: Get current scraper status
        console.log('1. Getting current scraper status...');
        const statusResponse = await axios.get(`${BASE_URL}/status`);
        console.log('✅ Current status:', JSON.stringify(statusResponse.data, null, 2));
        console.log();

        // Test 2: Disable funda scraper
        console.log('2. Disabling funda scraper...');
        const disableResponse = await axios.post(`${BASE_URL}/disable/funda`);
        console.log('✅ Disable response:', JSON.stringify(disableResponse.data, null, 2));
        console.log();

        // Test 3: Check status after disabling
        console.log('3. Checking status after disabling funda...');
        const statusAfterDisable = await axios.get(`${BASE_URL}/status`);
        console.log('✅ Status after disable:', JSON.stringify(statusAfterDisable.data, null, 2));
        console.log();

        // Test 4: Try to disable already disabled scraper
        console.log('4. Trying to disable already disabled scraper...');
        try {
            await axios.post(`${BASE_URL}/disable/funda`);
        } catch (error) {
            console.log('✅ Expected error for already disabled scraper:', error.response.data.message);
        }
        console.log();

        // Test 5: Enable funda scraper back
        console.log('5. Enabling funda scraper back...');
        const enableResponse = await axios.post(`${BASE_URL}/enable/funda`);
        console.log('✅ Enable response:', JSON.stringify(enableResponse.data, null, 2));
        console.log();

        // Test 6: Try to enable already enabled scraper
        console.log('6. Trying to enable already enabled scraper...');
        try {
            await axios.post(`${BASE_URL}/enable/funda`);
        } catch (error) {
            console.log('✅ Expected error for already enabled scraper:', error.response.data.message);
        }
        console.log();

        // Test 7: Test invalid scraper name
        console.log('7. Testing invalid scraper name...');
        try {
            await axios.post(`${BASE_URL}/enable/invalid`);
        } catch (error) {
            console.log('✅ Expected error for invalid scraper:', error.response.data.message);
        }
        console.log();

        // Test 8: Final status check
        console.log('8. Final status check...');
        const finalStatus = await axios.get(`${BASE_URL}/status`);
        console.log('✅ Final status:', JSON.stringify(finalStatus.data, null, 2));

        console.log('\n🎉 All tests completed successfully!');

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

// Run the test
testScraperEnableDisable();