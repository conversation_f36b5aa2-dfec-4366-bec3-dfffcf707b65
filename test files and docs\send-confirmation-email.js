require("dotenv").config();
const mongoose = require("mongoose");
const User = require("./src/models/User");
const ApplicationResult = require("./src/models/ApplicationResult");
const config = require("./src/config/config");

async function sendConfirmationEmail() {
  try {
    await mongoose.connect(config.mongoURI);
    console.log("✅ Connected to database");

    // Find the ApplicationResult we just created
    const applicationResult = await ApplicationResult.findOne({})
      .populate("userId", "email name")
      .sort({ createdAt: -1 });

    if (!applicationResult) {
      console.log("❌ No application result found");
      return;
    }

    console.log(
      `\n📧 Sending confirmation email to: ${applicationResult.userId.email}`
    );
    console.log(`   Application ID: ${applicationResult._id}`);
    console.log(`   Status: ${applicationResult.status}`);
    console.log(
      `   Listing URL: ${applicationResult.listingSnapshot?.url || "N/A"}`
    );

    // Initialize SendGrid
    if (
      !process.env.SENDGRID_API_KEY ||
      !process.env.SENDGRID_API_KEY.startsWith("SG.")
    ) {
      console.log("❌ SendGrid API key not found in environment variables");
      console.log("📧 Email notification simulation:");
      console.log("-----------------------------------");
      console.log(`To: ${applicationResult.userId.email}`);
      console.log(`Subject: ✅ Your property application has been submitted!`);
      console.log(`
Dear ${applicationResult.userId.name || "User"},

Great news! Your application has been successfully submitted for:
📍 Property URL: ${
        applicationResult.listingSnapshot?.url ||
        "https://www.funda.nl/detail/huur/utrecht/appartement-eendrachtlaan-46-d/43728488/"
      }

📋 Application Details:
• Application ID: ${applicationResult._id}
• Submitted: ${applicationResult.submittedAt.toLocaleString()}
• Status: Successfully submitted
• Confirmation Number: ${applicationResult._id.toString().slice(-8)}

🔍 What happens next?
1. Your application is now with the landlord/property manager
2. They will review your application along with others
3. You may receive a response within 1-7 business days
4. If selected, you'll be contacted for next steps (viewing, documents, etc.)

📱 You can track the status of this and other applications in your ZakMakelaar dashboard.

Best of luck with your application!

ZakMakelaar Team
`);
      console.log("-----------------------------------");

      // Update the notification status in the database
      applicationResult.notifications = applicationResult.notifications || {};
      applicationResult.notifications.emailSent = true;
      applicationResult.notifications.emailSentAt = new Date();
      applicationResult.notifications.emailMethod = "simulated";
      await applicationResult.save();

      console.log("✅ Email notification simulated and database updated");
      return;
    }

    const sgMail = require("@sendgrid/mail");
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);

    const msg = {
      to: applicationResult.userId.email,
      from: process.env.SENDGRID_FROM_EMAIL || "<EMAIL>",
      subject: "✅ Your property application has been submitted!",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2196F3;">Application Submitted Successfully! 🎉</h2>
          
          <p>Dear ${applicationResult.userId.name || "User"},</p>
          
          <p>Great news! Your application has been successfully submitted for:</p>
          <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <strong>📍 Property:</strong> <a href="${
              applicationResult.listingSnapshot?.url || "#"
            }" style="color: #2196F3;">View Property</a><br>
          </div>
          
          <h3>📋 Application Details:</h3>
          <ul>
            <li><strong>Application ID:</strong> ${applicationResult._id}</li>
            <li><strong>Submitted:</strong> ${applicationResult.submittedAt.toLocaleString()}</li>
            <li><strong>Status:</strong> Successfully submitted</li>
            <li><strong>Confirmation Number:</strong> ${applicationResult._id
              .toString()
              .slice(-8)}</li>
          </ul>
          
          <h3>🔍 What happens next?</h3>
          <ol>
            <li>Your application is now with the landlord/property manager</li>
            <li>They will review your application along with others</li>
            <li>You may receive a response within 1-7 business days</li>
            <li>If selected, you'll be contacted for next steps (viewing, documents, etc.)</li>
          </ol>
          
          <p>📱 You can track the status of this and other applications in your ZakMakelaar dashboard.</p>
          
          <p style="color: #4CAF50; font-weight: bold;">Best of luck with your application!</p>
          
          <hr style="margin: 30px 0;">
          <p style="font-size: 12px; color: #666;">
            ZakMakelaar Team<br>
            This is an automated message from your property application system.
          </p>
        </div>
      `,
    };

    await sgMail.send(msg);

    // Update the notification status in the database
    applicationResult.notifications = applicationResult.notifications || {};
    applicationResult.notifications.emailSent = true;
    applicationResult.notifications.emailSentAt = new Date();
    applicationResult.notifications.emailMethod = "sendgrid";
    await applicationResult.save();

    console.log("✅ Email sent successfully via SendGrid!");
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    mongoose.disconnect();
  }
}

sendConfirmationEmail();
