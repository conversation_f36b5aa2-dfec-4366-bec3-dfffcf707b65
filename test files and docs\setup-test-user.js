/**
 * Setup script to create test user in Docker environment
 */

const mongoose = require("mongoose");
const bcrypt = require("bcrypt");
require("dotenv").config();

// Use Docker MongoDB connection
const MONGO_URI =
  process.env.MONGO_URI ||
  "**********************************************************************";

const TEST_USER = {
  email: "<EMAIL>",
  password: "Password123",
  firstName: "Wellis",
  lastName: "Hant",
  phone: "+31612345678",
};

async function setupTestUser() {
  try {
    console.log("🔌 Connecting to MongoDB...");
    await mongoose.connect(MONGO_URI);
    console.log("✅ Connected to MongoDB");

    // Import User model
    const User = require("./src/models/User");

    // Check if user already exists
    const existingUser = await User.findOne({ email: TEST_USER.email });
    if (existingUser) {
      console.log("✅ Test user already exists");

      // Update user settings for auto application
      const AutoApplicationSettings = require("./src/models/AutoApplicationSettings");
      let settings = await AutoApplicationSettings.findOne({
        userId: existingUser._id,
      });

      if (!settings) {
        settings = new AutoApplicationSettings({
          userId: existingUser._id,
          enabled: true,
          settings: {
            maxApplicationsPerDay: 10,
            applicationTemplate: "professional",
            autoSubmit: true,
            requireManualReview: false,
            language: "dutch",
          },
          criteria: {
            maxPrice: 2000,
            minRooms: 1,
            maxRooms: 5,
            propertyTypes: ["woning", "apartment"],
            locations: ["Utrecht", "Amsterdam"],
            minSize: 20,
            maxSize: 150,
          },
          personalInfo: {
            fullName: `${TEST_USER.firstName} ${TEST_USER.lastName}`,
            email: TEST_USER.email,
            phone: TEST_USER.phone,
            monthlyIncome: 3000,
            occupation: "Software Developer",
            moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            numberOfOccupants: 1,
          },
          formData: {
            firstName: TEST_USER.firstName,
            lastName: TEST_USER.lastName,
            email: TEST_USER.email,
            phone: TEST_USER.phone,
            age: 30,
            occupation: "Software Developer",
            monthlyIncome: 3000,
            preferredMoveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
              .toISOString()
              .split("T")[0],
          },
        });

        await settings.save();
        console.log("✅ Auto application settings created");
      } else {
        console.log("✅ Auto application settings already exist");
      }

      await mongoose.connection.close();
      return;
    }

    // Hash password
    console.log("🔐 Hashing password...");
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(TEST_USER.password, saltRounds);

    // Create user
    console.log("👤 Creating test user...");
    const user = new User({
      email: TEST_USER.email,
      password: hashedPassword,
      firstName: TEST_USER.firstName,
      lastName: TEST_USER.lastName,
      phone: TEST_USER.phone,
      isEmailVerified: true,
      role: "user",
      preferences: {
        notifications: {
          email: true,
          whatsapp: false,
        },
        language: "nl",
      },
    });

    await user.save();
    console.log("✅ Test user created successfully");

    // Create auto application settings
    console.log("⚙️ Creating auto application settings...");
    const AutoApplicationSettings = require("./src/models/AutoApplicationSettings");

    const settings = new AutoApplicationSettings({
      userId: user._id,
      enabled: true,
      settings: {
        maxApplicationsPerDay: 10,
        applicationTemplate: "professional",
        autoSubmit: true,
        requireManualReview: false,
        language: "dutch",
      },
      criteria: {
        maxPrice: 2000,
        minRooms: 1,
        maxRooms: 5,
        propertyTypes: ["woning", "apartment"],
        locations: ["Utrecht", "Amsterdam"],
        minSize: 20,
        maxSize: 150,
      },
      personalInfo: {
        fullName: `${TEST_USER.firstName} ${TEST_USER.lastName}`,
        email: TEST_USER.email,
        phone: TEST_USER.phone,
        monthlyIncome: 3000,
        occupation: "Software Developer",
        moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        numberOfOccupants: 1,
      },
      formData: {
        firstName: TEST_USER.firstName,
        lastName: TEST_USER.lastName,
        email: TEST_USER.email,
        phone: TEST_USER.phone,
        age: 30,
        occupation: "Software Developer",
        monthlyIncome: 3000,
        preferredMoveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0],
      },
    });

    await settings.save();
    console.log("✅ Auto application settings created");

    console.log("\n🎉 Test user setup completed successfully!");
    console.log(`📧 Email: ${TEST_USER.email}`);
    console.log(`🔑 Password: ${TEST_USER.password}`);
    console.log(`👤 Name: ${TEST_USER.firstName} ${TEST_USER.lastName}`);
    console.log(`📱 Phone: ${TEST_USER.phone}`);
  } catch (error) {
    console.error("❌ Setup failed:", error.message);
    console.error(error.stack);
  } finally {
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
  }
}

// Run setup
if (require.main === module) {
  setupTestUser().catch(console.error);
}

module.exports = setupTestUser;
