#!/usr/bin/env node

/**
 * Start Auto Application Status Monitor
 *
 * Usage:
 *   npm run monitor:auto-app         # Continuous monitoring
 *   npm run monitor:auto-app:once    # Single status check
 */

require("dotenv").config();

const AutoApplicationStatusMonitor = require("./monitor-auto-application-status");

// Handle command line arguments
const args = process.argv.slice(2);
const help = args.includes("--help") || args.includes("-h");

if (help) {
  console.log(`
🤖 Auto Application Status Monitor

Usage:
  node start-auto-app-monitor.js [options]

Options:
  --once, -o           Run once and exit (no continuous monitoring)
  --interval=<seconds> Set refresh interval in seconds (default: 10)
  --help, -h           Show this help message

Examples:
  node start-auto-app-monitor.js                    # Start continuous monitoring
  node start-auto-app-monitor.js --once             # Single status check
  node start-auto-app-monitor.js --interval=30      # Monitor with 30s refresh
  
The monitor shows:
  📊 System overview and queue statistics
  👥 Per-user status with attempt counts and timing
  ⏰ Time remaining until next attempts
  ❌ Recent failures with detailed error reasons
  📈 Success rates and completion statistics
  🚨 Error analysis and debugging information
  `);
  process.exit(0);
}

console.log("🚀 Starting Auto Application Status Monitor...\n");

const monitor = new AutoApplicationStatusMonitor();

// Handle any startup errors
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
  process.exit(1);
});

// Start the monitor
monitor
  .initialize()
  .then((success) => {
    if (!success) {
      console.error("❌ Failed to initialize monitor");
      process.exit(1);
    }

    console.log("✅ Monitor initialized successfully");

    if (args.includes("--once") || args.includes("-o")) {
      console.log("📊 Running single status check...\n");
      monitor
        .displayOnce()
        .then(() => {
          monitor.cleanup().then(() => {
            console.log("\n✅ Status check completed");
            process.exit(0);
          });
        })
        .catch((error) => {
          console.error("❌ Status check failed:", error.message);
          process.exit(1);
        });
    } else {
      console.log("🔄 Starting continuous monitoring...\n");
      monitor.startMonitoring();
    }
  })
  .catch((error) => {
    console.error("❌ Failed to start monitor:", error.message);
    process.exit(1);
  });
