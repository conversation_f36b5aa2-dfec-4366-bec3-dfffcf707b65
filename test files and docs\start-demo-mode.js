#!/usr/bin/env node

/**
 * Demo Mode Server Startup Script
 *
 * This script starts the server in demo mode with visible browser automation
 * for client presentations and testing purposes.
 */

require("dotenv").config();

console.log("🎬 Starting ZakMakelaar Server in DEMO MODE");
console.log("=".repeat(50));

// Display demo configuration
console.log("📋 Demo Configuration:");
console.log(`   DEMO_MODE: ${process.env.DEMO_MODE}`);
console.log(`   DEMO_BROWSER_HEADLESS: ${process.env.DEMO_BROWSER_HEADLESS}`);
console.log(`   DEMO_SHOW_BROWSER: ${process.env.DEMO_SHOW_BROWSER}`);
console.log(`   DEMO_SLOW_MOTION: ${process.env.DEMO_SLOW_MOTION}ms`);
console.log(
  `   FUNDA_AUTO_APPLICATION_HEADLESS: ${process.env.FUNDA_AUTO_APPLICATION_HEADLESS}`
);
console.log(`   FUNDA_DEMO_EMAIL: ${process.env.FUNDA_DEMO_EMAIL}`);
console.log(`   FUNDA_DEMO_FIRST_NAME: ${process.env.FUNDA_DEMO_FIRST_NAME}`);
console.log(`   FUNDA_DEMO_LAST_NAME: ${process.env.FUNDA_DEMO_LAST_NAME}`);

// Validate demo mode is enabled
if (process.env.DEMO_MODE !== "true") {
  console.error('❌ DEMO_MODE is not set to "true"');
  console.error("   Please ensure DEMO_MODE=true in your .env file");
  process.exit(1);
}

if (
  process.env.DEMO_BROWSER_HEADLESS !== "false" &&
  process.env.FUNDA_AUTO_APPLICATION_HEADLESS !== "false"
) {
  console.warn("⚠️  Both browser headless modes are enabled");
  console.warn(
    "   For visible demo mode, set DEMO_BROWSER_HEADLESS=false and FUNDA_AUTO_APPLICATION_HEADLESS=false"
  );
}

console.log("\n🚀 Initializing server with demo mode configuration...\n");

// Start the main server
require("./src/index.js");
