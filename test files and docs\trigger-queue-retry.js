require("dotenv").config();
const mongoose = require("mongoose");
const User = require("./src/models/User");
const ApplicationQueue = require("./src/models/ApplicationQueue");
const config = require("./src/config/config");

async function triggerQueueRetry() {
  try {
    await mongoose.connect(config.mongoURI);
    console.log("✅ Connected to database");

    // Find the retrying queue item
    const queueItem = await ApplicationQueue.findOne({
      status: "retrying",
    }).populate("userId", "email name");

    if (!queueItem) {
      console.log("❌ No retrying queue items found");
      return;
    }

    console.log(`\n🔍 Found retrying queue item:`);
    console.log(`   ID: ${queueItem._id}`);
    console.log(`   User: ${queueItem.userId?.email}`);
    console.log(`   Status: ${queueItem.status}`);
    console.log(`   Attempts: ${queueItem.attempts}`);
    console.log(`   Scheduled At: ${queueItem.scheduledAt}`);
    console.log(`   Current Time: ${new Date()}`);

    // Update the queue item to be processed immediately
    console.log(`\n🔄 Setting queue item for immediate processing...`);

    const now = new Date();
    queueItem.status = "pending";
    queueItem.scheduledAt = now;
    queueItem.delayUntil = undefined;
    queueItem.updatedAt = now;

    await queueItem.save();

    console.log(`✅ Queue item updated for immediate processing!`);
    console.log(`   New Status: ${queueItem.status}`);
    console.log(`   New Scheduled At: ${queueItem.scheduledAt}`);

    console.log(`\n👀 After the FormAutomationService fix:`);
    console.log(`   Browser should now be VISIBLE during processing`);
    console.log(`   Monitor with: node monitor-auto-application-status.js`);
    console.log(`   Processing should start within 30 seconds`);
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    mongoose.disconnect();
  }
}

triggerQueueRetry();
