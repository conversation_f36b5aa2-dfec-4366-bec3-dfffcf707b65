{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:04:33'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:04:35'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:04:39'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:04:47'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:04:47'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:04:50'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:04:52'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:04:56'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:01'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:08'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:09'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:10'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:13'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:18'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:21'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/amstelveen/appartement-fluweelboomlaan-117/43038625/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:05:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:27'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:29'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/rotterdam/appartement-tandwielstraat-999/89298106/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:05:30'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:30'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/rotterdam/huis-dosiostraat-182/88114663/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:05:30'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/enschede/appartement-hulsmaatstraat-45-104/43045859/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:05:40'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/almelo/huis-parallelweg-18/43023443/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:05:41'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/badhoevedorp/huis-nieuwemeerdijk-199/89470802/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:05:41'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:44'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:49'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:05:50'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.141Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'consecutive_failures',
    message: '3 consecutive transformation failures detected',
    value: 3,
    threshold: 3,
    timestamp: '2025-09-08T23:05:55.141Z'
  },
  level: 'error',
  message: '3 consecutive transformation failures detected',
  timestamp: '2025-09-09 00:05:55'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372755130_c16uj',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MULTIFUNCTIONELE GARAGEBOXEN TE HUUR IN WADDINXVEEN\n' +
              'Modern garageboxcomplex in Business Parkrand met 24/7 toegang en zakelijke mogelijkheden\n' +
              '\n' +
              'Aan de Zuidelijke Rondweg in Waddinxveen, direct naast de nieuwbouwwijk Park Triangel, is Business ParkRand ontwikkeld: een moderne werkomgeving met hoogwaardige garageboxen die zowel voor zakelijk als particulier gebruik beschikbaar zijn. Of u nu een startende ondernemer bent, een zzp’er met behoefte aan werkruimte, of juist op zoek bent naar veilige en toegankelijke opslag of stalling, hier vindt u de perfecte oplossing.\n' +
              '\n' +
              'Midden op het terrein van de eerste fase is een gebouw gerealiseerd met in totaal 184 garageboxen, verspreid over vier verdiepingen. De boxen variëren in grootte van circa 15 m² tot 51 m². Dankzij een eigen postadres per unit zijn de garageboxen ook zakelijk inzetbaar, bijvoorbeeld als inschrijfadres.\n' +
              '\n' +
              'De boxen zijn 24 uur per dag toegankelijk via een beveiligde elektrische schuifpoort. Elke garagebox is bereikbaar met de auto en voorzien van een (elektrische) overheaddeur, een standaard stroomaansluiting en verlichting. Op iedere etage bevinden zich sanitaire voorzieningen met toilet, voorportaal en wasbak, waardoor de units ook geschikt zijn voor langduriger gebruik als werkruimte.\n' +
              '\n' +
              'De ligging van ParkRand is ideaal: nabij de A12 en N207, met uitstekende verbindingen richting Gouda, Boskoop, Zoetermeer en Rotterdam. \n' +
              'Station Waddinxveen Triangel ligt op loopafstand, met elk kwartier een treinverbinding richting Gouda en Alphen a/d Rijn – ideaal voor ondernemers en gebruikers die ook met het openbaar vervoer reizen.\n' +
              '\n' +
              'Daarbij sluit de uitstraling van het complex naadloos aan op de architectuur van Park Triangel. Denk aan industriële details, metselwerk en een groene inrichting die is doorgetrokken vanuit de woonwijk. Het maakt van ParkRand de enige werklocatie binnen deze levendige woonomgeving met ruim 2.900 huishoudens.\n' +
              '\n' +
              'Indeling\n' +
              'TYPE G8 – 27 m²\n' +
              'De units bieden circa 27 m² bruto vloeroppervlakte en zijn gelegen op diverse etages binnen het complex. Alle verdiepingen zijn per voertuig bereikbaar via een hellingsbaan. De units zijn standaard voorzien van verlichting, wandcontactdozen en een handmatig bedienbare overheaddeur met een doorrijbreedte van circa 2,60 tot 2,70 meter. Dankzij de functionele indeling en ruime afmetingen zijn deze units geschikt voor uiteenlopende doeleinden, zoals opslag, werkruimte of kleinschalige zakelijke activiteiten. Enkele units zijn uitgevoerd met meerwerkopties, zoals een elektrisch bedienbare overheaddeur of een extra groep.\n' +
              '\n' +
              'Afmetingen:\n' +
              '- Lengte: 9,10 meter\n' +
              '- Breedte: 2,90 meter\n' +
              '\n' +
              'Hoogte & doorrijhoogte per verdieping:\n' +
              '- Begane grond: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Eerste verdieping: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Tweede verdieping: hoogte ca. 2,70 m | doorrijhoogte ca. 2,35 m\n' +
              '- Derde verdieping: hoogte ca. 2,80 m | doorrijhoogte ca. 2,20 m\n' +
              '\n' +
              'Op elke verdieping van het gebouw bevindt zich een centrale sanitaire ruimte, voorzien van een toilet, een wastafel met koudwaterkraan en een uitstortgootsteen. Deze faciliteiten maken de garageboxen geschikt voor zowel kort als langer gebruik, bijvoorbeeld als werkruimte of atelier. De sanitaire voorzieningen worden centraal onderhouden via de Vereniging van Eigenaren (VvE), wat bijdraagt aan een nette en verzorgde uitstraling van het complex.\n' +
              '\n' +
              'Huurcondities:\n' +
              'Type G8 27 m2 BESCHIKBAAR:\n' +
              'Vanaf € 370,- per maand excl. BTW, € 4.440,- per jaar excl. BTW.\n' +
              '\n' +
              'Servicekosten:\n' +
              'Bijdrage servicekosten € 48,- per maand excl. BTW per unit.\n' +
              '\n' +
              'Huurtermijn:\n' +
              'In overleg.\n' +
              '\n' +
              'Huuringangsdatum:\n' +
              'Beschikbaar na oplevering, verwacht in september 2025.\n' +
              '\n' +
              'Huurprijsherziening:\n' +
              'Jaarlijks, voor het eerst één jaar na huuringangsdatum, op basis van de wijziging van het maandprijsindexcijfer volgens de consumentenprijsindex (CPI), reeks CPI-werknemers Laag (2015=100) zoals gepubliceerd door het Centraal Bureau voor de Statistiek (CBS).\n' +
              '\n' +
              'Huurbetaling:\n' +
              'Per maand vooruit.\n' +
              '\n' +
              'Waarborgsom:\n' +
              'Drie (3) maanden huur incl. BTW.\n' +
              '\n' +
              'Huurovereenkomst:\n' +
              'Conform model ROZ.\n' +
              '\n' +
              'BTW:\n' +
              'Verhuurder wenst te opteren voor BTW-belaste huur en verhuur. Ingeval huurder de BTW niet kan verrekenen zal de huurprijs worden verhoogd ter compensatie van de gevolgen van het vervallen van de mogelijkheid om te opteren voor BTW-belaste huur.\n' +
              '\n' +
              'Bijzonderheden\n' +
              '- 24/7 toegankelijk via een elektrische schuifpoort\n' +
              '- Elke box is bereikbaar met de auto\n' +
              '- Voorzien van (elektrische) overheaddeur, verlichting en stroomaansluiting\n' +
              '- Sanitaire voorzieningen op elke verdieping (toilet, voorportaal, wasbak)\n' +
              '- Elke unit heeft een eigen postadres, dus ook interessant om zakelijk te gebruiken\n' +
              '- Veilig en representatief terrein met cameratoezicht\n' +
              '\n' +
              'De verhuur is gestart en de eerste units worden binnenkort opgeleverd. Wil jij verzekerd zijn van een garagebox op deze unieke en goed bereikbare locatie? Neem vandaag nog contact met ons op voor de mogelijkheden!\n' +
              '\n' +
              'Deze vrijblijvende objectinformatie is met de meeste zorg samengesteld, maar voor de juistheid daarvan kunnen wij geen aansprakelijkheid aanvaarden. Ook kan aan de vermelde gegevens geen enkel recht worden ontleend. Alle informatie is enkel en alleen bedoeld voor de presentatie van het object en niet meer dan een uitnodiging tot het doen van een bod. Eventueel bijgevoegde plattegronden zijn slechts indicatief.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:55.141Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.160Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'consecutive_failures',
    message: '4 consecutive transformation failures detected',
    value: 4,
    threshold: 3,
    timestamp: '2025-09-08T23:05:55.160Z'
  },
  level: 'error',
  message: '4 consecutive transformation failures detected',
  timestamp: '2025-09-09 00:05:55'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372755148_fesdz',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: Build year must be between 1800 and 2030. "price" must be less than or equal to 50000
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: 'Build year must be between 1800 and 2030',
          path: [ 'year' ],
          type: 'custom',
          context: { label: 'year', value: '1777', key: 'year' }
        },
        {
          message: '"price" must be less than or equal to 50000',
          path: [ 'price' ],
          type: 'number.max',
          context: { limit: 50000, value: 84500, label: 'price', key: 'price' }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:55.159Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.180Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.198Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.217Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372755204_rg9s2',
  source: 'funda',
  duration: '13ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'TE HUUR: Bernstraat 48 – Instapklare en verrassend ruime woning in een rustige straat van Schalkwijk\n' +
              '\n' +
              'In een rustige en prettige buurt in Haarlem-Schalkwijk ligt deze ruime en keurig afgewerkte woning. Bernstraat 48 is in 2020 grondig gerenoveerd: volledig gestript en opnieuw opgebouwd met aandacht voor kwaliteit en comfort. Van elektra tot vloeren, van stucwerk tot badkamers – werkelijk alles is vernieuwd. In augustus 2025 is de woning weer volledig opgefrist en luxer gemaakt. \n' +
              '\n' +
              'De woning beschikt over vier goed bemeten slaapkamers, verdeeld over twee verdiepingen, en twee moderne badkamers. Een luxe die zorgt voor comfort en privacy of voor wie graag een logeerkamer of thuiswerkplek heeft.\n' +
              '\n' +
              'De woonkamer is licht en ruim, met een nette doorgelegde vloer en strakke wanden. Aan de achterzijde bevindt zich een ruim balkon op het zuiden, waar je bijna de hele dag zon hebt. Aan de voorzijde is een tweede balkon, ideaal voor een koel moment in de schaduw.\n' +
              '\n' +
              'De degelijke, maar verzorgde keuken is uitgerust met diverse inbouwapparatuur, waaronder een vaatwasser, oven en een inductie kookplaat, en sluit goed aan op de stijl van de rest van de woning. Praktisch is de aparte kast met ruimte voor wasmachine en droger. Er is bovendien een eigen berging aanwezig voor extra opslag of fietsen.\n' +
              '\n' +
              'De woning is gunstig gelegen in het rustige deel van Schalkwijk, op loopafstand van winkelcentrum Schalkwijk. Hier vind je een uitgebreid aanbod aan winkels, supermarkten, horeca en voorzieningen. Ook openbaar vervoer en uitvalswegen zijn nabij.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '•\tWoonoppervlakte 94,6 m2\n' +
              '•\tNaast de maandelijkse huur is de huurder verantwoordelijk voor de kosten van verwarming (collectieve verwarming, maandelijkse vooruitbetaling van € 36 aan de verhuurder), water, elektriciteit, tv/internet en gemeentelijke belastingen\n' +
              '•\tDe huurperiode is voor onbepaalde tijd (huurovereenkomst model A)\n' +
              '•\tHuisdieren en roken zijn niet toegestaan\n' +
              '•\tDeze woning kan worden gehuurd door een huurder met een stabiel, voldoende maandelijks inkomen uit arbeid\n' +
              '•\tIn 2020 volledig gerenoveerd en vernieuwd. In augustus 2025 is de woning weer opgefrist en zijn de badkamers extra luxe verbouwd\n' +
              '•\tVier slaapkamers en twee moderne badkamers\n' +
              '•\tDegelijke, nette keuken met vaatwasser en inductieplaat. \n' +
              '•\tTwee balkons (waarvan één op het zuiden)\n' +
              '•\tAparte wasruimte en een eigen berging\n' +
              '•\tVolledig voorzien van isolerend glas\n' +
              '•\tBlokverwarming en warm water via Intergas HR ketel uit 2020\n' +
              '•\tRustige ligging in Schalkwijk, vlak bij winkelcentrum\n' +
              '•\tKeurig afgewerkt, instapklaar. \n' +
              '•\tGratis parkeren voor de deur\n' +
              '•\tBeschikbaar vanaf 1 september 2025\n' +
              '•\tDe woning beschikt over een nieuw energielabel C. \n' +
              '\n' +
              'Een fijne, ruime woning met een solide basis en alle comfort van nu. Ideaal voor wie zorgeloos wil wonen op een goed bereikbare locatie in Haarlem.\n' +
              '\n' +
              'FOR RENT: Bernstraat 48 – Move-in ready and surprisingly spacious home in a quiet part of Schalkwijk (Haarlem)\n' +
              '\n' +
              'Located in a quiet and pleasant area of Haarlem-Schalkwijk, you’ll find this spacious and neatly finished home. Bernstraat 48 was thoroughly renovated in 2020: completely stripped and rebuilt with attention to quality and comfort. Everything has been renewed, from the electrical system to the floors, plasterwork, and bathrooms. By August 2025, the house will have been completely refurbished and made more luxurious.\n' +
              '\n' +
              'The property offers four well-sized bedrooms spread over several floors and two modern bathrooms – a luxury that provides comfort and privacy or for anyone who needs a guest room or home office.\n' +
              '\n' +
              'The living room is bright and spacious, with a neat, continuous floor and smooth plastered walls. At the rear, you’ll find a large south-facing balcony that enjoys sunlight for most of the day. At the front is a second balcony, perfect for catching a breeze in the shade.\n' +
              "The well-maintained and practical kitchen is equipped with built-in appliances, including a dishwasher, and fits seamlessly with the rest of the home's modern look. There’s a separate utility closet with space for a washing machine and dryer, and an additional private storage unit is also available.\n" +
              '\n' +
              'The home is ideally located in a peaceful section of Schalkwijk, just a short walk from the Schalkwijk shopping center, which offers a wide range of shops, supermarkets, cafés, and essential amenities. Public transport and major roads are also close by.\n' +
              '\n' +
              'Key features:\n' +
              '•\tLiving area 94,6 m2\n' +
              '•\tIn addition to the monthly rent, the tenant is responsible for the costs of heating (block heating, monthly advance payment of € 36 to the landlord), water, electricity, TV/Internet, and municipal taxes\n' +
              '•\tThe rental period is indefinite\n' +
              '•\tThis property can be rented by a tenant with a stable, sufficient monthly income from employment\n' +
              '•\tPets and smoking are not permitted\n' +
              '•\tFully renovated in 2020. In august 20255 the apartment was refurbished and the two badrooms were renovated to an extra luxurious standard. \n' +
              '•\tFour bedrooms and two modern bathrooms\n' +
              '•\tKitchen with dishwasher\n' +
              '•\tTwo balconies (one facing south)\n' +
              '•\tSeparate laundry closet and private storage\n' +
              '•\tFully fitted with insulating double glazing\n' +
              '•\tDistrict heating and hot water via Intergas HR boiler (2020)\n' +
              '•\tQuiet location near Schalkwijk shopping center\n' +
              '•\tNeat and modern finish, move-in ready\n' +
              '•\tFree parking\n' +
              '•\tAvailable from september 1st, 2025\n' +
              '•       The property has a new energy label C.\n' +
              '\n' +
              'A comfortable, spacious home with a solid foundation and all the conveniences of modern living – ideal for those seeking a worry-free lifestyle in a well-connected part of Haarlem.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:55.217Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.237Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.254Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.271Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.287Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372755276_tpo4u',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MULTIFUNCTIONELE GARAGEBOXEN TE HUUR IN WADDINXVEEN\n' +
              'Modern garageboxcomplex in Business Parkrand met 24/7 toegang en zakelijke mogelijkheden\n' +
              '\n' +
              'Aan de Zuidelijke Rondweg in Waddinxveen, direct naast de nieuwbouwwijk Park Triangel, is Business ParkRand ontwikkeld: een moderne werkomgeving met hoogwaardige garageboxen die zowel voor zakelijk als particulier gebruik beschikbaar zijn. Of u nu een startende ondernemer bent, een zzp’er met behoefte aan werkruimte, of juist op zoek bent naar veilige en toegankelijke opslag of stalling, hier vindt u de perfecte oplossing.\n' +
              '\n' +
              'Midden op het terrein van de eerste fase is een gebouw gerealiseerd met in totaal 184 garageboxen, verspreid over vier verdiepingen. De boxen variëren in grootte van circa 15 m² tot 51 m². Dankzij een eigen postadres per unit zijn de garageboxen ook zakelijk inzetbaar, bijvoorbeeld als inschrijfadres.\n' +
              '\n' +
              'De boxen zijn 24 uur per dag toegankelijk via een beveiligde elektrische schuifpoort. Elke garagebox is bereikbaar met de auto en voorzien van een (elektrische) overheaddeur, een standaard stroomaansluiting en verlichting. Op iedere etage bevinden zich sanitaire voorzieningen met toilet, voorportaal en wasbak, waardoor de units ook geschikt zijn voor langduriger gebruik als werkruimte.\n' +
              '\n' +
              'De ligging van ParkRand is ideaal: nabij de A12 en N207, met uitstekende verbindingen richting Gouda, Boskoop, Zoetermeer en Rotterdam. \n' +
              'Station Waddinxveen Triangel ligt op loopafstand, met elk kwartier een treinverbinding richting Gouda en Alphen a/d Rijn – ideaal voor ondernemers en gebruikers die ook met het openbaar vervoer reizen.\n' +
              '\n' +
              'Daarbij sluit de uitstraling van het complex naadloos aan op de architectuur van Park Triangel. Denk aan industriële details, metselwerk en een groene inrichting die is doorgetrokken vanuit de woonwijk. Het maakt van ParkRand de enige werklocatie binnen deze levendige woonomgeving met ruim 2.900 huishoudens.\n' +
              '\n' +
              'Indeling\n' +
              'TYPE G9 – 29 m²\n' +
              'De units van circa 29 m² BVO zijn altijd hoekunits en daardoor iets breder dan gebruikelijk. Ze zijn gelegen op de eerste en tweede verdieping van het complex, beide bereikbaar per voertuig via de hellingsbaan. De units zijn standaard uitgerust met verlichting, wandcontactdozen en een handmatig bedienbare overheaddeur met een doorrijbreedte van circa 2,60 tot 2,70 meter. Dankzij de praktische indeling en extra breedte zijn ze geschikt voor uiteenlopende doeleinden, zoals opslag, werkruimte of kleinschalige zakelijke activiteiten.\n' +
              '\n' +
              'Op dit moment hebben wij twee van deze hoekunits beschikbaar:\n' +
              'Bouwnummer 77 (1e verdieping): huursom € 405,- excl. btw en servicekosten\n' +
              'Bouwnummer 96 (2e verdieping): huursom € 400,- excl. btw en servicekosten\n' +
              '\n' +
              'Afmetingen:\n' +
              '- Lengte: 6,80 meter\n' +
              '- Breedte: 3,95 meter\n' +
              '\n' +
              'Hoogte & doorrijhoogte per verdieping:\n' +
              '- Eerste verdieping: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Tweede verdieping: hoogte ca. 2,70 m | doorrijhoogte ca. 2,35 m\n' +
              '\n' +
              'Op elke verdieping van het gebouw bevindt zich een centrale sanitaire ruimte, voorzien van een toilet, een wastafel met koudwaterkraan en een uitstortgootsteen. Deze faciliteiten maken de garageboxen geschikt voor zowel kort als langer gebruik, bijvoorbeeld als werkruimte of atelier. De sanitaire voorzieningen worden centraal onderhouden via de Vereniging van Eigenaren (VvE), wat bijdraagt aan een nette en verzorgde uitstraling van het complex.\n' +
              '\n' +
              'Huurcondities:\n' +
              'Type G9 29 m2 BESCHIKBAAR:\n' +
              'Vanaf € 400,- per maand excl. BTW, € 3.360,- per jaar excl. BTW.\n' +
              '\n' +
              'Servicekosten:\n' +
              'Bijdrage servicekosten € 51,- per maand per unit.\n' +
              '\n' +
              'Huurtermijn:\n' +
              'In overleg.\n' +
              '\n' +
              'Huuringangsdatum:\n' +
              'Beschikbaar na oplevering, verwacht in januari 2026.\n' +
              '\n' +
              'Huurprijsherziening:\n' +
              'Jaarlijks, voor het eerst één jaar na huuringangsdatum, op basis van de wijziging van het maandprijsindexcijfer volgens de consumentenprijsindex (CPI), reeks CPI-werknemers Laag (2015=100) zoals gepubliceerd door het Centraal Bureau voor de Statistiek (CBS).\n' +
              '\n' +
              'Huurbetaling:\n' +
              'Per maand vooruit.\n' +
              '\n' +
              'Waarborgsom:\n' +
              'Drie (3) maanden huur incl. BTW.\n' +
              '\n' +
              'Huurovereenkomst:\n' +
              'Conform model ROZ.\n' +
              '\n' +
              'BTW:\n' +
              'Verhuurder wenst te opteren voor BTW-belaste huur en verhuur. Ingeval huurder de BTW niet kan verrekenen zal de huurprijs worden verhoogd ter compensatie van de gevolgen van het vervallen van de mogelijkheid om te opteren voor BTW-belaste huur.\n' +
              '\n' +
              'Bijzonderheden\n' +
              '- 24/7 toegankelijk via een elektrische schuifpoort\n' +
              '- Elke box is bereikbaar met de auto\n' +
              '- Voorzien van (elektrische) overheaddeur, verlichting en stroomaansluiting\n' +
              '- Sanitaire voorzieningen op elke verdieping (toilet, voorportaal, wasbak)\n' +
              '- Elke unit heeft een eigen postadres, dus ook interessant om zakelijk te gebruiken\n' +
              '- Veilig en representatief terrein met cameratoezicht\n' +
              '\n' +
              'De verhuur is gestart en de eerste units worden binnenkort opgeleverd. Wil jij verzekerd zijn van een garagebox op deze unieke en goed bereikbare locatie? Neem vandaag nog contact met ons op voor de mogelijkheden!\n' +
              '\n' +
              'Deze vrijblijvende objectinformatie is met de meeste zorg samengesteld, maar voor de juistheid daarvan kunnen wij geen aansprakelijkheid aanvaarden. Ook kan aan de vermelde gegevens geen enkel recht worden ontleend. Alle informatie is enkel en alleen bedoeld voor de presentatie van het object en niet meer dan een uitnodiging tot het doen van een bod. Eventueel bijgevoegde plattegronden zijn slechts indicatief.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:55.287Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.304Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372755293_vpran',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MULTIFUNCTIONELE GARAGEBOXEN TE HUUR IN WADDINXVEEN\n' +
              'Modern garageboxcomplex in Business Parkrand met 24/7 toegang en zakelijke mogelijkheden\n' +
              '\n' +
              'Aan de Zuidelijke Rondweg in Waddinxveen, direct naast de nieuwbouwwijk Park Triangel, is Business ParkRand ontwikkeld: een moderne werkomgeving met hoogwaardige garageboxen die zowel voor zakelijk als particulier gebruik beschikbaar zijn. Of u nu een startende ondernemer bent, een zzp’er met behoefte aan werkruimte, of juist op zoek bent naar veilige en toegankelijke opslag of stalling, hier vindt u de perfecte oplossing.\n' +
              '\n' +
              'Midden op het terrein van de eerste fase is een gebouw gerealiseerd met in totaal 184 garageboxen, verspreid over vier verdiepingen. De boxen variëren in grootte van circa 15 m² tot 51 m². Dankzij een eigen postadres per unit zijn de garageboxen ook zakelijk inzetbaar, bijvoorbeeld als inschrijfadres.\n' +
              '\n' +
              'De boxen zijn 24 uur per dag toegankelijk via een beveiligde elektrische schuifpoort. Elke garagebox is bereikbaar met de auto en voorzien van een (elektrische) overheaddeur, een standaard stroomaansluiting en verlichting. Op iedere etage bevinden zich sanitaire voorzieningen met toilet, voorportaal en wasbak, waardoor de units ook geschikt zijn voor langduriger gebruik als werkruimte.\n' +
              '\n' +
              'De ligging van ParkRand is ideaal: nabij de A12 en N207, met uitstekende verbindingen richting Gouda, Boskoop, Zoetermeer en Rotterdam. \n' +
              'Station Waddinxveen Triangel ligt op loopafstand, met elk kwartier een treinverbinding richting Gouda en Alphen a/d Rijn – ideaal voor ondernemers en gebruikers die ook met het openbaar vervoer reizen.\n' +
              '\n' +
              'Daarbij sluit de uitstraling van het complex naadloos aan op de architectuur van Park Triangel. Denk aan industriële details, metselwerk en een groene inrichting die is doorgetrokken vanuit de woonwijk. Het maakt van ParkRand de enige werklocatie binnen deze levendige woonomgeving met ruim 2.900 huishoudens.\n' +
              '\n' +
              'Indeling\n' +
              'TYPE G5 – 21 m²\n' +
              'De units bieden circa 21 m² bruto vloeroppervlakte en zijn gelegen op diverse etages binnen het complex. Alle verdiepingen zijn per voertuig bereikbaar via een hellingsbaan. De units zijn standaard voorzien van verlichting, wandcontactdozen en een handmatig bedienbare overheaddeur met een doorrijbreedte van circa 2,60 tot 2,70 meter. Dankzij de functionele indeling en ruime afmetingen zijn deze units geschikt voor uiteenlopende doeleinden, zoals opslag, werkruimte of kleinschalige zakelijke activiteiten. Enkele units zijn uitgevoerd met meerwerkopties, zoals een elektrisch bedienbare overheaddeur of een extra groep.\n' +
              '\n' +
              'Afmetingen:\n' +
              '- Lengte: 6,80 meter\n' +
              '- Breedte: 2,90 meter\n' +
              '\n' +
              'Hoogte & doorrijhoogte per verdieping:\n' +
              '- Eerste verdieping: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Tweede verdieping: hoogte ca. 2,70 m | doorrijhoogte ca. 2,35 m\n' +
              '- Derde verdieping: hoogte ca. 2,80 m | doorrijhoogte ca. 2,20 m\n' +
              '\n' +
              'Op elke verdieping van het gebouw bevindt zich een centrale sanitaire ruimte, voorzien van een toilet, een wastafel met koudwaterkraan en een uitstortgootsteen. Deze faciliteiten maken de garageboxen geschikt voor zowel kort als langer gebruik, bijvoorbeeld als werkruimte of atelier. De sanitaire voorzieningen worden centraal onderhouden via de Vereniging van Eigenaren (VvE), wat bijdraagt aan een nette en verzorgde uitstraling van het complex.\n' +
              '\n' +
              'Huurcondities:\n' +
              'Type G5 21 m2 BESCHIKBAAR:\n' +
              'Vanaf € 280,- per maand excl. BTW, € 3.360,- per jaar excl. BTW.\n' +
              '\n' +
              'Servicekosten:\n' +
              'Bijdrage servicekosten € 37,- per maand per unit.\n' +
              '\n' +
              'Huurtermijn:\n' +
              'In overleg.\n' +
              '\n' +
              'Huuringangsdatum:\n' +
              'Beschikbaar na oplevering, verwacht in september 2025.\n' +
              '\n' +
              'Huurprijsherziening:\n' +
              'Jaarlijks, voor het eerst één jaar na huuringangsdatum, op basis van de wijziging van het maandprijsindexcijfer volgens de consumentenprijsindex (CPI), reeks CPI-werknemers Laag (2015=100) zoals gepubliceerd door het Centraal Bureau voor de Statistiek (CBS).\n' +
              '\n' +
              'Huurbetaling:\n' +
              'Per maand vooruit.\n' +
              '\n' +
              'Waarborgsom:\n' +
              'Drie (3) maanden huur incl. BTW.\n' +
              '\n' +
              'Huurovereenkomst:\n' +
              'Conform model ROZ.\n' +
              '\n' +
              'BTW:\n' +
              'Verhuurder wenst te opteren voor BTW-belaste huur en verhuur. Ingeval huurder de BTW niet kan verrekenen zal de huurprijs worden verhoogd ter compensatie van de gevolgen van het vervallen van de mogelijkheid om te opteren voor BTW-belaste huur.\n' +
              '\n' +
              'Bijzonderheden\n' +
              '- 24/7 toegankelijk via een elektrische schuifpoort\n' +
              '- Elke box is bereikbaar met de auto\n' +
              '- Voorzien van (elektrische) overheaddeur, verlichting en stroomaansluiting\n' +
              '- Sanitaire voorzieningen op elke verdieping (toilet, voorportaal, wasbak)\n' +
              '- Elke unit heeft een eigen postadres, dus ook interessant om zakelijk te gebruiken\n' +
              '- Veilig en representatief terrein met cameratoezicht\n' +
              '\n' +
              'De verhuur is gestart en de eerste units worden binnenkort opgeleverd. Wil jij verzekerd zijn van een garagebox op deze unieke en goed bereikbare locatie? Neem vandaag nog contact met ons op voor de mogelijkheden!\n' +
              '\n' +
              'Deze vrijblijvende objectinformatie is met de meeste zorg samengesteld, maar voor de juistheid daarvan kunnen wij geen aansprakelijkheid aanvaarden. Ook kan aan de vermelde gegevens geen enkel recht worden ontleend. Alle informatie is enkel en alleen bedoeld voor de presentatie van het object en niet meer dan een uitnodiging tot het doen van een bod. Eventueel bijgevoegde plattegronden zijn slechts indicatief.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:55.304Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.325Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.343Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.359Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.375Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.390Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.407Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.434Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.456Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.478Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.498Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372755487_x4car',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: '*FOR ENGLISH SEE BELOW \n' +
              '\n' +
              'HET PROJECT – De Kade\n' +
              'Stel je voor: modern wonen in hartje Amsterdam, op een plek waar rust, ruimte en karakter samenkomen. Welkom bij De Kade – 56 eigentijdse appartementen op een stoer en sfeervol eiland aan de Wittenburgervaart. Hier is het autoluw en hebben fietsers en wandelaars vrij spel.\n' +
              '\n' +
              'De woningen zijn verdeeld over twee karaktervolle gebouwen, elk met een unieke uitstraling. Veel appartementen beschikken over een balkon of loggia – of zelfs allebei! En alsof dat nog niet genoeg is, kun je samen met je buren genieten van een groene, gedeelde daktuin met een schitterend uitzicht.\n' +
              '\n' +
              'DE LOCATIE – Oostenburg\n' +
              'Oostenburg bruist! Deze bijzondere wijk in het voormalige havengebied van Amsterdam ademt historie én toekomst. Je woont straks aan de gezellige VOC-kade of de sfeervolle Pieter Goosstraat, tussen smalle eilanden en schiereilanden met een robuust verleden.\n' +
              '\n' +
              'Ooit was dit het bedrijvige domein van 17e-eeuwse kooplieden. Die rijke historie voel je nog steeds, mede dankzij de industriële details die hier prachtig bewaard zijn gebleven. Nu is Oostenburg getransformeerd tot een levendige stadswijk met een stoer randje. Hier woon je duurzaam en modern, maar toch vlak bij alles: winkels, horeca, OV en volop ruimte om te ontspannen.\n' +
              '\n' +
              'Na een drukke dag nog even een wandeling langs het water? Een drankje op je balkon in de avondzon? Of samen met buren ontspannen in de daktuin? Het kan hier allemaal. En binnenkort is ook de indrukwekkende Van Gendthallen gerestaureerd, de nieuwe ontmoetingsplek van de buurt.\n' +
              '\n' +
              'DE WONING – Pieter Goosstraat 36\n' +
              'Comfort, ruimte en een moderne afwerking – dit appartement heeft het allemaal! Gelegen aan de rustige Pieter Goosstraat, biedt deze woning een royale woonkamer met open keuken, voorzien van luxe inbouwapparatuur. Er is een ruime slaapkamer, een stijlvolle badkamer met inloopdouche, wastafel en toilet, én een handige berging voor je spullen.\n' +
              '\n' +
              'Bovendien kun je volop genieten van het buitenleven – op je eigen balkon of in de gedeelde daktuin met een adembenemend uitzicht over Amsterdam. De muren zijn al sausklaar, en de vloer kan – in overleg met de huidige huurder – worden overgenomen. Wij brengen je graag met hen in contact.\n' +
              '\n' +
              'In het kort:\n' +
              '•\tModern nieuwbouwappartement in een bruisende wijk\n' +
              '•\tRuime slaapkamer\n' +
              '•\tGrote, lichte woonkamer met open keuken en ruimte voor een thuiswerkplek\n' +
              '•\tKeuken met luxe inbouwapparatuur\n' +
              '•\tGedeelde daktuin met uitzicht over Amsterdam\n' +
              '\n' +
              'Goed om te weten:\n' +
              '•\tEr is geen aparte externe berging aanwezig\n' +
              '•\tEr is geen parkeergarage in het project \n' +
              '•\tDe inkomenseis bedraagt 4x de maandhuur\n' +
              '•\tHet appartement is direct beschikbaar\n' +
              '\n' +
              'Ben je klaar om te wonen in een van de meest spannende nieuwe woonwijken van Amsterdam? Kom de sfeer proeven – je bent van harte welkom op De Kade!\n' +
              'Interesse in deze woning? Stuur ons een mail via  of je kunt ons telefonisch bereiken op +31 85 303 28 52\n' +
              '------------------------------------------------------------------------------------------------\n' +
              '\n' +
              'THE PROJECT – De Kade\n' +
              'Imagine living in the heart of Amsterdam, where modern comfort meets peace, space, and character. Welcome to De Kade – a unique development featuring 56 contemporary apartments on a stylish and vibrant island along the Wittenburgervaart. This is a low-traffic area, giving pedestrians and cyclists the freedom to move around with ease.\n' +
              '\n' +
              'The apartments are spread across two distinctive buildings, each with its own unique character. Many of the homes feature a balcony or loggia – and some even offer both! And as a bonus, you’ll share a lush rooftop garden with your neighbors, offering stunning views over the city.\n' +
              '\n' +
              'THE NEIGHBORHOOD – Oostenburg\n' +
              'Oostenburg is alive and buzzing! This dynamic neighborhood, located in a former harbor area of Amsterdam, is rich in both history and future potential. You’ll be living on the lively VOC-kade or the charming Pieter Goosstraat, surrounded by narrow islands and peninsulas full of character.\n' +
              '\n' +
              "Once a bustling hub for 17th-century merchants, the area's industrial past is still visible in its beautifully preserved architecture. Today, Oostenburg has transformed into a vibrant urban district with a bold, modern edge. Here you’ll find sustainable, high-quality living right in the city — close to shops, restaurants, public transport, and recreational areas.\n" +
              '\n' +
              'After a busy day, why not take a peaceful walk along the waterfront? Or unwind with a drink on your balcony in the evening sun? Prefer some company? Relax in the shared rooftop garden with your neighbors. And soon, the impressive Van Gendthallen will reopen after a major renovation — set to become the new beating heart of the neighborhood.\n' +
              '\n' +
              'THE APARTMENT – Pieter Goosstraat 36\n' +
              'Comfort, space, and contemporary design – this apartment has it all! Located on the quiet and pleasant Pieter Goosstraat, this home features a spacious living room with an open-plan kitchen equipped with high-end built-in appliances. There’s a generous bedroom, a stylish bathroom with a walk-in shower, sink and toilet, and a convenient storage space.\n' +
              '\n' +
              'You’ll also enjoy outdoor living to the fullest – whether it’s on your private balcony or the shared rooftop garden with panoramic views across Amsterdam. The walls have already been finished (ready to paint), and the current flooring can be taken over from the previous tenant by arrangement — we’d be happy to connect you.\n' +
              '\n' +
              'Key features:\n' +
              '•\tModern newly built apartment in a vibrant neighborhood\n' +
              '•\tSpacious bedroom\n' +
              '•\tLarge, bright living room with open kitchen and space for a home office\n' +
              '•\tKitchen with luxury built-in appliances\n' +
              '•\tShared rooftop garden with amazing views over Amsterdam\n' +
              '\n' +
              'Important to know:\n' +
              '•\tNo separate external storage is included\n' +
              '•\tNo garage or dedicated parking space – parking must be arranged separately\n' +
              '•\tIncome requirement is 4x the monthly rent\n' +
              '•\tAvailable for occupancy right away\n' +
              '\n' +
              'Interested in this apartment?\n' +
              'Email us at  or give us a call at +31 85 303 28 52',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:55.498Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.523Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372755513_ol1fb',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'ENGLISH VERSION BELOW\n' +
              '\n' +
              'Gelegen middenin het hart van Almere, direct naast het World Trade Center Almere en station Almere centrum bieden wij u diverse volledig gemeubileerde, turn-key studio’s en appartementen voor zowel short stay als long stay te huur aan. Inschrijving bij de gemeente is mogelijk evenals het ontvangen van post.\n' +
              '\n' +
              'Wij bieden verschillende verblijfstypes:\n' +
              'Comfort studio               € 1.815,00 ongeveer 27m2\n' +
              'Superior studio              € 1.980,00 ongeveer  31m2\n' +
              'Comfort appartement     € 2.145,00 ongeveer  38m2\n' +
              'Superior appartement     € 2.310,00 ongeveer  54m2\n' +
              '\n' +
              'Deze luxe full service appartementen zijn gelegen op de 9e to 14e verdieping en zijn onderdeel van het Martinez complex waar onder andere Regus, Sky Fitness en Restaurant Loetje zijn gevestigd. De volledig turn-key appartementen hebben een periodieke schoonmaak  en linnenservice.  De huurder kan gebruikmaken van alle faciliteiten die het complex te bieden heeft, zoals sporten in de Sky Fitness (tegen vergoeding) of een drankje aan de rooftopbar. Parkeren is via per dag of in abonnementsvorm mogelijk in de parkeerkelder van het complex.\n' +
              '\n' +
              'De centrale locatie en de full service die geboden wordt, maken deze appartementen ideaal voor expats, of mensen die opzoek zijn naar tijdelijke accommodatie. Vanzelfsprekend kunnen deze full serviceappartementen ook een ideale oplossing bieden voor mensen die met relationele problemen kampen of in scheiding liggen.\n' +
              '\n' +
              'Alle moderne en volledig gemeubileerde appartementen zijn uitgerust met een zithoek, een bureau, thee- en koffiefaciliteiten, een flatscreen-tv met kabelzenders en een kitchenette. De kitchenettes zijn uitgerust met een magnetron, een koelkast en kookfaciliteiten. Alle appartementen zijn voorzien van airco, periodieke schoonmaak en linnenservice. De eind schoonmaak bedraagt €100. Daarnaast biedt elke accommodatie een weergaloos uitzicht over Almere en bij helder weer zelfs Amsterdam!\n' +
              '\n' +
              'De appartementen liggen tegenover het World Trade Center (WTC) Almere, het treinstation en de bushalte. Met de trein bevindt u zich via een rechtstreekse verbinding in 20 min op Amsterdam Centraal en respectievelijk 22 minuten op de Amsterdamse Zuidas. Tevens bent u binnen 30 minuten op station Schiphol wat het een ideale locatie voor expats maakt.\n' +
              '\n' +
              'Het complex biedt een ontbijtrestaurant, waar dagelijks een continentaal ontbijt tegen betaling wordt geserveerd. In de bar kun je een drankje nuttigen, of je kunt een bezoek brengen aan restaurant Loetje Almere. Verder is er een zelfbedieningswinkel waar je 24 uur per dag eten en drinken kunt kopen. Indien u de groene omgeving van Almere wilt verkennen, kunt u bij de accommodatie terecht voor fietsverhuur.\n' +
              '\n' +
              'Interesse? Bezichtigingen zijn via ons kantoor 7 dagen per week op afspraak mogelijk.\n' +
              '\n' +
              'Located right in the heart of Almere, right next to the World Trade Center Almere and Almere center station, we offer various fully furnished, turn-key studios and apartments for rent for both short stay and long stay. Registration with the municipality is possible, as well as receiving mail.\n' +
              '\n' +
              'We offer different types of accommodation:\n' +
              'Comfort studio          € 1,815.00 approximately 27m2\n' +
              'Superior studio          € 1,980.00 approximately 31m2\n' +
              'Comfort apartment    € 2,145.00 approximately 38m2\n' +
              'Superior apartment    € 2,310.00 approximately 54m2\n' +
              '\n' +
              'These luxurious full-service apartments are located on the 9th to 14th floor and are part of the Martinez complex, which includes Regus, Sky Fitness and Restaurant Loetje. The fully turnkey apartments have cleaning and linen service. The tenant can use all the facilities in the complex has to offer, such as exercising in the Sky Fitness (for a fee) or having a drink at the rooftop bar. Parking is possible per day or in subscription form in the parking garage of the complex.\n' +
              '\n' +
              'The central location and the full service offered make these apartments ideal for expats or people looking for temporary accommodation. It goes without saying that these full service apartments can also offer an ideal solution for people who are experiencing relationship problems or are going through a divorce.\n' +
              '\n' +
              'All modern and fully furnished apartments are equipped with a seating area, a desk, a tea/coffee maker, a flat-screen TV with cable channels and a kitchenette. The kitchenettes are equipped with a microwave, refrigerator and cooking facilities. All apartments are equipped with air conditioning, periodic cleaning and linen service. The final cleaning costst are €100. In addition, each accommodation offers an unparalleled view of Almere and even Amsterdam in clear weather!\n' +
              '\n' +
              'The apartments are located opposite the World Trade Center (WTC) Almere, the train station and the bus stop. By train you are via a direct connection in 20 minutes at Amsterdam Central Station and 22 minutes at the Amsterdam Zuidas respectively. You are also within 30 minutes at Schiphol station, which makes it an ideal location for expats.\n' +
              '\n' +
              'The complex offers a breakfast restaurant, where a continental breakfast against a fee is served daily. You can enjoy a drink in the  bar, or you can visit restaurant Loetje Almere. There is also a self-service shop where you can buy food and drinks 24 hours a day. If you want to explore the green surroundings of Almere, you can rent a bicycle at the accommodation.\n' +
              '\n' +
              'Interested? Viewings are possible through our office 7 days a week by appointment.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:55.523Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.546Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.567Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.589Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.614Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.646Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.670Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.691Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.715Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.738Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.762Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.787Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372755775_zct1z',
  source: 'funda',
  duration: '12ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'ENGLISH BELOW\n' +
              '\n' +
              'Op een fantastische locatie, gelegen middenin het hart van Den Haag, en op loopafstand van Den Haag centraal station bieden wij u diverse volledig gemeubileerde, full service studio’s en appartementen voor zowel short stay als long stay te huur aan. Inschrijving bij de gemeente is mogelijk evenals het ontvangen van post.\n' +
              '\n' +
              'Deze luxe full service appartementen zijn gelegen op de 10de t/m 18e verdieping, waardoor u een prachtig uitzicht heeft over de skyline van Den Haag. De schoonmaak en linnenservice zullen ter plaatse worden verzorgd. De huurder kan gebruikmaken van o.a de volgende faciliteiten; het continentaal ontbijtbuffet, het fitnesscentrum en de was faciliteiten. Parkeren is tegen betaling per dag of in abonnementsvorm mogelijk in de parkeerkelder van het complex.\n' +
              '\n' +
              'De centrale ligging en de full service die geboden wordt, maken deze appartementen ideaal voor expats, of mensen die opzoek zijn naar tijdelijke accommodatie. Vanzelfsprekend kunnen deze full serviceappartementen ook een ideale oplossing bieden voor mensen die met relationele problemen kampen of in scheiding liggen.\n' +
              '\n' +
              'Alle ruime moderne en volledig gemeubileerde studio’s en appartementen zijn uitgerust met een zitgedeelte, thee- en koffiefaciliteiten, een flatscreen-tv met kabelzenders, strijkfaciliteiten en een kitchenette. De kitchenettes zijn uitgerust met een magnetron, vaatwasser, koelkast en kookfaciliteiten. Alle appartementen zijn voorzien van airco en dagelijkse schoonmaak en linnenservice (indien gewenst). Daarnaast biedt elk verblijf een weergaloos uitzicht over Den Haag!\n' +
              '\n' +
              'Het station bevindt zich op een ongeveer 5 minuten lopen. Met de trein bevindt u zich via een rechtstreekse verbinding in 33 minuten op Rotterdam Centraal en binnen 50 minuten op Amsterdam Centraal. Tevens bent u binnen 45 minuten op station Rotterdam The Hague Airport, wat het een ideale locatie voor expats maakt.\n' +
              '\n' +
              'U kunt dagelijks gratis gebruik maken van het continentaal ontbijtbuffet. In de bar van de lounge kunt u de gehele dag gratis gebruik maken van de koffie en thee faciliteiten. Tevens kunt u hier zowel non-alcoholisch als alcoholische dranken bestellen. Verder heeft het hotel een kleine “grab en go corner”, waar u kleine versnaperingen kunt kopen. Indien u de omgeving van Den Haag wilt verkennen, kunt u bij de accommodatie terecht voor fietsverhuur.\n' +
              '\n' +
              'Wij bieden verschillende verblijfstypes, waarvan de prijzen op aanvraag ( vanaf € 3600,00 per maand)zijn:\n' +
              'Standard Queen\n' +
              'Standard King\n' +
              'Standard Twin\n' +
              'Standard Queen panorama\n' +
              'Standard King panorama\n' +
              'Loft\n' +
              'One bedroom apartment\n' +
              '\n' +
              'Interesse? Bezichtigingen zijn via ons kantoor 7 dagen per week op afspraak mogelijk.\n' +
              '\n' +
              'In a fantastic location, right in the heart of The Hague, and within walking distance of The Hague central station, we offer you various fully furnished, full-service studios and apartments for both short stay and long stay for rent. Registration with the municipality is possible, as is receiving mail.\n' +
              '\n' +
              "These luxurious full-service apartments are located on the 10th to 18th floors, giving you a beautiful view of the skyline of The Hague. The cleaning and linen service will be provided on site. The tenant can use the following facilities, among others; the continental breakfast buffet, the fitness center and the laundry facilities. Parking is available in the complex's basement parking lot for a fee per day or by subscription.\n" +
              '\n' +
              'The central location and the full service offered make these apartments ideal for expats or people looking for temporary accommodation. Naturally, these full-service apartments can also offer an ideal solution for people who are experiencing relationship problems or are going through a divorce.\n' +
              '\n' +
              'All spacious modern and fully furnished studios and apartments are equipped with a seating area, tea/coffee making facilities, a flat-screen TV with cable channels, ironing facilities and a kitchenette. The kitchenettes are equipped with a microwave, dishwasher, refrigerator and cooking facilities. All apartments have air conditioning and daily cleaning and linen service (if desired). In addition, each accommodation offers an unparalleled view of The Hague!\n' +
              '\n' +
              'The station is approximately a 5-minute walk away. By train you can reach Rotterdam Central Station in 33 minutes and Amsterdam Central Station in 50 minutes. You can also reach Rotterdam The Hague Airport station within 45 minutes, making it an ideal location for expats.\n' +
              '\n' +
              'You can make free use of the continental breakfast buffet every day. In the lounge bar you can make free use of the coffee and tea facilities all day long. You can also order both non-alcoholic and alcoholic drinks here. Furthermore, the hotel has a small “grab and go corner”, where you can buy small refreshments. If you want to explore the surroundings of The Hague, you can rent a bicycle at the accommodation.\n' +
              '\n' +
              'We offer different types of accommodation, the prices ( from € 3.600,00 per month) of which are on request:\n' +
              'Standard Queen\n' +
              'Standard King\n' +
              'Standard Twin\n' +
              'Standard Queen panorama\n' +
              'Standard King panorama\n' +
              'Loft\n' +
              'One bedroom apartment\n' +
              '\n' +
              'Interested? Viewings are possible by appointment through our office 7 days a week.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:55.787Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.812Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.838Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.865Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.890Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.919Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.945Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.968Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:55.994Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:55'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.018Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372756007_b2c5d',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'ENGLISH BELOW\n' +
              '\n' +
              'Op een fantastische locatie, gelegen middenin het hart van Den Haag, en op loopafstand van Den Haag centraal station bieden wij u diverse volledig gemeubileerde, full service studio’s en appartementen voor zowel short stay als long stay te huur aan. Inschrijving bij de gemeente is mogelijk evenals het ontvangen van post.\n' +
              'Deze luxe full service appartementen zijn gelegen op de 10de t/m 18e verdieping, waardoor u een prachtig uitzicht heeft over de skyline van Den Haag. De schoonmaak en linnenservice zullen ter plaatse worden verzorgd. De huurder kan gebruikmaken van o.a de volgende faciliteiten; het continentaal ontbijtbuffet, het fitnesscentrum en de was faciliteiten. Parkeren is tegen betaling per dag of in abonnementsvorm mogelijk in de parkeerkelder van het complex.\n' +
              'De centrale ligging en de full service die geboden wordt, maken deze appartementen ideaal voor expats, of mensen die opzoek zijn naar tijdelijke accommodatie. Vanzelfsprekend kunnen deze full serviceappartementen ook een ideale oplossing bieden voor mensen die met relationele problemen kampen of in scheiding liggen.\n' +
              'Alle ruime moderne en volledig gemeubileerde studio’s en appartementen zijn uitgerust met een zitgedeelte, thee- en koffiefaciliteiten, een flatscreen-tv met kabelzenders, strijkfaciliteiten en een kitchenette. De kitchenettes zijn uitgerust met een magnetron, vaatwasser, koelkast en kookfaciliteiten. Alle appartementen zijn voorzien van airco en dagelijkse schoonmaak en linnenservice (indien gewenst). Daarnaast biedt elk verblijf een weergaloos uitzicht over Den Haag!\n' +
              'Het station bevindt zich op een ongeveer 5 minuten lopen. Met de trein bevindt u zich via een rechtstreekse verbinding in 33 minuten op Rotterdam Centraal en binnen 50 minuten op Amsterdam Centraal. Tevens bent u binnen 45 minuten op station Rotterdam The Hague Airport, wat het een ideale locatie voor expats maakt.\n' +
              'U kunt dagelijks gratis gebruik maken van het continentaal ontbijtbuffet. In de bar van de lounge kunt u de gehele dag gratis gebruik maken van de koffie en thee faciliteiten. Tevens kunt u hier zowel non-alcoholisch als alcoholische dranken bestellen. Verder heeft het hotel een kleine “grab en go corner”, waar u kleine versnaperingen kunt kopen. Indien u de omgeving van Den Haag wilt verkennen, kunt u bij de accommodatie terecht voor fietsverhuur.\n' +
              'Wij bieden verschillende verblijfstypes, waarvan de prijzen op aanvraag zijn:\n' +
              'Standard Queen\n' +
              'Standard King\n' +
              'Standard Twin\n' +
              'Standard Queen panorama\n' +
              'Standard King panorama\n' +
              'Loft\n' +
              'One bedroom apartment\n' +
              '\n' +
              'Interesse? Bezichtigingen zijn via ons kantoor 7 dagen per week op afspraak mogelijk.\n' +
              '\n' +
              'In a fantastic location, right in the heart of The Hague, and within walking distance of The Hague central station, we offer you various fully furnished, full-service studios and apartments for both short stay and long stay for rent. Registration with the municipality is possible, as is receiving mail.\n' +
              "These luxurious full-service apartments are located on the 10th to 18th floors, giving you a beautiful view of the skyline of The Hague. The cleaning and linen service will be provided on site. The tenant can use the following facilities, among others; the continental breakfast buffet, the fitness center and the laundry facilities. Parking is available in the complex's basement parking lot for a fee per day or by subscription.\n" +
              'The central location and the full service offered make these apartments ideal for expats or people looking for temporary accommodation. Naturally, these full-service apartments can also offer an ideal solution for people who are experiencing relationship problems or are going through a divorce.\n' +
              'All spacious modern and fully furnished studios and apartments are equipped with a seating area, tea/coffee making facilities, a flat-screen TV with cable channels, ironing facilities and a kitchenette. The kitchenettes are equipped with a microwave, dishwasher, refrigerator and cooking facilities. All apartments have air conditioning and daily cleaning and linen service (if desired). In addition, each accommodation offers an unparalleled view of The Hague!\n' +
              'The station is approximately a 5-minute walk away. By train you can reach Rotterdam Central Station in 33 minutes and Amsterdam Central Station in 50 minutes. You can also reach Rotterdam The Hague Airport station within 45 minutes, making it an ideal location for expats.\n' +
              'You can make free use of the continental breakfast buffet every day. In the lounge bar you can make free use of the coffee and tea facilities all day long. You can also order both non-alcoholic and alcoholic drinks here. Furthermore, the hotel has a small “grab and go corner”, where you can buy small refreshments. If you want to explore the surroundings of The Hague, you can rent a bicycle at the accommodation.\n' +
              'We offer different types of accommodation, the prices of which are on request:\n' +
              'Standard Queen\n' +
              'Standard King\n' +
              'Standard Twin\n' +
              'Standard Queen panorama\n' +
              'Standard King panorama\n' +
              'Loft\n' +
              'One bedroom apartment\n' +
              '\n' +
              'Interested? Viewings are possible by appointment through our office 7 days a week.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:56.018Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.048Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.071Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.096Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.117Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.139Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.163Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.187Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.214Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.238Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.263Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.286Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.307Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.333Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.361Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.388Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.423Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.454Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.477Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.499Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.521Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.541Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.566Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.589Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.613Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.638Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.659Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.681Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.703Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372756692_ebj5i',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: '-English translation below-\n' +
              '\n' +
              'Luxe, sfeervol en modern 2-kamer appartement te huur in Hyde Park - Kensington! Dit moderne appartement wordt volledig gemeubileerd aangeboden en beschikt over een prachtig, weids uitzicht vanaf de vijfde verdieping. De lichte woonkamer beschikt over heerlijk openslaande deuren met Franse balkons en een moderne keuken met diverse inbouwapparatuur. De rustgevende slaapkamer beschikt over hoge inbouwkasten en eveneens een Frans balkon. Het appartement voelt heerlijk ruim aan mede door de hoge plafonds en de grote ramen. Kortom; een nieuw gebouw, met een zeer hoge standaard, in een prachtige omgeving naast het Stadspark Hoofddorp!\n' +
              '\n' +
              "De woning is gelegen aan de rand van het centrum van Hoofddorp met alle voorzieningen op loopafstand zoals diverse restaurants, een ruim winkelaanbod in winkelcentrum Vier Meren, een bioscoop, verschillenden supermarkten en stadsschouwburg ''Het Cultuurgebouw''. Het station van hoofddorp is op nog geen 5 minuten fietsen. Op fietsafstand is het Burgemeester van Stamplein waar een groot aanbod aan bussen vertrekt naar onder andere Schiphol, Amsterdam en Haarlem brengen. In het vernieuwde Stadspark van Hoofddorp kunt u terecht voor een fijne wandeling. Het Haarlemmermeerse Bos is op 10 minuten fietsafstand gelegen. Tevens zijn alle snelwegen makkelijk te bereiken zoals N201, A4 en A9.\n" +
              '\n' +
              'Goed om te weten\n' +
              '* Bouwjaar: 2024\n' +
              '* Energielabel A+\n' +
              '* Volledig gemeubileerd\n' +
              '* Extra ruime keuken met Bosch apparatuur\n' +
              '* Woonkamer met twee Franse balkons\n' +
              '* Heerlijk balkon\n' +
              '* Vloerverwarming en koeling\n' +
              '* Gezonde en actieve Vereniging van Eigenaars\n' +
              '* Gemeenschappelijke binnentuin\n' +
              '* Twee fietsenbergingen\n' +
              '* Inpandige berging etage -1\n' +
              '* Stadspark Hoofddorp: 1 minuut lopen\n' +
              '* Winkelcentrum Vier Meren: 5 minuten lopen\n' +
              '* Uitvalswegen richting Amsterdam, Den Haag en Haarlem zijn gemakkelijk te bereiken\n' +
              '* Goede verbinding met het openbaar vervoer naar Amsterdam, Haarlem, Leiden en Schiphol\n' +
              '* Niet beschikbaar voor: studenten, huisdieren en rokers\n' +
              '* Borgsom: 2 maanden huur\n' +
              '* Huurprijs exclusief nutsvoorzieningen, tv en internet én gemeentelijke lasten\n' +
              '* Huurovereenkomst van minimaal 12 maanden\n' +
              '* Per direct beschikbaar\n' +
              '\n' +
              'Begane grond\n' +
              'Entree met brede lobby en een conciërge, toegang tot de twee gemeenschappelijke fietsenbergingen en de twee liften. \n' +
              '\n' +
              'Eerste verdieping\n' +
              'Gemeenschappelijke tuin aangelegd door landschapsarchitecten Piet Oudolf en Wim Voogt, hier kunt u heerlijk genieten van een kleine wandeling in de zon.\n' +
              '\n' +
              'Vijfde verdieping\n' +
              'Hal met toegang tot alle vertrekken met aan de rechterzijde de lichte woonkamer met open keuken en twee Franse balkons. De keuken beschikt over diverse inbouwapparatuur waaronder een koelkast, vriezer, 4-pits inductiekookplaat, afzuigunit (allen van het merk Bosch). Via de hal bereikt u tevens het balkon waar u heerlijk kunt genieten van de ochtendzon. De lichte masterbedroom is gelegen aan de linkerzijde van het appartement en heeft een vaste kastenwand. De badkamer beschikt over een inloopdouche, wastafelmeubel en een handdoekradiator. Er is een ruime separaat toilet en een bergruimte welke voorzien is van een was/droogcombinatie.\n' +
              '\n' +
              '-English translation-\n' +
              '\n' +
              'Luxury, atmospheric and modern 2-room apartment for rent in Hyde Park - Kensington! This modern apartment is offered fully furnished and has beautiful, wide-open views from the fifth floor. The bright living room has lovely French doors with French balconies and a modern kitchen with various built-in appliances. The relaxing bedroom has high-fitted wardrobes and also a French balcony. The apartment feels wonderfully spacious, partly due to the high ceilings and the large windows. In short; A new building, with a very high standard, in a beautiful surrounding, next to Hoofddorp City Park!\n' +
              '\n' +
              "The house is located on the edge of the center of Hoofddorp with all amenities within walking distance, such as various restaurants, a wide range of shops in the Vier Meren shopping center, a cinema, various supermarkets and the city theater ''Het Cultuurgebouw''. Hoofddorp train station is nearby, less than 5 minutes by bike. Within cycling distance is the Burgemeester van Stampplein, where a wide range of buses depart to Schiphol, Amsterdam and Haarlem, among others. You can go for a nice walk in the renovated Stadspark of Hoofddorp. The Haarlemmermeerse Bos is a 10-minute bike ride away. All highways are also easily accessible, such as N201, A4 and A9.\n" +
              '\n' +
              'Good to know\n' +
              '* Year of construction: 2024\n' +
              '* Energy label A+\n' +
              '* Fully furnished\n' +
              '* Extra spacious kitchen with Bosch appliances\n' +
              '* Living room with two French balconies\n' +
              '* Lovely balcony\n' +
              '* Underfloor heating and cooling\n' +
              "* Healthy and active Owners' Association\n" +
              '* Communal courtyard\n' +
              '* Two bicycle sheds\n' +
              '* Indoor storage room floor -1\n' +
              '* Hoofddorp City Park: 1 minute walk\n' +
              '* Vier Meren shopping center: 5 minutes walk\n' +
              '* Major roads to Amsterdam, The Hague and Haarlem are easily accessible\n' +
              '* Good connection with public transport to Amsterdam, Haarlem, Leiden and Schiphol\n' +
              '* Not available for: students, pets and smokers\n' +
              '* Deposit: 2 months rent\n' +
              '* Rent excluding utilities, TV and internet and municipal charges\n' +
              '* Rental agreement with a minimum of 12 months\n' +
              '* Available immediately\n' +
              '\n' +
              'Ground floor\n' +
              'Entrance with wide lobby and a concierge, access to the two communal bicycle sheds and the two elevators. \n' +
              '\n' +
              'First floor\n' +
              "Communal, the building's private garden laid out by landscape known architects Piet Oudolf and Wim Voogt, where you can enjoy a short walk in the sun.\n" +
              '\n' +
              'Fifth floor\n' +
              'Hall with access to all rooms with the bright living room with open kitchen and two French balconies on the right. The kitchen has various built-in appliances, including a refrigerator, freezer, 4-burner induction hob, extractor unit (all from the Bosch brand). Through the hall you also reach the balcony where you can enjoy the morning sun. The bright master bedroom is located on the left side of the apartment and has a fixed cupboard wall. The bathroom has a walk-in shower, washbasin and towel radiator. There is a spacious separate toilet and a storage room which is equipped with a washer/dryer combination.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:56.702Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.726Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.746Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757372756736_p1d5y',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'English below:\n' +
              '\n' +
              'Mooier en leuker dan dit wordt het niet…!\n' +
              '\n' +
              'Deze luxe en geheel gemoderniseerde appartementen zijn gelegen op loopafstand van het gezellige centrum van Haarlem. \n' +
              'De appartementen zijn volledig duurzaam, stijlvol ingericht en energiezuinig gebouwd, ideaal!\n' +
              'Byzantiumstraat is letterlijk op loopafstand van het gezellige centrum van Haarlem met haar vele terrassen, restaurants en winkelstraatjes.\n' +
              'Openbaar vervoer met diverse busverbindingen is eveneens op loopafstand gelegen. Via deze busverbindingen heb je een directe verbinding o.a. naar Haarlem centrum, Schiphol, Hoofddorp en Amsterdam Zuid-as.\n' +
              'Vanwege de zeer centrale ligging zijn de uitvalswegen richting o.a. Amsterdam, Schiphol, Hoofddorp, Utrecht, Rotterdam en Den Haag zeer snel en eenvoudig bereikbaar.\n' +
              'De stranden en duinen van Bloemendaal en Zandvoort zijn eveneens eenvoudig met de fiets te bereiken.\n' +
              '\n' +
              'DUURZAAM WONEN OP EEN UNIEKE LOCATIE\n' +
              'U komt straks te wonen in een duurzame woning. Hoe kan dat? De woning is voorzien van uitstekende hoogwaardige isolatie waardoor u in de basis al veel minder energie gaat verbruiken. Door het toepassen van vloerverwarming door het gehele huis is de woning op lage temperatuur aangenaam én zal deze duurzaam worden verwarmd. U heeft dan ook geen losse radiotoren meer in uw woning.\n' +
              '\n' +
              'Wat levert dat concreet op?\n' +
              '– Een te verwachten lage energierekening door de volledige verduurzaming\n' +
              '– Een zeer comfortabele woning\n' +
              '– Energielabel A\n' +
              '- Volledig uitgerust met een mechanische ventilatie met warmte terugwin systeem (WTW) welke daarbij ook nog eens de lucht in het appartement zuivert\n' +
              '– Door het toepassen van de LTV minder Co2 uitstoot, en daarmee beter voor het klimaat\n' +
              '\n' +
              'Deze appartementen zijn op een zeer hoog kwaliteitsniveau afgewerkt en voorzien van de volgende indeling:\n' +
              '\n' +
              'Begane grond:\n' +
              'Entree, lichte woonkamer voorzien van PVC vloer met vloerverwarming/vloerverkoeling. Moderne keuken voorzien van diverse inbouwapparatuur, te weten: vaatwasser, koel-vries combinatie, combi oven-magnetron, mengkraan, afzuigkap en een inductiekookplaat.\n' +
              'Slaapkamer aan de achterzijde eveneens voorzien van vloerverwarming en verkoeling.\n' +
              'Moderne badkamer met inloopdouche met regendouche, zwevend toilet, wastafelmeubel met spiegel, thermostaatkraan, wasmachine en droger.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Huurprijs € 2.150,- per maand waar nog een voorschot van 125 euro per maand bij komt voor energie en waterverbruik.\n' +
              '- Borg € 4.300 ,-\n' +
              '- Direct te betrekken indien gewenst, snelle oplevering mogelijk.\n' +
              '- Huurperiode in overleg\n' +
              '- Inductieplaat met downdraft\n' +
              '- Terras met elektrisch te openen glazen dak waardoor ruimte zowel in de zomer als winter te gebruiken is\n' +
              '- Luxe stalen pui volledig te openen om maximaal gebruik te maken van het terras\n' +
              '- Entree via de berging/garage. Gebruik hiervan in overleg.\n' +
              '- Ring video deurbel\n' +
              '- Geheel gemoderniseerde en zeer sfeervolle woning in populaire en gewilde buurt\n' +
              '- Maximaal geïsoleerd en volledig duurzaam gebouwd met hoge plafondhoogte\n' +
              '- Geheel voorzien van vloerverwarming en verkoeling middels een warmtepompsysteem.\n' +
              '- Geheel gelijkvloers\n' +
              '- De gezellige binnenstad van Haarlem met haar vele terrassen en winkelstraatjes is op 5 minuten fietsafstand gelegen, daarbij zijn supermarkten, sportscholen en bijvoorbeeld een bibliotheek op een nabij gelegen afstand te vinden\n' +
              '- Op loopafstand van scholen, supermarkten, winkelstraat Amsterdamstraat en winkelcentrum van Haarlem en het N.S. station Haarlem centraal\n' +
              '- Uitvalswegen richting Amsterdam, Schiphol, Hoofddorp, Alkmaar, Utrecht en Rotterdam in directe nabijheid gelegen\n' +
              '- De stranden en duinen van Bloemendaal en Zandvoort zijn eveneens op fietsafstand gelegen\n' +
              '- Oplevering, in overleg\n' +
              '- Parkeren, in overleg\n' +
              '\n' +
              '__________________________________________________________________________________________________\n' +
              '\n' +
              "It doesn't get more beautiful and better than this…!\n" +
              '\n' +
              'These luxurious and fully modernized apartments are located within walking distance of the pleasant city centre of Haarlem.\n' +
              'The apartments are fully sustainable, stylishly furnished and energy-efficient, ideal!\n' +
              'Byzantiumstraat is literally within walking distance of the pleasant city centre with its many cafes, restaurants and shopping streets.\n' +
              'Public transport with various bus connections is also within walking distance. Via these bus connections you have a direct connection to, among others, Haarlem centre, Schiphol, Hoofddorp, Amsterdam centre and Amsterdam Zuidas.\n' +
              'Due to its very central location Amsterdam, Schiphol, Hoofddorp, Utrecht, Rotterdam and The Hague are very brief and easily accessible.\n' +
              'The beaches and dunes of Bloemendaal and Zandvoort are also easy to reach by bicycle.\n' +
              '\n' +
              'Basically: you will not find a place more luxurious and convenient than this!\n' +
              '\n' +
              'These apartments are completed with a very high quality finish and have the following layout:\n' +
              '\n' +
              'Ground floor:\n' +
              'Entrance, bright living room with PVC floor with underfloor heating and cooling. Modern kitchen with various built-in appliances, namely: dishwasher, fridge-freezer, combi oven-microwave, mixer tap, extractor hood and an induction hob.\n' +
              'Bedroom at the rear also with underfloor heating and cooling.\n' +
              'Modern bathroom with walk-in shower with rain shower, floating toilet, washbasin with mirror, thermostat, washing machine and dryer.\n' +
              '\n' +
              'Details:\n' +
              '- Rental price € 2.150,- per month to which an advance payment of € 125 per month is added for energy and water consumption.\n' +
              '- Deposit 2 months (€ 4.300,-)\n' +
              '- Immediately available if desired, fast delivery possible.\n' +
              '- Rental period in consultation\n' +
              '- Completely modernized and very attractive house in a popular and sought-after neighbourhood\n' +
              '- Fully insulated and durable construction with high ceilings\n' +
              '- All on the ground floor located\n' +
              '- The cozy city center of Haarlem with its many bars, cafes and shopping streets is a 5-minute bike ride away, while supermarkets, gyms and, for example, a library can be found nearby\n' +
              '- Within walking distance of schools, supermarkets, shopping street Amsterdamstraat, shopping center of Haarlem and the N.S. Haarlem central station\n' +
              '- Roads to Amsterdam, Schiphol, Hoofddorp, Alkmaar, Utrecht and Rotterdam located in the immediate vicinity\n' +
              '- The beaches and dunes of Bloemendaal and Zandvoort are also within cycling distance\n' +
              '- Delivery in consultation\n' +
              '- Parking in consultation\n' +
              '\n' +
              'SUSTAINABLE LIVING IN A UNIQUE LOCATION\n' +
              'You will soon be living in a sustainable home. How is that possible? The house is equipped with excellent high-quality insulation so that you will use much less energy. By applying underfloor heating throughout the house, the house maintains a  pleasant temperature and it will be heated sustainably. You no longer have separate radiators in your home.\n' +
              '\n' +
              'What does that deliver in concrete terms?\n' +
              '- An expected low energy bill due to full sustainability\n' +
              '- A very comfortable home\n' +
              '- Energy label A\n' +
              '- Fully equipped with underfloor heating and cooling through a heat pump system.\n' +
              '- Fully equipped with a mechanical ventilation with heat recovery system (HRV) which also purifies the air in the apartment\n' +
              '- By applying the LTH regulation system, less CO2 emissions, and therefore better for the climate\n' +
              '- Terrace with glass roof which you can electrically open to make use of the space in both summer and winter\n' +
              '- Luxury steel facade, can be fully opened to make maximum use of the terrace\n' +
              '- Entrance through the storage room / garage. Usage in consultation.\n' +
              '- Ring video doorbell',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:05:56.746Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.771Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:05:56.793Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:05:56'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:02'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:02'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:06'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:09'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:10'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:26'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:28'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:32'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/haarlem/appartement-colensostraat-58-rd/43017243/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:06:36'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/den-haag/appartement-dr-lelykade-36-b/89510647/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:06:38'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:44'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/waddinxveen/parkeergelegenheid-steur-154/89512924/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:06:44'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:46'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/almelo/huis-appelstraat-31/43023438/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:06:55'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:06:55'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/enschede/appartement-hulsmaatstraat-45-402/43021511/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:06:56'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:01'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:06'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:17'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:17'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:18'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:20'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:29'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:37'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:37'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:39'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:39'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/wormerveer/appartement-zuideinde-1-b/89434850/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:07:48'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/den-haag/appartement-oude-haagweg-42-r/89486036/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:07:50'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:50'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:56'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:07:59'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/waddinxveen/parkeergelegenheid-steur-240/89440166/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:08:01'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/venlo/parkeergelegenheid-laurens-janszoon-costerstraat-16/89467444/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:08:05'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:10'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/enschede/appartement-hulsmaatstraat-45-202/43018318/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:08:10'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:13'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:24'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:31'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:32'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:36'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:46'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:52'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:54'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:08:57'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/huizen/appartement-crailoseweg-116-b5/43096661/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:09:06'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/naaldwijk/appartement-verdilaan-15-t/43934333/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:09:06'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:07'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:12'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/berkel-en-rodenrijs/parkeergelegenheid-industrieweg-69-144/89396855/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:09:16'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/huizen/appartement-crailoseweg-112-d5/43074762/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:09:25'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:28'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:29'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:36'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:46'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:47'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:52'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:09:54'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:10:09'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:10:10'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:10:11'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:10:14'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/huizen/appartement-crailoseweg-110-d1/43096669/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:10:21'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/rotterdam/appartement-hertekade-105/89406470/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:10:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/veldhoven/appartement-de-reijenburg-67/43922663/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:10:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.413Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030401_u7wdg',
  source: 'funda',
  duration: '12ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'State-of-the-Art wonen aan de Laan van Meerdervoort 342\n' +
              '\n' +
              'Elegantie, comfort en duurzaamheid: drie woorden die deze vijf luxe huurappartementen perfect omschrijven. Dit voormalige kantoorpand is de afgelopen jaren grondig en onder architecturaal toezicht gerenoveerd, wat heeft geresulteerd in vijf huurwoningen in het luxe segment waar het aan niets ontbreekt. Alle woningen zijn hagelnieuw en stijlvol gemeubileerd, voorzien van een eigen warmtepomp met vloerverwarming (en koeling!) en zonnepanelen, hebben allen hun eigen internetaansluiting en zeer hoge energielabels (waaronder A ++++).\n' +
              '\n' +
              'Hier ervaar je "turn-key" wonen op een nieuw niveau: stijlvol, modern en met aandacht voor detail. Gelegen in een van de meest gewilde buurten van Den Haag, met uitstekende (openbaar vervoer-) verbindingen naar internationale scholen, winkels en restaurants. Denk hierbij bijvoorbeeld aan de gezellige Reinkenstraat en het Regentesseplein.\n' +
              '\n' +
              'Word jij de eerste bewoner van een van deze prachtige appartementen? Reageer dan snel en we geven je graag een rondleiding.\n' +
              '\n' +
              'Deze advertentie omschrijft Laan van Meerdervoort 342A:\n' +
              '\n' +
              'Entree op straat niveau, algemene voordeur. Hal met toegang tot de voordeur van het appartement. Achter de entree is de woonkamer met slaapkamer gelegen. De woonkamer is gelegen aan de voorzijde van het appartement en is erg licht. Wat direct opvalt is de mooie erker, stijlvolle meubilering en de open keuken met composieten werkblad welke van alle gemakken is voorzien. Denk hierbij aan een luxe Quooker heetwaterkraan, een oven-magnetron combinatie van Siemens, een koelkast, vaatwasser en een inductiekookplaat met downdraft. De woning is verder voorzien van een visgraat PVC vloer en wordt verwarmd (en gekoeld!) middels een warmtepomp. Kortom, comfort in optima forma!\n' +
              '\n' +
              'De slaapkamer is gelegen aan de achterzijde van het appartement en biedt toegang tot de luxe badkamer voorzien van een inloopdouche met hand- en regendouche, een dubbele wastafel met luxe meubel en een verwarmde spiegel met verlichting. Ook het hangende toilet is in deze ruimte aanwezig.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Kenmerken van deze woning:\n' +
              '\n' +
              'Energielabel A++++\n' +
              'Woonoppervlakte ca. 37m2\n' +
              'De woning is per 1 november 2025 beschikbaar\n' +
              'Minimale huurperiode 3 maanden\n' +
              'De woning wordt volledig gemeubileerd en ingericht opgeleverd (€150 p/m)\n' +
              'Volledig onder architectuur gerenoveerd, unieke materiaalkeuzes en stijl (in 2024)\n' +
              'Vloerverwarming en actieve koeling via warmtepomp (Daikin)\n' +
              'Luxe keuken met Siemens apparatuur en Quooker\n' +
              'Woning voorzien van eigen high-speed internet aansluiting\n' +
              'Lage servicekosten (€60 per maand)\n' +
              'Voorschot verbruik elektra en water, periodieke schoonmaak en internet\n' +
              'Borg is gelijk aan 1 maand huur\n' +
              'Geen gemeentelijke huisvestingsvergunning van toepassing\n' +
              '\n' +
              '**\n' +
              '\n' +
              'State-of-the-Art Living at Laan van Meerdervoort 342\n' +
              '\n' +
              'Elegance, comfort, and sustainability: three words that perfectly describe these five luxury rental apartments. This former office building has been thoroughly renovated over the past few years under architectural supervision, resulting in five high-end rental homes that leave nothing to be desired. All residences are brand new and stylishly furnished, equipped with their own heat pumps with underfloor heating (and cooling!), solar panels, individual internet connections, and very high energy labels (including A ++++).\n' +
              '\n' +
              `Here, you experience "turn-key" living on a new level: stylish, modern, and attention to detail. Located in one of The Hague's most desirable neighborhoods, with excellent (public transport) connections to international schools, shops, and restaurants. Think, for example, of the cozy Reinkenstraat and the Regentesseplein.\n` +
              '\n' +
              'Will you be the first resident of one of these beautiful apartments? Respond quickly, and we will gladly give you a tour.\n' +
              '\n' +
              'This advertisement describes Laan van Meerdervoort 342A:\n' +
              '\n' +
              'Entrance at street level, main front door. Hallway with access to the apartment’s front door. Behind the entrance is the living room with a bedroom located. The living room is situated at the front of the apartment and is very bright. What immediately stands out is the beautiful bay window, stylish furnishings, and the open kitchen with a composite countertop, fully equipped for all conveniences. Think of a luxury Quooker hot water tap, a Siemens oven-microwave combination, a refrigerator, dishwasher, and an induction cooktop with downdraft. The residence is further equipped with a herringbone PVC floor and is heated (and cooled!) via a heat pump. In short, comfort in optimal form!\n' +
              '\n' +
              'The bedroom is located at the rear of the apartment and provides access to the luxurious bathroom equipped with a walk-in shower with hand and rain shower, a double sink with a luxury vanity, and a heated mirror with lighting. The wall-mounted toilet is also present in this space.\n' +
              '\n' +
              'Layout:\n' +
              '\n' +
              'Features of this residence:\n' +
              '\n' +
              'Energy Label A++++\n' +
              'Living area approximately 37m²\n' +
              'The residence is available per november 1st 2025\n' +
              'Minimum rental period of 3 months\n' +
              'The residence is delivered fully furnished and decorated (€150 per month)\n' +
              'Completely renovated under architectural supervision, unique material choices, and style (in 2024)\n' +
              'Underfloor heating and active cooling via heat pump (Daikin)\n' +
              'Luxury kitchen with Siemens appliances and Quooker\n' +
              'Residence equipped with its own high-speed internet connection\n' +
              'Low service costs (€60 per month)\n' +
              'Advance payment for electricity and water consumption, periodic cleaning, and internet\n' +
              'Security deposit equal to 1 month’s rent\n' +
              'No municipal housing permit applicable\n' +
              '\n' +
              "If you're interested in experiencing luxury living in a prime location, don't hesitate to reach out for a viewing!",
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.413Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.448Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030436_9wjbz',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'For English, see below**\n' +
              '\n' +
              'Welkom aan de Laan van nieuw Oosteinde! In dit schitterende pand uit 1890 zijn onder architectuur acht hoogwaardige woningen gerealiseerd, met A+ energielabel.\n' +
              '\n' +
              'De zeer luxe en tot in de puntjes afgewerkte woningen zijn van alle gemakken voorzien en vrijwel energieneutraal dankzij de warmtepompen, zonnepanelen, hoogwaardige isolatie en CO2 gestuurde ventilatie. Een unicum voor huurwoningen in Voorburg en omstreken! Des temeer vanwege de realisatie in het karakteristieke gebouw uit laat 19e eeuw.\n' +
              '\n' +
              'De woningen zijn ontworpen door Millimeter architecten en voorzien van luxe en unieke materiaalkeuzes. De keukens voorzien van A-merk apparatuur (AEG, Quooker). De woningen zijn tevens volledig gemeubileerd en ingericht met oog voor kwaliteit en detail. Uitgerust met o.a. complete keukeninventaris, beddengoed en was- droog combinatie etc. om direct turn-key te kunnen wonen. Extra bijzonder is de aandacht voor de vele kastruimte en slim maatmeubilair, die de woningen hun maximale potentie laten benutten.\n' +
              '\n' +
              'Op de begane grond bevinden zich vier woningen, elk met eigen tuin aan de woning. Op de eerste etage zijn twee woningen gerealiseerd met een balkon of terras, op de tweede etage twee appartementen waarvan een met balkon. Voor alle woningen is er een grote gemeenschappelijke tuin gerealiseerd en optioneel zijn externe bergingen en parkeerplaatsen (met laadpaal) op eigen terrein bij te huren (op basis van beschikbaarheid).\n' +
              '\n' +
              'De locatie is perfect, op wandelafstand van het pittoreske centrum van Voorburg, treinstation Voorburg en treinstation Laan van NOI. In minder dan een minuut rijden zit je op de snelweg richting Amsterdam, Rotterdam en Utrecht.\n' +
              '\n' +
              'Indeling huisnummer 21-H:\n' +
              '\n' +
              'Entree op de begane grond, hal, trap naar de top etage. Voordeur appartement. Entree appartement met toilet en aan de linkerzijde direct de keuken voorzien van oven/magnetron combinatie, vaatwasser, inductiekookplaat, Quooker kokend water kraan en een mooi natuurstenen blad. Aan de achterzijde van de keuken bevindt zich een grote kastenwand. Open verbinding naar de slaapkamer met en suite badkamer (voorzien van wastafelmeubel en inloopdouche). Ruime woonkamer. Aan de woonkamer is een over de breedte gelegen balkon met een mooi uitzicht.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '\n' +
              'Energielabel A+\n' +
              'Woonoppervlakte ca. 40m2\n' +
              'Ruim balkon\n' +
              'De woning is per 20 oktober 2025 beschikbaar\n' +
              'De woning wordt volledig gemeubileerd en ingericht opgeleverd (€150 p/m)\n' +
              'Volledig onder architectuur gerenoveerd – unieke materiaalkeuzes en stijl\n' +
              'Servicekosten €125 per maand (inclusief eigen high speed internet aansluiting, vaste bijdrage in gezamenlijke stroom- en waterverbruik voor o.a. de collectieve warmtepomp installatie en verlichting algemene ruimten en tuin en bijdrage aan periodieke schoonmaak algemene ruimten en tuin onderhoud\n' +
              'Borg is gelijk aan 1 maand huur\n' +
              'Geen gemeentelijke huisvestingsvergunning van toepassing\n' +
              '\n' +
              '**\n' +
              '\n' +
              'Welcome to the Laan van Nieuw Oosteinde! In this beautiful building from 1890, eight high-quality homes have been realized under architecture, with an A+ energy label.\n' +
              '\n' +
              'The very luxurious and finished to perfection properties are fully equipped and virtually energy-neutral thanks to heat pumps, solar panels, high-quality insulation and CO2-controlled ventilation. Unique for rental properties in Voorburg and the surrounding area! Also unique because of the realization of all of these features in the characteristic building from the late 19th century.\n' +
              '\n' +
              'The houses are designed by Millimeter architects and equipped with luxurious and unique material choices. The kitchens are equipped with A-brand appliances (AEG, Quooker). The houses are also fully furnished and decorated with an eye for quality and detail. Equipped with, among other things, complete kitchen inventory, bedding and washer-dryer combination, etc. to be able to move in turn-key right away. Extra special is the attention paid to the ample cupboard space and smart custom furniture, which allow the homes to utilize their maximum potential.\n' +
              '\n' +
              'There are four houses on the ground floor, each with its own garden attached to the house. Two apartments have been realized on the first floor with a balcony or terrace, and on the second floor two apartments as well, of which one with a balcony.\n' +
              '\n' +
              'A large communal garden has been realized for all homes and external storage rooms and parking spaces (with charging station) can optionally be rented on site (based on availability).\n' +
              '\n' +
              "The location is perfect, within walking distance of the picturesque center of Voorburg, train station Voorburg and train station Laan van NOI. In less than a minute's drive you are on the highway to Amsterdam, Rotterdam and Utrecht.\n" +
              '\n' +
              'Layout:\n' +
              '\n' +
              'Entrance on the ground floor, hall, stairs to the top floor. Front door apartment. Entrance apartment with toilet and on the left directly the kitchen with oven / microwave combination, dishwasher, induction hob, Quooker boiling water tap and a beautiful natural stone top. At the rear of the kitchen is a large cupboard wall. Open connection to the bedroom with en-suite bathroom (with washbasin and walk-in shower). Spacious living room. The living room has a balcony located across the width with a beautiful view.\n' +
              '\n' +
              'Layyout number 21-H:\n' +
              '\n' +
              'Entrance on the ground floor, hall, stairs to the top floor. Front door apartment. Entrance apartment with toilet and on the left directly the kitchen with oven / microwave combination, dishwasher, induction hob, Quooker boiling water tap and a beautiful natural stone top. At the rear of the kitchen is a large cupboard wall. Open connection to the bedroom with en-suite bathroom (with washbasin and walk-in shower). Spacious living room. The living room has a balcony located across the width with a beautiful view.\n' +
              '\n' +
              'Particularities\n' +
              '\n' +
              'Energy label A+\n' +
              'Living area approx. 40m2\n' +
              'Large balcony\n' +
              'Available as of October 20th 2025\n' +
              'Completely architecturally renovated - unique choice of materials and style\n' +
              'The property is delivered fully furnished and equipped (€150 per month)\n' +
              'Fully renovated under architectural design – unique material choices and style\n' +
              'Service costs €125 per month (including personal high-speed internet connection, a fixed contribution to shared electricity and water usage for the collective heat pump installation, lighting of common areas and garden, and contribution to periodic cleaning of common areas and garden maintenance)\n' +
              'Deposit equal to 1 month’s rent\n' +
              'No municipal housing permit required',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.447Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.468Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.491Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.507Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.523Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030512_fdtv2',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Heeft u interesse in deze woning? Wij verzoeken u vriendelijk om via Funda te reageren. Telefonisch een bezichtiging aanvragen is helaas niet mogelijk. Wij doen ons best zo snel mogelijk te reageren! Nadat u uw interesse heeft getoond nemen wij contact met u op. Wij zoeken de beste match voor de eigenaar. \n' +
              '\n' +
              'Exceptional living in The Hague in Plesmanduin – See English tekst below\n' +
              '\n' +
              'Wonen in een rijksmonument met het comfort van nieuwbouw op een van de groenste en mooiste plekken van Den Haag. Dit royale loft-achtige appartement is volledig gelijkvloers en bereikbaar vanaf de centrale hoofdingang direct op de begane grond. In het appartement zelf bevindt u zich op de derde woonlaag met uitzicht over het water. \n' +
              '\n' +
              'INDELING:\n' +
              'Representatieve afgesloten hoofdentree met brievenbussen, entree appartement direct op de begane grond:\n' +
              '•\tLichte woon-/eetkamer van ca. 45 m² met grote raampartijen en deur naar ruim balkon op het zuidoosten\n' +
              '•\tPlafondhoogte 3,23 m in woon- en slaapkamer\n' +
              '•\tLuxe open keuken met schiereiland, Quooker, koel-/vrieskast, multifunctionele oven, inductiekookplaat met ingebouwde afzuiging en vaatwasser\n' +
              '•\tApart toilet, videofoon en garderobe\n' +
              '•\tInpandige berging met wasmachine- en drogeraansluiting\n' +
              '•\tLuxe badkamer met ruime inloopdouche en dubbele wastafel\n' +
              '•\tRuime slaapkamer van ca. 13,4 m²\n' +
              '\n' +
              'Gemeenschappelijke voorzieningen:\n' +
              '•\tAfgesloten fietsenberging met mogelijkheid tot opladen e-bikes\n' +
              '•\tSfeervolle gezamenlijke binnentuin als groene oase\n' +
              '\n' +
              'BIJZONDERHEDEN\n' +
              '•\tGestoffeerd met vloer, gordijnen, verlichting en keukenapparatuur\n' +
              '•\tAlle vertrekken voorzien van led-plafondspots\n' +
              '•\tHR++ glas en visgraat pvc-vloer met vloerverwarming/-koeling\n' +
              '•\tDuurzaam, gasloos, energielabel A+\n' +
              '•\tWKO-installatie voor vloerverwarming/-koeling en warm tapwater\n' +
              '•\tCentrale luchtbehandeling met warmteterugwinning\n' +
              '•\tGlasvezel aanwezig (abonnement niet inbegrepen)\n' +
              '•\tRijksmonument (bouwjaar 1939, renovatie 2022)\n' +
              '•\tBeschermd stadsgezicht Westbroekpark en Belgisch Park\n' +
              '•\tMet het naastgelegen Hotel De Plesman is een logeerkamer voor bezoek niet nodig\n' +
              '•\tAlbert Bar & Lounge en Restaurant Suus om de hoek\n' +
              '\n' +
              'LOCATIE\n' +
              'Perfect gelegen tussen natuur en stad: Sint Hubertuspark, Westbroekpark, Scheveningse Bosjes en Nieuwe Scheveningse Bosjes op loopafstand. Strand van Scheveningen op 2,5 km. Centrum en Centraal Station Den Haag binnen 3 km. Uitstekende bereikbaarheid via A4 en A12 richting Amsterdam/Schiphol, Rotterdam en Utrecht.\n' +
              '\n' +
              'GOED OM TE WETEN:\n' +
              '•\tbeschikbaar per 1 oktober 2025\n' +
              '•\tMinimale huurperiode 12 maanden\n' +
              '•\tWaarborgsom 2 maanden\n' +
              '•\tVoorschot servicekosten € 50,- per maand\n' +
              '•\tKosten nutsvoorzieningen, tv, internet, telefoon voor eigen rekening\n' +
              '•\tRoken en huisdieren niet toegestaan\n' +
              '•\tInkomenseis 3x de maandhuur als bruto-inkomen\n' +
              '•\tVoor maximaal 2 personen\n' +
              '•\tScreening kandidaten via NVM Woontoets\n' +
              '•\tJaarlijkse huurverhoging per 1 juli, voor het eerst per 1 juli 2026.\n' +
              '\n' +
              'SELECTIEPROCEDURE:\n' +
              'De uitnodiging voor een bezichtiging is op volgorde van binnenkomende berichten via Funda. Het uitgangspunt is dat degene die als eerste reageert, ook als eerste de kans heeft om de woning te bezichtigen. Houd er rekening mee dat bij veel aanmeldingen het kan voorkomen dat niet iedereen wordt uitgenodigd voor een bezichtiging. De keuze om de woning aan kandidaten te verhuren is geheel aan eigenaar woning. Onze werkwijze bij het aanbieden van een huurwoning ter voorkoming van woondiscriminatie krijgt u bij een bezichtiging of kunt u bij uw aanvraag voor een bezichtiging aanvragen. \n' +
              '\n' +
              '-------\n' +
              '\n' +
              'Interested in this apartment?\n' +
              'We kindly ask you to respond via Funda. Unfortunately, it is not possible to request a viewing by phone. We will do our best to reply as soon as possible. After you have expressed your interest, we will contact you. Please note that we are looking for the best match for the owner.\n' +
              '\n' +
              'Exceptional living in The Hague in Plesmanduin\n' +
              '\n' +
              'Living in a listed national monument with the comfort of new construction, in one of the greenest and most beautiful locations in The Hague.\n' +
              'This spacious loft-style apartment is fully single-level and accessible directly from the main entrance on the ground floor. Once inside, however, you are on the third residential floor, enjoying wide views over the water.\n' +
              '\n' +
              'LAYOUT\n' +
              'Representative secured main entrance with mailboxes, direct access to the apartment from the ground floor:\n' +
              '• Bright living/dining room of approx. 45 m² with large windows and access to a spacious southeast-facing balcony\n' +
              '• Ceiling height of 3.23 m in both living room and bedroom\n' +
              '• Luxury open kitchen with kitchen island, Quooker tap, fridge/freezer, multifunctional oven, induction cooktop with built-in extractor, and dishwasher\n' +
              '• Separate toilet, video intercom, and cloakroom\n' +
              '• Internal storage with washing machine and dryer connection\n' +
              '• Luxury bathroom with walk-in shower and double washbasin\n' +
              '• Spacious bedroom of approx. 13.4 m²\n' +
              '\n' +
              'COMMUNAL FACILITIES\n' +
              '• Secured bicycle storage with charging points for e-bikes\n' +
              '• Attractive communal courtyard garden as a green oasis\n' +
              '\n' +
              'SPECIAL FEATURES\n' +
              '• Delivered with flooring, curtains, lighting, and built-in kitchen appliances\n' +
              '• All rooms equipped with LED ceiling spotlights\n' +
              '• HR++ glazing and herringbone PVC flooring with underfloor heating/cooling\n' +
              '• Sustainable, gas-free, energy label A+\n' +
              '• Aquifer thermal energy storage (ATES) system for heating/cooling and hot water\n' +
              '• Central air ventilation with heat recovery\n' +
              '• Fiber optic connection available (subscription not included)\n' +
              '• Listed national monument (originally built in 1939, renovated in 2022)\n' +
              '• Located in the protected cityscape of Westbroekpark and Belgisch Park\n' +
              '• With the adjacent Hotel De Plesman, a guest room for visitors is not needed\n' +
              '• Albert Bar & Lounge and Restaurant Suus just around the corner\n' +
              '\n' +
              'LOCATION\n' +
              'Perfectly situated between nature and city: Sint Hubertus Park, Westbroekpark, Scheveningse Bosjes, and Nieuwe Scheveningse Bosjes within walking distance. The beach of Scheveningen only 2.5 km away. The city center and The Hague Central Station within 3 km. Excellent accessibility via A4 and A12 towards Amsterdam/Schiphol, Rotterdam, and Utrecht.\n' +
              '\n' +
              'GOOD TO KNOW\n' +
              '• Available from October 1, 2025\n' +
              '• Minimum rental period 12 months\n' +
              '• Security deposit: 2 months’ rent\n' +
              '• Advance payment service costs € 50 per month\n' +
              '• Utilities, TV, internet, and telephone at tenant’s own expense\n' +
              '• Smoking and pets not allowed\n' +
              '• Income requirement: gross monthly income at least 3x the rent\n' +
              '• Suitable for a maximum of 2 persons\n' +
              '• Tenant screening via NVM Woontoets\n' +
              '• Annual rent increase on July 1, first on July 1, 2026\n' +
              '\n' +
              'SELECTION PROCEDURE\n' +
              'Viewings are scheduled in order of incoming requests via Funda. The principle is that the first to respond will also be the first to have the opportunity to view the apartment. Please note that in case of high demand, not all applicants may be invited for a viewing. The final decision to rent out the apartment is entirely at the discretion of the owner. Our working method regarding the allocation of rental properties to prevent housing discrimination will be provided during the viewing or upon request when applying for a viewing.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.523Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.540Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.556Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.571Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.587Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.603Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.620Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.639Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.657Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.675Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.693Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.711Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030698_tv6w8',
  source: 'funda',
  duration: '12ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Snelliusstraat 28 te Den Haag - Duinoord \n' +
              '\n' +
              'Totaal luxe gerenoveerd 4-kamer appartement 107m2 - 2 balkons - Energielabel A.\n' +
              '\n' +
              'See English text below. \n' +
              '\n' +
              'In de geliefde wijk Duinoord, waar statige gevels en lommerrijke straten de sfeer bepalen, bevindt zich dit unieke appartement aan de Snelliusstraat 28. Het huis, oorspronkelijk gebouwd in 1913, is in 2025 volledig gerenoveerd en voorzien van een energielabel A. Daarmee combineert het de karaktervolle charme van weleer met het wooncomfort en de duurzaamheid van nu.\n' +
              '\n' +
              'Wanneer je de woning betreedt, valt direct de royale hal op die toegang biedt tot alle vertrekken. De ruime woonkamer baadt in het licht en staat in open verbinding met een prachtige designkeuken. Deze keuken, uitgerust met een royaal kook- en spoeleiland en hoogwaardige inbouwapparatuur, vormt het hart van de woning en nodigt uit tot gezellig koken en borrelen met vrienden of familie. Vanuit de woonkamer loop je zo door naar het ruime, overdekte terras op het zuiden – een heerlijke plek om van de zon te genieten, van de vroege lente tot de nazomer.\n' +
              '\n' +
              'De woning beschikt over drie slaapkamers. Twee royale kamers waarvan de master bedroom extra bijzonder is dankzij het eigen balkon op het zuiden. Daarnaast is er nog een kleinere kamer, ideaal als werkkamer, kinderkamer of inloopkast. De badkamer ademt luxe: een ruim ligbad, een inloopdouche en een dubbele wastafel maken dit een plek om even helemaal tot rust te komen. Het moderne toilet bevindt zich apart in de gang.\n' +
              '\n' +
              'Dit appartement biedt de perfecte balans tussen stijl, ruimte en comfort. En dat alles op een van de mooiste plekken van Den Haag. Duinoord staat bekend om zijn monumentale uitstraling, het Sweelinckplein om de hoek, en natuurlijk de gezellige Frederik Hendriklaan met zijn winkels, cafés en restaurants. Het strand en het stadscentrum zijn binnen enkele minuten bereikbaar, zowel per fiets als met het openbaar vervoer.\n' +
              'Kortom: een woning die niet alleen instapklaar is, maar ook alles in zich heeft om jarenlang met plezier in te wonen – duurzaam, comfortabel en stijlvol.\n' +
              '\n' +
              '(Magnetron boven de oven wordt nog geplaatst komende week). \n' +
              '\n' +
              'Bijzonderheden:\n' +
              'Toplocatie \n' +
              'Volledig gerenoveerd in 2025\n' +
              'Energielabel A (zeer energiezuinig)\n' +
              'Luxe open keuken met kook- en spoeleiland\n' +
              'Royale badkamer met ligbad, inloopdouche en dubbele wastafel\n' +
              'Drie slaapkamers (twee ruim, één compact)\n' +
              'Master bedroom met eigen balkon op het zuiden\n' +
              'Groot overdekt terras op het zuiden\n' +
              'Modern separaat toilet\n' +
              'Gelegen op de eerste etage\n' +
              'Woonoppervlak ca. 110 m²\n' +
              'Gelegen in het populaire en karakteristieke Duinoord\n' +
              'Per direct beschikbaar.\n' +
              '\n' +
              'Snelliusstraat 28 – The Hague - Duinoord\n' +
              '\n' +
              'Fully renovated luxury 4-room apartment – 107 m² – 2 balconies – Energy label A\n' +
              '\n' +
              'In the sought-after neighborhood of Duinoord, known for its stately façades and leafy streets, lies this unique apartment at Snelliusstraat 28. Originally built in 1913, the property was completely renovated in 2025 and upgraded to energy label A, combining the timeless charm of early 20th-century architecture with modern comfort and sustainability.\n' +
              '\n' +
              'Upon entering, you are welcomed by a spacious hallway that connects all rooms. The bright living room flows seamlessly into a stunning designer kitchen. Equipped with a generous cooking and rinsing island and high-quality built-in appliances, the kitchen is the true heart of the home—perfect for cooking and entertaining with friends or family. From the living room, you step directly onto the large covered south-facing terrace, an ideal spot to enjoy the sun from early spring until late autumn.\n' +
              '\n' +
              'The apartment offers three bedrooms. Two spacious rooms, including the master bedroom with its own private south-facing balcony, and a smaller room—ideal as a home office, nursery, or walk-in closet. The luxurious bathroom features a bathtub, a walk-in shower, and a double washbasin, creating a serene spa-like atmosphere. A modern separate toilet is located in the hallway.\n' +
              '\n' +
              'This apartment strikes the perfect balance between style, space, and comfort—set in one of The Hague’s most desirable neighborhoods. Duinoord is renowned for its monumental character, the nearby Sweelinckplein, and the lively Frederik Hendriklaan with its wide selection of shops, cafés, and restaurants. Both the city center and the beach are just minutes away by bike or public transport.\n' +
              '\n' +
              'In short, a home that is not only move-in ready but also offers everything needed for years of enjoyable living—sustainable, comfortable, and stylish.\n' +
              '\n' +
              '(The microwave above the oven will be installed next week)\n' +
              '\n' +
              'Key Features:\n' +
              'Prime location\n' +
              'Fully renovated in 2025\n' +
              'Energy label A (very energy efficient)\n' +
              'Luxury open kitchen with cooking and rinsing island\n' +
              'Spacious bathroom with bathtub, walk-in shower, and double washbasin\n' +
              'Three bedrooms (two spacious, one compact)\n' +
              'Master bedroom with private south-facing balcony\n' +
              'Large covered south-facing terrace\n' +
              'Modern separate toilet\n' +
              'Situated on the first floor\n' +
              'Living area approx. 110 m²\n' +
              'Located in the popular and characteristic Duinoord neighborhood\n' +
              'Available immediately.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.710Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.729Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.747Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030734_rxbcf',
  source: 'funda',
  duration: '12ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Op de hoek van de Van der Hooplaan en de Sportlaan ligt een markant appartementencomplex met o.a. een ruim 3-kamerhoekappartement (oppervlakte circa 107 m2) met balkon Gelegen op de 11e verdieping heeft u prachtig uitzicht over Amstelveen. In de afgesloten onderbouw bevindt zich een eigen (fietsen)berging en een eigen parkeerplaats. \n' +
              '\n' +
              'Indeling\n' +
              'Afgesloten centrale entree met bellenbord en brievenbussen. Met de liften of trap is het appartement bereikbaar.\n' +
              '\n' +
              'Indeling appartement:\n' +
              'Ruime entree met garderobe, videofoon, meterkast en toegang tot alle vertrekken. Toilet met wandcloset en wastafelmeubel met spiegel. Ruime woonkamer met toegang tot het balkon (3,25 x 2,60) op het westen. Open keuken o.a. voorzien van een apothekerskast, Amerikaanse koelkast, ingebouwde koelkast, vaatwasmachine, keramische kookplaat, afzuigkap en combi-magnetron. \n' +
              'Masterbedroom, in wit/grijs uitgevoerde badkamer met ligbad, separate dubbele douche in cabine en dubbel wastafelmeubel, 2e slaapkamer en de technische ruimte (tevens de plaats van de wasmachine en de c.v.-combiketel).\n' +
              '\n' +
              'Het appartementencomplex is gelegen op de hoek Van der Hooplaan/Sportlaan. Aan de Van der Hooplaan zijn de winkels voor de dagelijkse boodschappen beschikbaar en op relatief korte afstand bevindt zich het “Stadshart" van Amstelveen met diverse luxe en grotere speciaalzaken, alsmede de stadsschouwburg, het Cobramuseum, de openbare bibliotheek en de volksuniversiteit. Tevens treft u in de nabijheid sportvoorzieningen en scholen (o.a. de Internationale School). Goede bereikbaarheid met openbaar vervoer (bus- en sneltramverbindingen) en de uitvalswegen (A-9, A-10 en A-4).\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '• De huurprijs is inclusief privé parkeerplaats en servicekosten, maar exclusief gas/water/elektra;\n' +
              '• Het appartement wordt gestoffeerd en gedeeltelijk gemeubileerd verhuurd;\n' +
              '• Waarborgsom 2 maanden huur;\n' +
              '• Oplevering vanaf circa 1 oktober 2025.\n' +
              '\n' +
              'Criteria waar rekening mee gehouden wordt bij toewijzing:\n' +
              '1. Reactiesnelheid\n' +
              'In de basis geldt: wie het eerst komt, die het eerst maalt. Het initiatief om op een aangeboden woning te reageren ligt bij kandidaat-huurders. In geval van veel reacties kan het dus voorkomen dat bepaalde kandidaat-huurders niet meer worden uitgenodigd voor een bezichtiging en worden afgewezen.\n' +
              '2. Bron van inkomen/soort arbeidsovereenkomst\n' +
              'Een arbeidsovereenkomst voor onbepaalde tijd met een van goede naam en faam bekend staande werkgever heeft de voorkeur. Dit geeft opdrachtgever de meeste financiële zekerheid. Tijdelijke arbeidsovereenkomsten, (recent) zelfstandig ondernemerschap en andere vormen van inkomstenbronnen zoals alimentatie, een stagevergoeding, een garantstelling door derden, een erfenis, huurinkomsten etc., kunnen wel degelijk ook leiden tot toewijzing van een huurwoning, maar dat is maatwerk.\n' +
              '3. Hoogte van het inkomen\n' +
              'Na een eventuele minimale inkomenseis heeft het hoogste (gezamenlijke)inkomen de voorkeur. Dit geeft opdrachtgever de meeste financiële zekerheid ten aanzien van de nakoming van de financiële verplichtingen uit de huurovereenkomst.\n' +
              '4. Gedegen en controleerbaar positief huurverleden\n' +
              'Een kandidaat-huurder met een positieve en verifieerbare verhuurdersverklaring heeft de voorkeur. Kandidaat-huurders zonder aantoonbaar huurverleden is maatwerk, want dat vergt nadere informatie of onderzoek.\n' +
              '5. Een positieve screening van ID bewijs en solvabiliteit\n' +
              'Een kandidaat-huurder kan een woning alleen toegewezen krijgen onder het voorbehoud dat de identiteit kan worden geverifieerd en de financiële draagkracht in orde is. Om dit te toetsen voert de verhurende makelaar een grondige screening uit. Een onderdeel van deze screening is een handmatige controle op echtheidskenmerken van het ID uitgevoerd. De uitslag van deze screening kan leiden tot een afwijzing (ook na een initiële toewijzing).\n' +
              '6. Samenstelling huishouden\n' +
              'Het is belangrijk dat er een passende bewonerssamenstelling geldt per woning. Dit om overlast en schade te voorkomen, maar ook om bijvoorbeeld overbewoning van woningen te voorkomen. Er zal daarom bij toewijzing rekening worden gehouden met de bewonerssamenstelling per woning. Afhankelijk van het type woning hebben bepaalde samenstellingen de voorkeur. Dit is maatwerk per woning.\n' +
              '7. Passendheid in (de omgeving van) de aangeboden woning\n' +
              'In sommige gevallen kunnen de ligging van de huurwoning of de eigenschappen daarvan ertoe leiden dat bepaalde kandidaat-huurders in beginsel meer geschikt zijn dan anderen.\n' +
              '8. Gunning opdrachtgever\n' +
              'Voor iedere aangeboden woning geldt dat opdrachtgever uiteindelijk de keuze maakt tussen de kandidaat-huurders. De verhurende makelaar heeft op de uiteindelijke keuze geen invloed.\n' +
              '\n' +
              "**Deze informatie is met de grootst mogelijke zorg samengesteld. Toch kunnen wij niet altijd voorkomen dat de informatie enigszins afwijkt van hetgeen je in of rond de woning ziet of hebt gezien. Dit kan met name gelden voor de brochure tekst, foto's de plattegronden en maatvoeringen. Hieraan kunnen dan ook geen rechten worden ontleend.**",
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.746Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.766Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.783Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.798Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030788_mxv1w',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: "Welkom in ''De Dirigent'' – een stijlvol nieuwbouwcomplex in het hart van Naaldwijk \n" +
              '\n' +
              'Dit royale appartement (bouwjaar 2024) van ca. 124 m2 is gelegen op de tweede verdieping en biedt alles wat u mag verwachten van modern wonen: ruimte, licht, hoogwaardige afwerking en uitstekende energieprestaties. Gelegen op een centrale locatie, met alle voorzieningen op loopafstand, combineert deze woning het gemak van het dorp met het comfort van een gloednieuw thuis. \n' +
              '\n' +
              'Dit moderne appartement ontbreekt werkelijk aan niets. De riante woonkamer met open keuken vormt een sfeervol en licht middelpunt van de woning, met directe toegang tot een zonnig balkon van ca. 12 m2 op het zuidwesten – ideaal voor lange zomeravonden. Met twee ruime slaapkamers, een ruime badkamer en een apart toilet is het appartement perfect ingedeeld. Duurzaamheid staat centraal: energielabel A+++, een efficiënte warmtepomp, HR-glas en volledige vloerverwarming zorgen voor een aangenaam binnenklimaat én lage energielasten. Daarnaast beschikt de woning over een inpandige berging en een privéparkeerplaats onder het gebouw. Voorzieningen zoals winkels, supermarkten en horeca bevinden zich tevens op loopafstand, waardoor het dagelijks leven hier erg comfortabel is. \n' +
              '\n' +
              'Kortom: een instapklaar appartement waar aan alles is gedacht. \n' +
              '\n' +
              'Indeling: \n' +
              'Op de begane grond vindt u een gezamenlijke en sfeervolle entree met liften en trappenhuis welke toegang verlenen tot het appartement op de derde verdieping. Verder vindt u op de begane grond toegang naar de (eigen) berging en de afgesloten parkeergarage.  \n' +
              '\n' +
              'Entree:  \n' +
              'Zodra u de voordeur opent, komt u direct terecht in een uitnodigende en sfeervolle hal. De combinatie van een strak gestuukt plafond, wanden voorzien van warmbruin behang met een verfijnde print en subtiele wandverlichting zorgt direct voor een aangename ambiance/eerste indruk. De keurige laminaatvloer sluit hier naadloos op aan en onderstreept de verzorgde afwerking. Deze entree weerspiegelt perfect de kwaliteit en uitstraling van de rest van het appartement.  \n' +
              '\n' +
              'Vanuit de hal heeft u onder andere toegang tot een apart toilet, de technische ruimte, de badkamer en twee slaapkamers.  \n' +
              '\n' +
              'Het aparte toilet met fonteintje en de badkamer zijn volledig betegeld in een warme, bruine kleur die zorgt voor een strakke uitstraling. De badkamer is uitgerust met een stijlvol dubbel wastafelmeubel, een ruime inloopdouche, een tweede toilet en een handdoekradiator. Dankzij de vloerverwarming is het een aangename ruimte om de dag te beginnen of juist af te sluiten.  \n' +
              '\n' +
              'In de technische ruimte vindt u de aansluitingen voor de wasmachine en droger. De twee slaapkamers zijn ruim van formaat en bieden dankzij de grote raampartijen een royaal uitzicht. Vanuit de hal bereikt u eveneens de bijkeuken, die doorgang biedt naar de riante woonkamer met open keuken.  \n' +
              '\n' +
              'De keuken is volledig ingericht en is een ware eye-catcher. Ze bestaat uit een stijlvolle bruine kastenwand en een kookeiland met een licht werkblad, wat zorgt voor een mooi contrast. De keuken is voorzien van alle gemakken, waaronder een inductiekookplaat met geïntegreerde afzuiging, Quooker- en spa blauw waterkraan, oven, magnetron, koelkast, vriezer en vaatwasser. Dankzij het overstek van het kookeiland is er plek voor twee barstoelen, ideaal voor een informeel diner of een gezellige borrel. Een echte leefkeuken waar koken en samenzijn centraal staan. \n' +
              '\n' +
              'De ruime woonkamer voelt extra licht en open dankzij de grote raampartijen, die niet alleen veel daglicht binnenlaten, maar ook een prachtig groen uitzicht bieden op de karakteristieke kerk aan het Wilhelminaplein. Via openslaande deuren stapt u zo het balkon op, dat met ca. 12 m2 royaal van formaat is en gunstig ligt op het zuidwesten. Hier geniet u van de zon en het uitzicht – een heerlijke plek om te ontspannen/genieten. \n' +
              '\n' +
              'Wonen in De Dirigent betekent genieten van comfort en luxe met alles binnen handbereik. Dit duurzame complex, gelegen aan de Verdilaan in het hart van Naaldwijk, is niet alleen modern en stijlvol, maar ook milieuvriendelijk. Het gebouw is uitgerust met hoogwaardige isolatie, zonnepanelen en warmtepompen, wat zorgt voor aanzienlijke energiebesparing. Groene dakvlakken dragen bij aan waterbeheer, verkoeling en biodiversiteit, waardoor het zowel een comfortabele als verantwoorde keuze is. \n' +
              '\n' +
              `"De Dirigent'' bevindt zich op een prachtige locatie, op loopafstand van het levendige Wilhelminaplein en tal van winkels. Medische voorzieningen, zoals een huisarts en apotheek, zijn dichtbij, en (basis)scholen en hoger onderwijs zijn uitstekend bereikbaar. Voor openbaar vervoer is het nabijgelegen busstation ideaal, met snelle verbindingen naar omliggende gemeenten, evenals naar Delft, Den Haag en Rotterdam. \n` +
              '\n' +
              'Bijzonderheden:  \n' +
              "- Luxe appartement op de tweede verdieping, gelegen in nieuwbouw appartementencomplex ''De Dirigent'';  \n" +
              '- Bouwjaar 2024;  \n' +
              '- Betonnen constructievloer;  \n' +
              '- Energielabel A+++;  \n' +
              '- Voorzien van een warmtepomp;  \n' +
              '- Volledig voorzien van vloerverwarming;  \n' +
              '- Woonoppervlakte van ca. 124 m2 (indicatieve meting, conform NEN2580 meetinstructie); \n' +
              '- Badkamer voorzien van o.a. toilet en apart toilet aanwezig;  \n' +
              '- Zonnig balkon van ca. 12 m2, gelegen op het zuidwesten;  \n' +
              '- Inpandige berging, voorzien van elektra;  \n' +
              '- Huurprijs is excl. servicekosten en nutsvoorzieningen;  \n' +
              '- Servicekosten bestaan uit een maandelijkse bijdrage van € 135,- als huurdersgedeelte in de VvE bijdrage; \n' +
              '- Contracten voor verbruik/levering van water en elektra worden rechtsreeks door huurder afgesloten; \n' +
              '- Huurovereenkomst conform model ROZ aangevuld met gebruikelijke artikelen ten aanzien van gebruik; \n' +
              '- Jaarlijkse huurprijs aanpassing conform CPI; \n' +
              '- Inkomensnorm: 3 maal de maandelijkse huurprijs (bruto inkomen), partner mag voor 100% worden meegeteld; \n' +
              '- VvE bijdrage eigen overdekte parkeerplaats € 24,22 p.m;  \n' +
              '- Oplevering in overleg.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.798Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.815Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.829Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.845Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030835_6rx4p',
  source: 'funda',
  duration: '9ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Immobilia presenteert een stijlvol omgetoverde woonboerderij tot comfortabele en instapklare woning. De woning beschikt over een ruime living met een luxe open keuken. De woning is gelegen in de populaire, kindvriendelijke wijk Rokkeveen. Op loopafstand van het grootste park van deze wijk het Burgemeester Hoekstrapark. In de directe omgeving tref je alle denkbare voorzieningen zoals het moderne winkelcentrum Rokkeveen met wekelijks de markt, diverse sportfaciliteiten en meerdere basisscholen.\n' +
              '\n' +
              'Indeling:\n' +
              'Entree via de tuin in de woonkamer. Royale living van maar liefst 70m2 met veel lichtinval, mede door de dubbele openslaande deuren. Deze ruimte ademt de authentieke boerderijstijl door de hoge plafonds met spanten en de luxe keuken in boeren stijl. De open keuken is luxe uitgevoerd, biedt voldoende ruimte en is voorzien van een stenen aanrechtblad en inbouwapparatuur, te weten een 4-pits gasfornuis, afzuigkap, oven, magnetron, vaatwasser en koel-/vriescombinatie. \n' +
              '\n' +
              'De woning beschikt over een drietal (slaap)kamers, waarvan er twee met elkaar zijn verboden. Deze slaapkamers meten ca. 10,5 en 12m2. Ideaal als kinder- of werkkamer. De derde slaapkamer bevindt zich aan de zijkant van de woning en is ca. 11m2. Alle slaapkamers zijn charmant vormgegeven met een hoge schuine kap, helemaal in boerderijstijl. De slaapkamers zijn voorzien van een laminaatvloer.\n' +
              '\n' +
              'De moderne badkamer is uitgevoerd met een dubbel wastafelmeubel, een luxe regendouche, designradiator en mechanische ventilatie. De wanden zijn betegeld met een witte wandtegel en de vloer met een antracietkleurige tegel. Het aparte toilet is uitgevoerde in een zelfde stijl als de badkamer en is uitgevoerd met een hangend toilet en een fonteintje.\n' +
              '\n' +
              'De achtertuin in gelegen op het zuidwesten, de royale zijtuin op het westen. \n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Sfeervolle, vrij gelegen woonboerderij.\n' +
              '- Woonoppervlak van ca. 128m2.\n' +
              '- Ruime living met luxe open keuken.\n' +
              '- Gelegen in een groene, kindvriendelijke wijk.\n' +
              '- Tuin aan de zijkant en achterkant van de woning. \n' +
              '- Entree van de woning aan de achterzijde.\n' +
              '\n' +
              'Huurdersprofiel\n' +
              'Voor deze woning zijn wij op zoek naar nette en betrouwbare huurders die goed passen bij de woning en de omgeving.\n' +
              '\n' +
              'Geschikte huurdersprofielen zijn o.a.:\n' +
              '- Expats die tijdelijk in Nederland verblijven.\n' +
              '- Max. 2 buitenlandse studenten die een rustige en nette woonruimte zoeken.\n' +
              '- Alleenstaanden of ouders in scheiding met één minderjarig kind die behoefte hebben aan een stabiele woonplek.\n' +
              '\n' +
              'Belangrijk:\n' +
              '- Huisdieren en roken zijn in deze woning helaas niet toegestaan.\n' +
              '\n' +
              'Huurcondities:\n' +
              '- Per direct beschikbaar.\n' +
              '- Huisdieren zijn niet toegestaan in de woning.\n' +
              '- Huurprijs is excl. gas, water, elektra, internet en tv. Contracten dienen zelf door de huurder bij de betreffende bedrijven afgesloten te worden.\n' +
              '- Huurprijs is incl. gedeeltelijke stoffering.\n' +
              '- Huurprijsherziening: de huurprijs zal jaarlijks met een door het Centraal bureau voor de statistiek (CBS) vastgesteld percentage worden verhoogd, volgens het gestelde in de algemene bepalingen huurovereenkomst woonruimte.\n' +
              '- Waarborgsom 2 maanden huur.\n' +
              '\n' +
              'Om in aanmerking te komen voor deze woning zijn de eisen als volgt:\n' +
              '- Aantoonbaar inkomen uit werk. In de regel dient je bruto maandsalaris minimaal drie maal de maandhuur te bedragen.\n' +
              '\n' +
              'Benodigde documenten bij interesse:\n' +
              '- Een kleurenkopie van een geldig legitimatiebewijs waarbij het BSN-nummer onzichtbaar is gemaakt.\n' +
              '- Een kleurenkopie van je bankpas.\n' +
              '- Uittreksel uit de gemeentelijke basis administratie persoonsgegevens.\n' +
              '- Een kopie van je arbeidsovereenkomst.\n' +
              '- Een werkgeversverklaring niet ouder dan drie (3) maanden.\n' +
              '- Drie recente salarisstroken of inkomsten studiefinanciering.\n' +
              '- Je meest recente jaaropgave of IB60 formulier van personen boven de 18.\n' +
              '- Meest recente bankafschrift van de afgelopen drie (3) maanden waarop de salarisstorting en de huurbetaling of hypotheekbetaling te zien is.\n' +
              '- Een verklaring van je huidige huisbaas/verhuurder dat je een goede huurder bent en geen huurschulden of achterstanden hebt.\n' +
              '\n' +
              'Indien je (zelfstandig) ondernemer bent, dien je naast bovengenoemde documenten ook deze aanvullende stukken ook aan te leveren:\n' +
              '- Een kleuren kopie van een geldig identiteitsbewijs van de bestuurder van de onderneming, waarbij je foto en BSN-nummer zijn afgeschermd;\n' +
              '- Een kleurenkopie van je bankpas.\n' +
              '- Een uittreksel van het handelsregister van de Kamer van Koophandel niet ouder dan drie weken;\n' +
              '- Btw-nummer;\n' +
              '- Goedgekeurde jaarstukken en een actuele balans, winst- en verliesrekening.\n' +
              '- Meest recente bankafschrift van de afgelopen drie maanden waarop de huurbetaling of hypotheekbetaling te zien is.\n' +
              '- Een verklaring van je huidige verhuurder dat je een goede huurder bent en geen huurschulden of achterstanden hebt (indien van toepassing).\n' +
              '\n' +
              'Toelichting NEN 2580:\n' +
              'De meetinstructie is gebaseerd op NEN 2580. De meetinstructie is bedoeld om een meer eenduidige manier van meten toe te passen voor het geven van een indicatie van de gebruiksoppervlakte. De meetinstructie sluit verschillen in meetuitkomsten niet volledig uit, door bijvoorbeeld interpretatieverschillen, afrondingen of beperkingen bij het uitvoeren van de meting. Bijgevoegde plattegronden zijn ingemeten volgens de meetinstructie.\n' +
              '\n' +
              'Alle verstrekte informatie moet beschouwd worden als een uitnodiging tot het doen van een bod of om in onderhandeling te treden. Er kunnen geen rechten worden ontleend aan deze informatie.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.844Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.860Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.876Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.892Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030881_12qqs',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: '*This property is listed by a MVA Certified Expat Broker*\n' +
              '\n' +
              'English translation below\n' +
              '\n' +
              'Zeer hoogwaardig gerenoveerd 3-dubbel bovenhuis van 185 m2 met riant dakterras in een van de mooiste straten van Amsterdam Zuid\n' +
              '\n' +
              'DE OMGEVING\n' +
              '\n' +
              'Het appartement is gelegen tussen het Minervaplein en de Beethovenstraat. Op loopafstand bevindt zich een gevarieerd aanbod winkels en toprestaurants. Voor de dagelijkse boodschappen kunt u terecht op Beethovenstraat of het Olympiaplein. De Cornelis Schuytstraat, Vondelpark en de Zuidas liggen pal om de hoek. In deze kindvriendelijke buurt bevinden zich behalve de British School, diverse basis- en middelbare scholen.\n' +
              'De woning is uitstekend bereikbaar per auto, openbaar vervoer en ligt nabij uitvalswegen A2, A4 en A10. De Zuidas en NS-station Zuid liggen op 5 minuten lopen.\n' +
              '\n' +
              'HET APPARTEMENT\n' +
              '\n' +
              'Overdekt portiek op de begane grond met toegang tot de woning. De toegangsdeur is voorzien van elektrische 3-puntssluiting en video-intercom. Op de begane grond bevindt zich een ruime hal welke uitstekend geschikt is voor het stallen van een kinderwagen.\n' +
              '\n' +
              'De dubbele trap brengt u bij de woonverdieping op tweede verdieping. In de hal vindt u een ruim toilet met fontein en een garderobekast. Via de fraaie dubbele deur (uitgevoerd in zwart  staal) betreedt u de woonkamer. Deze royale en lichte woonkamer met erker aan de voorzijde, is ca. 46 m2 groot en is voorzien van openslaande deuren naar het zonnige balkon (westen). Verder is de woonkamer uitgerust met sfeervolle gashaard. \n' +
              '\n' +
              'De halfopen keuken bevindt zich aan de achterzijde. Deze luxe greeploze inbouwkeuken met composiet werkblad is voorzien van diverse inbouw-apparatuur waaronder een koelkast, vriezer, vaatwasser, inductiekookplaat met afzuiging, combi-oven/magnetron, stoomoven en een Quooker. Aan de voorzijde van de tweede verdieping bevindt zich een ruime slaapkamer welke eveneens geschikt als werk of logeerkamer. \n' +
              '\n' +
              'Via het ruime trappenhuis betreedt u de overloop van de derde verdieping. Op deze verdieping vindt u maar liefst vier slaapkamers welke allen zijn uitgerust met inbouwkasten en twee badkamers.\n' +
              '\n' +
              'De master slaapkamer is gelegen aan de voorzijde en beschikt over een eigen badkamer welke ook gedeeld kan worden met de slaapkamer aan de achterzijde. Deze riante badkamer is ingericht met een ligbad met handdouche, inloopdouche, wastafelmeubel, een spiegel met verlichting, handdoekradiator en een tweede toilet. De twee kleinere slaapkamers zijn gelegen, één aan de voorzijde en één aan de achterzijde. Op de overloop is de tweede badkamer welke is uitgerust met een wastafelmeubel, spiegelkast met verlichting, inloopdouche en een handdoekradiator. Het separate derde toilet en de wasruimte zijn eveneens te bereiken vanuit de overloop. \n' +
              '\n' +
              'Via de vast trap bereikt u de kapverdieping. Deze zolder beschikt over een slaapkamer en ter plaatse van de trap en de toegang tot het dakterras bevindt zich een compacte pantry met koelkast.\n' +
              '\n' +
              'Het dakterras van 37 m2 groot biedt de gehele dag zon en is verder uitgerust met elektra en watervoorziening.\n' +
              '\n' +
              'HET COMPLEX\n' +
              '\n' +
              'Het complex is gebouwd omstreeks 1928 naar ontwerp van K. van Geijn & H.J.A. Bijlard (Architectenbureau Ed. Cuypers, Amsterdam). Het maakt onderdeel uit van het stedenbouwkundige uitbreidingsplan van Berlage. Het prestigieuze Plan Berlage is net als de typerende architectonische bouwstijl, kenmerkend voor de architectuur van de Amsterdamse School.\n' +
              '\n' +
              'BIJZONDERHEDEN\n' +
              '\n' +
              '- Hoogwaardig gerenoveerd in 2021\n' +
              '- Gehele woning (behoudens natte ruimtes) voorzien van een prachtige lamelparketvloer\n' +
              '- Volledig geisoleerd\n' +
              '- Oplevering/aanvaarding: in overleg\n' +
              '- Minimale huurperiode: 1 jaar\n' +
              '- Waarborgsom: 2 maanden\n' +
              '- Voorbehoud goedkeuring verhuurder\n' +
              '- GEEN delers, studenten en/of groepsbewoning\n' +
              '------------------------------------------------------------------\n' +
              '*This property is listed by a MVA Certified Expat Broker*\n' +
              '\n' +
              'Very high-quality renovated 3-double upper house of 185 m2 with a spacious roof terrace in one of the most beautiful streets of Amsterdam South\n' +
              '\n' +
              'AREA\n' +
              '\n' +
              'The apartment is located between Minervaplein and Beethovenstraat. There is a varied range of shops and top restaurants within walking distance. For daily shopping you can go to Beethovenstraat or Olympiaplein. The Cornelis Schuytstraat, Vondelpark and the Zuidas are just around the corner. In addition to the British School, there are various primary and secondary schools in this child-friendly neighbourhood.\n' +
              'The house is easily accessible by car, public transport and is located near highways A2, A4 and A10. The Zuidas and South railway station are a 5-minute walk away.\n' +
              '\n' +
              'THE APARTMENT\n' +
              '\n' +
              'Porch on the ground floor with access to the house. The entrance door is equipped with an electric 3-point lock and video intercom. On the ground floor there is a spacious hall which is ideal for storing a stroller.\n' +
              '\n' +
              'The double staircase takes you to the living area on the second floor. In the hall you will find a spacious toilet with a washbasin and a wardrobe. You enter the living room through the beautiful double doors (made of black steel). This spacious and bright living room with bay window at the front is approx. 46 m2 and has patio doors to the sunny balcony (west). Furthermore, the living room is equipped with an attractive gas fireplace.\n' +
              '\n' +
              'The semi-open kitchen is located at the rear. This luxurious handle less built-in kitchen with composite worktop is equipped with various built-in appliances including a refrigerator, freezer, dishwasher, induction hob with extractor, combined oven / microwave, steam oven and a Quooker. At the front of the second floor is a spacious bedroom which is also suitable as a work or guest room.\n' +
              '\n' +
              'Via the spacious staircase you enter the hallway of the third floor. On this floor you will find no less than four bedrooms, all equipped with fitted wardrobes and two bathrooms\n' +
              '\n' +
              'The master bedroom is located at the front and has its own bathroom which can also be shared with the bedroom at the rear. This spacious bathroom has a bath with hand shower, walk-in shower, washbasin, a mirror cabinet with lighting, towel radiator and a second toilet. The two smaller bedrooms are located, one at the front and one at the rear. On the hallway is the second bathroom which is equipped with a washbasin, mirror cabinet with lighting, walk-in shower and a towel radiator. The separate third toilet and laundry room can also be reached from the hallway.\n' +
              '\n' +
              'You reach the top floor via the fixed staircase. This attic has a bedroom and at the location of the stairs and the access to the roof terrace is a compact pantry with refrigerator and a sink\n' +
              '\n' +
              'The 37 m2 roof terrace offers sun all day long and is also equipped with electricity and water.\n' +
              '\n' +
              'THE COMPLEX\n' +
              '\n' +
              "The complex was built around 1928 to a design by K. van Geijn & H.J.A. Bijlard (Architectural firm Ed. Cuypers, Amsterdam). It is part of Berlage's urban expansion plan. The prestigious Plan Berlage is, just like the typical architectural style, characteristic of the architecture of the Amsterdam School.\n" +
              '\n' +
              'PARTICULARITIES\n' +
              '\n' +
              '- Renovated to a high standard in 2021\n' +
              '- Entire house (except wet rooms) with a beautiful parquet floor\n' +
              '- Completely insulated\n' +
              '- Delivery / acceptance: in consultation\n' +
              '- Minimum rental period: 1 year\n' +
              '- Deposit: 2 months\n' +
              '- Under reservation for approval of the landlord\n' +
              '- NO sharers, students and / or group accommodation',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.891Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.908Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.923Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030913_fzav3',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Zie onder voor de Nederlandse tekst!!\n' +
              '\n' +
              'Beautiful fully renovated (energylabel A++) 2-bedroom ground floor apartment with huge garden at Houtplein, Haarlem\n' +
              '\n' +
              'This extraordinary apartment is a gem in the heart of Haarlem! Located on the stately Houtplein, this fully renovated apartment (using the highest quality materials) offers a unique opportunity for comfortable and sustainable living. Everything has been thought of here: from style and luxury to energy efficiency.\n' +
              '\n' +
              'Upon entering, you will be surprised by the impressive entrance, where you will experience a touch of grandeur from times gone by. High ceilings with a chandelier, modern wall panels and stylish wooden finishes around the meter cupboards set the tone. You reach this unique ground floor apartment via the staircase.\n' +
              '\n' +
              'Once inside, you will immediately notice how light and spacious the apartment is thanks to the high ceilings and well-thought-out layout. At the front is the attractive living room, which flows seamlessly into a sleek, modern kitchen with a central cooking island. This kitchen is the perfect place to set up as a kitchen, making it the central place in this beautiful apartment.\n' +
              '\n' +
              'The wide hallway, which is accessible from both the living room and the kitchen, offers a chic view of the backyard. At the rear are two spacious bedrooms with large windows, so you wake up every morning with a view of the beautifully landscaped garden.\n' +
              '\n' +
              'The bathroom has generous dimensions, a bath, large double washbasin with illuminated mirror, walk-in shower and a second toilet. Stylish sand-coloured tiles and top-quality sanitary ware complete the picture.\n' +
              '\n' +
              "The garden: a unique spot in the centre of Haarlem, located on the sunny southwest, is something we don't often see. With a surface area of ??no less than 140 m², this is the place to relax. The garden is fully landscaped and has a veranda, so you can enjoy the outdoors all year round.\n" +
              '\n' +
              'Haarlem combines the charm of a historic city with the comfort of modern facilities. Where you often have to brave the hustle and bustle and chaos in Amsterdam, Haarlem offers peace, space and character. Moreover, the city is super centrally located:\n' +
              '- You can reach Amsterdam Central Station by train within 15 minutes. - Schiphol is easily accessible by both car and public transport.\n' +
              '- The dunes and the beach are within cycling distance.\n' +
              '\n' +
              'Haarlem has an excellent range of restaurants, shops and cultural hotspots, ranging from cozy cafes to Michelin-star restaurants and from unique boutiques to large chains. You can also enjoy a friendly atmosphere, green parks and excellent connections to the Randstad.\n' +
              '\n' +
              'An apartment like this rarely comes on the market. With its central location, luxurious finish and energy neutrality, this is a home that one normally only expects in Amsterdam, but without the hustle and bustle. This is your chance to combine the best of both worlds: the peace and space of Haarlem, with all urban conveniences within easy reach.\n' +
              '\n' +
              'Details:\n' +
              '- Living area approx. 136m² and a volume of 552m³, and a very spacious fully landscaped backyard approx. 150 m²\n' +
              '- Rental period is based on an indefinite period with a minimum period of 18 months\n' +
              '- In addition to the monthly rent, the tenant must take care of the costs of electricity, water, TV/Internet and municipal taxes\n' +
              '- In addition to the rental price, € 150.00 service costs will be charged to the tenant for twice-yearly maintenance of the garden and cleaning of the windows at the front of the house\n' +
              '- The house is rented partly upholstered (floors are present, window coverings (only living room present) and lamps must be arranged by the tenant themselves)\n' +
              '- Pets and smoking are/is not allowed\n' +
              '- Landlord clearly prefers not to rent the house to house sharers and/or families\n' +
              '- This house can be rented by a tenant with his own monthly stable sufficient income from work and unfortunately not with a guarantor or a tenant who will not live in the house himself. - These apartments have been renovated to the most modern standards and are therefore equipped with modern insulation materials and each individual apartment has modern installations such as a heat pump system, Heat Recovery Installation (WTW) and underfloor heating, which together result in an Energy Label A++\n' +
              '---------------------------------------------------------------------------------------------------\n' +
              '\n' +
              'Prachtig volledig gerenoveerd (energielabel A++) 3-kamer parterre appartement met mega tuin aan het Houtplein, Haarlem\n' +
              '\n' +
              'Dit buitengewone appartement is een parel in hartje Haarlem! Gelegen aan het statige Houtplein, biedt dit volledig gerenoveerde appartement (met gebruik van de meest hoogwaardige materialen) een unieke kans op comfortabel en duurzaam wonen. Hier is aan alles gedacht: van stijl en luxe tot energiezuinigheid.\n' +
              '\n' +
              'Bij binnenkomst word je al verrast door de indrukwekkende entree, waar je een vleugje grandeur uit vervlogen tijden ervaart. Hoge plafonds met een kroonluchter, moderne wandpanelen en stijlvolle houten afwerkingen rondom de meterkasten zetten de toon. Via het trappenhuis bereik je dit unieke parterre appartement.\n' +
              '\n' +
              'Eenmaal binnen, valt direct op hoe licht en ruim het appartement is dankzij de hoge plafonds en doordachte indeling. Aan de voorzijde bevindt zich de sfeervolle woonkamer, die naadloos overloopt in een strakke, moderne woonkeuken met een centraal kookeiland. Deze keuken is de perfecte plek om in te richten als woonkeuken waardoor het de centrale plek in dit prachtige appartement is.\n' +
              '\n' +
              'De brede gang, die zowel via de woonkamer als de keuken toegankelijk is, biedt een chique doorkijk naar de achtertuin. Aan de achterzijde bevinden zich twee ruime slaapkamers met grote raampartijen, waardoor je elke ochtend wakker wordt met uitzicht op de prachtig aangelegde tuin.\n' +
              '\n' +
              'De badkamer is voorzien van royale afmetingen, ligbad, groot tweepersoons wastafelmeubel met verlichte spiegel, inloopdouche en een tweede toilet. Stijlvolle zandkleurige tegels en topkwaliteit sanitair maakt het helemaal af.\n' +
              '\n' +
              'De tuin: een unieke plek in het centrum van Haarlem, gelegen op het zonnige zuidwesten, zien wij niet vaak voorbij komen. Met een oppervlakte van maar liefst 140 m² is dit dé plek om te ontspannen. De tuin is volledig aangelegd en voorzien van een veranda, zodat je hier het hele jaar door kunt genieten van de buitenlucht.\n' +
              '\n' +
              'Haarlem combineert de charme van een historische stad met het comfort van moderne voorzieningen. Waar je in Amsterdam vaak de drukte en chaos moet trotseren, biedt Haarlem juist rust, ruimte én karakter. Bovendien is de stad super centraal gelegen:\n' +
              '- Binnen 15 minuten sta je op Amsterdam Centraal met de trein.\n' +
              '- Schiphol is snel bereikbaar met zowel de auto als het openbaar vervoer.\n' +
              '- De duinen en het strand liggen op fietsafstand.\n' +
              '\n' +
              'Haarlem heeft een uitstekend aanbod aan horeca, winkels en culturele hotspots, variërend van gezellige cafés tot Michelin-sterrenrestaurants en van unieke boetieks tot grote ketens. Daarbij geniet je hier van een gemoedelijke sfeer, groene parken en uitstekende verbindingen naar de Randstad.\n' +
              '\n' +
              'Een appartement als dit komt zelden op de markt. Met zijn centrale ligging, luxe afwerking en energieneutraliteit is dit een woning die men normaal alleen in Amsterdam verwacht, maar dan zonder de drukte. Dit is jouw kans om het beste van twee werelden te combineren: de rust en ruimte van Haarlem, met alle stedelijke gemakken binnen handbereik.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Woonoppervlakte ca. 136m² en een inhoud van 552m³, en een zeer royale compleet aangelegde achtertuin ca. 150 m²\n' +
              '- Huurperiode is op basis van onbepaalde tijd met een minimale periode van 18 maanden\n' +
              '- De huurder dient naast de maandelijkse huursom zorg te dragen voor de kosten van elektra, water, TV/Internet en Gemeentelijke belastingen\n' +
              '- Naast de huurprijs wordt er € 150,00 servicekosten naar de huurder doorberekend inzake twee maal per jaar onderhoud van de tuin en het raam reinigen van voorzijde woning\n' +
              '- De woning wordt deels gestoffeerd verhuurd (vloeren zijn aanwezig, raambekleding (alleen woonkamer aanwezig) en lampen dient huurder zelf te regelen)\n' +
              '- Huisdieren en roken zijn/is niet toegestaan\n' +
              '- Verhuurder geeft duidelijk de voorkeur de woning niet te verhuren aan woningdelers en/of gezinnen\n' +
              '- Deze woning kan worden gehuurd door een huurder met een eigen maandelijks stabiel toereikend inkomen uit werk en helaas niet met een garantsteller of een huurder die de woning niet zelf zal bewonen.\n' +
              '- Deze appartementen zijn gerenoveerd naar de meest moderne maatstaven en dus voorzien van moderne isolatie materialen en elk afzonderlijk appartement heeft moderne installaties zoals de een warmtepomp-systeem, Warmte-Terug-Win installatie (WTW) en vloerverwarming, wat tezamen resulteert in een Energielabel A++',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.923Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.939Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.955Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.971Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030960_39ejv',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Rembrandtweg 28/ 30 en Javaweg 2 \n' +
              '\n' +
              '*ENGLISH BELOW* \n' +
              '\n' +
              "Op toplocatie in de Noordwijkse Zuidduinen ligt dit prachtige “private estate” welke beschikt over 2 villa's, 3 aparte toegangshekken en 3 entrees welke elk een eigen adres hebben, te weten Rembrandtweg 28 en 30 en Javaweg 2 te Noordwijk aan Zee, goed om te weten is dat er nog op een van de drie percelen bijgebouwd kan worden!\n" +
              '\n' +
              'Deze droom estate ligt op loopafstand van het heerlijke Noordwijkse strand met fantastische beach clubs en geeft je het ideale permanente vakantiegevoel en dat in Nederland!\n' +
              'Zelden zien wij een vergelijkbaar afwerkingsniveau waarbij kosten noch moeite is gespaard. \n' +
              '\n' +
              'Dit “private estate” bestaat uit twee hypermoderne, nieuwgebouwde, vrijstaande villa’s.\n' +
              '\n' +
              'Hierdoor is dit “private estate” ideaal voor bijvoorbeeld dubbele bewoning met ouders of voor een gastenverblijf. Ook kan het dienen als een perfecte woon-werk combinatie, hetgeen op verschillende plekken binnen deze estate mogelijk is. Daarbij kan in alle gevallen het pool-house worden aangewend voor de huisvesting van bijvoorbeeld personeel, een nanny of butler.\n' +
              ' \n' +
              'De door architect Van Manen en Kameling&VanderBurgh ontworpen privé compound is prachtig omsloten en zeer goed beveiligd en beschikt over een prachtige tuin op het zuiden, naar een ontwerp van Wencop tuinarchitecten, met vrijstaand buitenzwembad met terrassen rondom en een pool house. Vanaf het prachtige grote dakterras kijkt u over de Zuidduinen en is zelfs de zee zichtbaar. Ook is de gehele tuin voorzien van verlichting. Via de aparte entree aan de Javaweg bereikt u de achterzijde en de extra parkeerplaatsen.\n' +
              '\n' +
              'Het totaal oppervlak van bedraagt circa 1.040 m2, waarvan circa 715 m2 woonruimte, circa  100 m2 garages en circa 225 m2 terrassen en een perceeloppervlakte ca. 1.872 m2. \n' +
              '\n' +
              'De villa’s zijn gebouwd in 2018 en zijn zeer luxe & compleet afgewerkt met veel maatwerk en oog voor detail.\n' +
              '\n' +
              'Het geheel (kantoor, woningen en poolhouse) heeft in totaal 4 woonkamers, 7 slaapkamers met 6 badkamers, 3 keukens en 4 extra toiletten. Uiteraard zijn er zijn meer slaapkamers te creeren omdat sommige kamers nu als extra vergaderruimte zijn gebruikt. Ook beschikt het geheel over twee dubbele inpandige garages en 8 parkeerplaatsen op eigen terrein. \n' +
              '\n' +
              'De ligging in het duin waarborgt privacy, terwijl boulevard, strand (400 meter), duinen en winkels zich op enkele minuten loopafstand bevinden.\n' +
              '\n' +
              "Op de kavel aan de Javaweg 2 gelegen is een vergunning om nog circa 300 m2 te bouwen. Er zou een garage met lift gemaakt kunnen worden van 300 m2 (in de grond), waar een auto liefhebber veel auto's in kwijt kan, plus een opbouw van 200 m2 ten behoeve van bijvoorbeeld een kantoor, atelier, gasten of auto's. Waarschijnlijk kan de opbouw zelfs groter dan 200 m2 omdat het te bouwen oppervlak 300 m2 is en je nog de lucht in mag. Uiteraard dient een en ander in overleg met de gemeente plaats te vinden en er een omgevingsvergunning aangevraagd te worden.\n" +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Tuinverdieping:\n' +
              'Bij de bouw en indeling is optimaal rekening gehouden met de situering van de villa’s in de duinen waardoor de verdiepingen anders en speelser zijn ingedeeld dan bij een standaard villa, hierdoor bevindt zich de ruime entree op niveau van de tuin.\n' +
              '\n' +
              'Beide villa’s hebben een eigen entree, alsmede toiletten en garderobe. De hoofdvilla heeft een indrukwekkend open trappenhuis naar de overloop over 4 woonlagen.\n' +
              'De tuinverdieping herbergt een enorme multi functionele ruimte welke bijvoorbeeld gebruik kan worden als extra woon/chill ruimte, partyruimte, kantoor/praktijk of fitnessruimte. Het kantoor is afgescheiden van het woonhuis en kan alleen worden betreden met vingerafdruk herkenning. De multi functionele ruimte heeft een eigen opgang evenals een guest room voor bijvoorbeeld zakelijke relaties en kan ook verhuurd worden als separate ruimte. Ook beschikt deze over een wellness, sauna met extra infrarood functie en Turks stoombad. \n' +
              '\n' +
              'Tevens bevinden zich op deze woonlaag een keuken, 2 logeerkamers ieder met eigen badkamer en toilet.\n' +
              'In de aangrenzende tuin ligt het pool house met zonneschermen over de gehele breedte en een groot terras met verwarmd zwembad van 12 X 4 meter voorzien van (sfeer)verlichting, Jetstream en roldek, separate ruime (buiten)jacuzzi en buitendouche.\n' +
              '\n' +
              '1e verdieping: \n' +
              'Op de eerste verdieping van de hoofdvilla (Rembrandtweg 30) bevinden zich drie luxe slaapkamers. In het bijgebouw (Rembrandtweg 28) zijn nog twee slaapkamers gelegen. Het gehele estate beschikt daarmee over vijf comfortabele slaapkamers op deze woonlaag, verdeeld over beide villa’s.\n' +
              'De hoofdvilla beschikt op deze verdieping over een zeer ruime master bedroom met een walk-in closet, die tevens is uitgevoerd als “saferoom”, en een aangrenzende luxe badkamer. Daarnaast zijn er twee extra slaapkamers, elk voorzien van een eigen en-suite badkamer. Aangrenzend aan de master bedroom bevindt zich een stijlvolle bibliotheek met een imposante trappartij die leidt naar de bovenliggende woonkamer en daar een eigen toegang verzorgt.\n' +
              'In het bijgebouw bevindt zich op deze verdieping een ruime woonkeuken met hoogwaardige Siemens-inbouwapparatuur, een separaat toilet, twee slaapkamers en een badkamer.\n' +
              '\n' +
              '2e verdieping:\n' +
              'De 2e verdieping is voorbehouden aan de hoofdvilla. Hier bevindt zich de heerlijke grote woonkamer met woonkeuken met twee gashaarden en met veel licht. De woonkamer loopt door naar het grote terras met open haard en een prachtig uitzicht.\n' +
              '\n' +
              '3e verdieping:\n' +
              'Op deze verdieping bevindt zich het prachtige dakterras met adembenemend uitzicht naar de zee en bollenvelden! Bij helder weer is zelfs Leiden te zien. Een ideale plek om na te genieten van een zomerse dag.\n' +
              '\n' +
              'Naast de villa ligt een gastenverblijf (vrijstaande villa) met 2 slaapkamers, een badkamer en terras. De woning is ideaal voor gezinnen met nanny’s of langdurige gasten die afgescheiden en privé in en rond de woning kunnen verblijven.\n' +
              '\n' +
              'Keukens:\n' +
              'Het geheel heeft vier Siemens keukens tot zijn beschikking, een in de hoofdwoning, een in de 2e woning, een kleine keuken in het kantoor en een in het poolhouse. Op het dak is er een kleine kookplek.\n' +
              'De keukens zijn voorzien van:\n' +
              '- Keuken hoofdhuis: Quooker Cube kraan, 2 vaatwassers, kingsize koelkast, kingsize vriezer, inbouw Siemens koffiezet apparaat (barista) en combi-oven en stoomoven. Inductie kookplaat + Teppan Yaki plaat met ingebouwde afzuiging van merk Bora.\n' +
              '- Kantoor Keuken: Vaatwasser, Koelkast en inbouw Siemens koffiemachine (Barista), Siemens apparatuur en Quooker Cube\n' +
              '- Pool house: Vaatwasser, koelkast, kookplaatje en Quooker Cube\n' +
              '- Keuken 2e woning: Siemens apparatuur te weten koelkast, combioven en inductie kookplaat met afzuigkap\n' +
              '\n' +
              'State of the art beveiliging:\n' +
              'Het geheel is zeer goed beveiligd en door een hek afgeschermd, beveiligd met camera’s en heeft meerdere kennels voor waakhonden. De villa en omgeving zijn voorzien van een beveiligd wifi systeem en is beveiligd door middel van een alarmsysteem, een surveillance dienst en wijkbeveiliging evenals een verbod op parkeren rondom het pand. Er is zowel rondom de woning als in de woning speciaal gelaagd slagvast beglazing aangebracht en diverse compartimenten (perimeters in en buiten de woningen) waardoor er extra beveiligingslagen ontstaan. Verder is het huis uiteraard voorzien van een safe room.\n' +
              '\n' +
              'Belangrijk om te weten:\n' +
              '- De villa is voorzien van Domotica;\n' +
              '- Huisautomatisering;\n' +
              '- Automatische verlichting, door gehele huis inbouwspots, wandverlichting en plafondspots;\n' +
              '- Vloerverwarming (per ruimte regelbaar);\n' +
              '- Vloeren zijn afgewerkt met op de woonverdieping visgraat eikenvloer en de overige vloeren zijn gietvloeren;\n' +
              '- Ventilatiesysteem;\n' +
              '- Keyless entry rondom met fingerscan.\n' +
              '- Elektrisch entree hekwerk;\n' +
              '- Zeer strakke wand- en plafondafwerking;\n' +
              '- Luxe hang-sluitwerk;\n' +
              '- Alle vertrekken beschikken over televisie- en internetaansluitingen;\n' +
              '- Zonnepanelen, recent zijn er zonnepanelen geïnstalleerd die gemiddeld 700 euro aan energie opleveren per maand;\n' +
              '- Centrale verwarming met WKO;\n' +
              '- Aansluiting voor heathers, windschermen en zonwering, bij de pool zijn deze wind gestuurd;\n' +
              '- Recent zijn er ook zonneschermen op het pool house geplaatst evenals “elektrische shades op alle ramen” die via domotica bediend worden;\n' +
              '- In totaal zijn er in de villa’s vier wasmachine en vier droger aansluitpunten beschikbaar;\n' +
              '- Rondom een onderhoudsarme (duin)tuin, beregeningsinstallatie en ruime terrassen op zuid en west;\n' +
              '- De aanwezige inventaris, binnen en buiten, is eventueel over te nemen.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.970Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:30.989Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:30'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373030978_5w7c6',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Omschrijving\n' +
              '\n' +
              'Locatie\n' +
              'Locatie, locatie, locatie! Wonen op dé meest gewilde locatie van Amsterdam!\n' +
              '\n' +
              'Modern wonen in historisch centrum van Amsterdam! Het gebouw is in 2007 volledig nieuw gebouwd in oude stijl en beschikt over het uitstekende energie label A!\n' +
              '\n' +
              'Het appartement bevindt zich in een rustige woonstraat midden in het hart van het centrum van Amsterdam met alle belangrijke pleinen en winkelstraten binnen handbereik terwijl men tegelijkertijd lekker rustig kan wonen.\n' +
              '\n' +
              'Het gebouw beschikt over een indrukwekend gedeeld entree van wel vijf meter hoog!\n' +
              '\n' +
              'Het appartement is volledig nieuw gemeubileerd en beschikt over nieuwe huishoudelijke apparatuur. Ook beschikt het appartement in de werkkamer (tweede slaapkamer) over een nieuwe grote kledingkast.\n' +
              '\n' +
              'De absolute highlight van dit appartement is prive toegang tot het buitengewoon royaal en zonnige dakterras met uitzicht over de Amsterdamse daken en zicht op onder andere het Rijksmuseum welke menig hart sneller zal doen kloppen.\n' +
              '\n' +
              'Ook het groene uitzicht op de binnentuinen van de grachtengordel vanaf het vijf meter brede balkon aan de achterzijde van het appartement in de werkkamer (tweede slaapkamer) samen met het uitzicht vanuit het raam in de slaapkamer laat niets te wensen over.\n' +
              '\n' +
              'Het appartement heeft geen direkte boven en geen naaste buren! Dit in combinatie met de rustige straat en de groene binnentuinen aan de achterzijde garandeerd een goede avondrust!\n' +
              '\n' +
              'De meest gewilde locatie direct in het drukke hart van het centrum van Amsterdam waarbij alle belangrijke bezienswaardigheden binnen handbereik zijn in combinatie met het toch rustige wonen in de Kerkstraat en zonder directe boven of naaste buren tezamen met het buitengewoon royaal dakterras en balkon maakt dit appartement dan ook een zeldzame vondst!\n' +
              '\n' +
              'Op de begane grond bevindt zich het populaire luxe banketbakcafé Pantopia welke enkel overdag geopend is.\n' +
              'Op korte afstand\n' +
              'Vondelpark 750m, \n' +
              'Museumplein 500m, \n' +
              'Rembrandplein 750m, \n' +
              'Muntplein 600m, \n' +
              'Leidseplein (Uitgaansgelegenheden) 550m, \n' +
              'Leidsetraat (Winkelstraat) 350m, \n' +
              'Kalverstraat (Winkelstraat) 800m, \n' +
              'Spiegelstraat (Antiek winkels) 30m en \n' +
              'PC-hoofdstraat 550m.\n' +
              '\n' +
              'Prinsengracht 100m,\n' +
              'Keizersgracht 120m,\n' +
              'Herengracht 280m.\n' +
              '\n' +
              'Openbaar vervoer verbindingen\n' +
              'Vijzelstraat 550m,\n' +
              'Tram: 1, 19, 7.\n' +
              'Bus: 357, 391, 397, N84, N88. \n' +
              'Metro: 52\n' +
              '\n' +
              'Leidsestraat 450m,\n' +
              'Tram: 2, 12, 17.\n' +
              '\n' +
              'Leidseplein 550m,\n' +
              'Bus: 314, 357, 397.\n' +
              '\n' +
              'Fiets verhuur\n' +
              'King bike rental 75m.\n' +
              '\n' +
              'Supermarkt\n' +
              'Albert Heijn Vijzelstraat,\n' +
              '200m.\n' +
              '\n' +
              'Appartement\n' +
              'Het appartement is volledig nieuw neutraal gemeubileerd.\n' +
              '\n' +
              'Slaapkamer is gemeubileerd met volledig nieuw: bed, matrassen, dekens en kussens.\n' +
              '\n' +
              'Werkkamer (tweede slaapkamer) is gemeubileerd met volledig nieuwe grote kledingkast en bureautafel met stoel.\n' +
              '\n' +
              'Woonkamer is gemeubileerd met volledig nieuw: echt lederen tweezits bank, vloerkleed en salontafel, eettafel en stoelen.\n' +
              '\n' +
              'Verplaatsbaar televisie meubel op wielen met nieuwe 109cm 4K smart televisie van LG.\n' +
              '\n' +
              'Keuken heeft nieuwe apparatuur waaronder: Bosch vaatwasser, Bosch koelkast, oven en zes pits gasfornuis. Afzuigkap met luchtafvoer naar buiten.\n' +
              '\n' +
              'Het appartement beschikt over een seperate toilet ruimte.\n' +
              '\n' +
              'De badkamer beschikt over een separate ruimte met een Bosch wasdroog combinatie.\n' +
              '\n' +
              'Badkamer, toilet en keuken zijn voorzien van mechanische ventilatie.\n' +
              '\n' +
              'Direkt naast de ingang van het appartement bevindt zich een handige prive bergingsruimte.\n' +
              'Kenmerken\n' +
              '\n' +
              'Overdracht\n' +
              'Huurprijs: € 3.790 per maand (geen servicekosten)\n' +
              'Waarborgsom: € 7.580 eenmalig\n' +
              'Huurovereenkomst: onbepaalde tijd\n' +
              'Huurperiode: minimaal 24 maanden (afwijkende periode bespreekbaar)\n' +
              'Aanvaarding: per direct beschikbaar\n' +
              '\n' +
              'Bouw\n' +
              'Soort appartement: bovenwoning (appartement) \n' +
              'Soort bouw: bestaande bouw\n' +
              'Bouwjaar: 2007\n' +
              '\n' +
              'Gebruiksoppervlakten\n' +
              'Wonen: 80m2\n' +
              'Gebouwgebonden buitenruimte: 35m2\n' +
              '\n' +
              'Indeling\n' +
              'Aantal slaapkamers: 2 kamers\n' +
              'Aantal badkamers: 1 badkamer, separaat toilet ruimte\n' +
              'Badkamervoorzieningen: ligbad, inloop douche en wastafelmeubel\n' +
              'Aantal woonlagen: 1 woonlaag\n' +
              'Gelegen op: 3e woonlaag\n' +
              'Voorzieningen: mechanische ventilatie\n' +
              '\n' +
              'Energie\n' +
              'Energielabel: A\n' +
              'Isolatie: Volledig geïsoleerd\n' +
              'Verwarming: Cv-ketel\n' +
              'Warm water: Cv-ketel\n' +
              'Cv-ketel: Gas gestookt\n' +
              '\n' +
              'Buitenruimte\n' +
              'Ligging: in het centrum\n' +
              'Balkon: aanwezig, 5m2\n' +
              'Dakterras: aanwezig, 30m2\n' +
              '\n' +
              'Bergruimte\n' +
              'Berging: direct naast appartement ingang, 2m2\n' +
              'Voorziening: elektra\n' +
              '\n' +
              'Vereisten\n' +
              'Inkomenseis: Rond twee keer de maand huur\n' +
              'Huisdieren: In overleg\n' +
              'Roken: Nee\n' +
              '\n' +
              '____________________________________________________\n' +
              '\n' +
              'Description\n' +
              '\n' +
              'Location\n' +
              'Location, location, location! Living in the most desirable location of Amsterdam!\n' +
              '\n' +
              'Modern living in the historic center of Amsterdam! The building was completely rebuilt in 2007 in old style and has the excellent energy label A!\n' +
              '\n' +
              'The apartment is located in a quiet residential street in the heart of the center of Amsterdam with all important squares and shopping streets within easy reach while at the same time one can live in peace.\n' +
              '\n' +
              'The building has an impressive shared entrance of no less than five meters high!\n' +
              '\n' +
              'The apartment is completely newly furnished and has new household appliances. The apartment also has a new large wardrobe in the study (second bedroom).\n' +
              '\n' +
              'The absolute highlight of this apartment is private access to the exceptionally spacious and sunny roof terrace with a view over the Amsterdam roofs and a view of the Rijksmuseum, among other things, which will make many a heart beat faster.\n' +
              '\n' +
              'The green view of the inner gardens of the canal belt from the five-meter wide balcony at the rear of the apartment in the study (second bedroom) together with the view from the window in the bedroom leaves nothing to be desired.\n' +
              '\n' +
              "The apartment has no direct upstairs and no immediate neighbors! This in combination with the quiet street and the green inner gardens at the rear guarantees a good evening's rest!\n" +
              '\n' +
              'The most sought-after location directly in the busy heart of the center of Amsterdam where all important sights are within easy reach in combination with the quiet living in the Kerkstraat and without direct upstairs or immediate neighbors together with the exceptionally spacious roof terrace and balcony makes this apartment a rare find!\n' +
              '\n' +
              'On the ground floor is the popular luxury pastry café Pantopia which is only open during the day.\n' +
              'At a short distance\n' +
              'Vondelpark 750m, \n' +
              'Museumplein 500m, \n' +
              'Rembrandplein 750m, \n' +
              'Muntplein 600m, \n' +
              'Leidseplein (Nightlife) 550m, \n' +
              'Leidsetraat (Shopping street) 350m, \n' +
              'Kalverstraat (Shopping street) 800m, \n' +
              'Spiegelstraat (Antique shops) 30m and \n' +
              'PC-hoofdstraat 550m.\n' +
              '\n' +
              'Prinsengracht 100m,\n' +
              'Keizersgracht 120m,\n' +
              'Herengracht 280m.\n' +
              '\n' +
              'Public transport connections\n' +
              'Vijzelstraat 550m,\n' +
              'Tram: 1, 19, 7.\n' +
              'Bus: 357, 391, 397, N84, N88.\n' +
              '\n' +
              'Metro: 52\n' +
              '\n' +
              'Leidsestraat 450m,\n' +
              'Tram: 2, 12, 17.\n' +
              '\n' +
              'Leidseplein 550m,\n' +
              'Bus: 314, 357, 397.\n' +
              '\n' +
              'Bike rental\n' +
              'King bike rental 75m.\n' +
              '\n' +
              'Supermarket\n' +
              'Albert Heijn Vijzelstraat,\n' +
              '200m.\n' +
              'Apartment\n' +
              'The apartment is completely new and neutrally furnished.\n' +
              '\n' +
              'Bedroom is furnished with completely new: bed, mattresses, blankets and pillows.\n' +
              '\n' +
              'Study (second bedroom) is furnished with completely new large wardrobe and desk table with chair.\n' +
              '\n' +
              'Living room is furnished with completely new: genuine leather two-seater sofa, carpet and coffee table, dining table and chairs.\n' +
              '\n' +
              'Movable television furniture on wheels with new 109cm 4K smart television from LG.\n' +
              '\n' +
              'Kitchen has new appliances including: Bosch dishwasher, Bosch refrigerator, oven and six-burner gas stove. Extractor hood with air exhaust to the outside.\n' +
              '\n' +
              'The apartment has a separate toilet room.\n' +
              '\n' +
              'The bathroom has a separate room with a Bosch washer-dryer combination.\n' +
              '\n' +
              'Bathroom, toilet and kitchen are equipped with mechanical ventilation.\n' +
              '\n' +
              'Directly next to the entrance of the apartment is a convenient private storage room.\n' +
              'Features\n' +
              '\n' +
              'Transfer\n' +
              'Rent: €3,790 per month (no service costs)\n' +
              'Deposit: €7,580 one-off\n' +
              'Rental agreement: indefinite period\n' +
              'Rental period: minimum 24 months (different period negotiable)\n' +
              'Acceptance: immediately available\n' +
              '\n' +
              'Construction\n' +
              'Type of apartment: upper house (apartment)\n' +
              'Type of construction: existing construction\n' +
              'Year of construction: 2007\n' +
              '\n' +
              'Usable surface areas\n' +
              'Living: 80m2\n' +
              'Building-related outdoor space: 35m2\n' +
              '\n' +
              'Layout\n' +
              'Number of bedrooms: 2 rooms\n' +
              'Number of bathrooms: 1 bathroom, separate toilet\n' +
              'Bathroom facilities: bath, walk-in shower and washbasin\n' +
              'Number of floors: 1 floor\n' +
              'Located on: 3rd floor\n' +
              'Facilities: mechanical ventilation\n' +
              '\n' +
              'Energy\n' +
              'Energy label: A\n' +
              'Insulation: Fully insulated\n' +
              'Heating: Central heating boiler\n' +
              'Hot water: Central heating boiler\n' +
              'Central heating boiler: Gas fired\n' +
              '\n' +
              'Outdoor space\n' +
              'Location: in the centre\n' +
              'Balcony: present, 5m2\n' +
              'Roof terrace: present, 30m2\n' +
              '\n' +
              'Storage space\n' +
              'Storage: directly next to apartment entrance, 2m2\n' +
              'Facilities: electricity\n' +
              '\n' +
              'Requirements\n' +
              'Income requirement: Around twice the monthly rent\n' +
              'Pets: In consultation\n' +
              'Smoking: No',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:30.988Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:30'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.006Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.023Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.040Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.056Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.073Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.089Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.105Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.122Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031111_bia5e',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MULTIFUNCTIONELE GARAGEBOXEN TE HUUR IN WADDINXVEEN\n' +
              'Modern garageboxcomplex in Business Parkrand met 24/7 toegang en zakelijke mogelijkheden\n' +
              '\n' +
              'Aan de Zuidelijke Rondweg in Waddinxveen, direct naast de nieuwbouwwijk Park Triangel, is Business ParkRand ontwikkeld: een moderne werkomgeving met hoogwaardige garageboxen die zowel voor zakelijk als particulier gebruik beschikbaar zijn. Of u nu een startende ondernemer bent, een zzp’er met behoefte aan werkruimte, of juist op zoek bent naar veilige en toegankelijke opslag of stalling, hier vindt u de perfecte oplossing.\n' +
              '\n' +
              'Midden op het terrein van de eerste fase is een gebouw gerealiseerd met in totaal 184 garageboxen, verspreid over vier verdiepingen. De boxen variëren in grootte van circa 15 m² tot 51 m². Dankzij een eigen postadres per unit zijn de garageboxen ook zakelijk inzetbaar, bijvoorbeeld als inschrijfadres.\n' +
              '\n' +
              'De boxen zijn 24 uur per dag toegankelijk via een beveiligde elektrische schuifpoort. Elke garagebox is bereikbaar met de auto en voorzien van een (elektrische) overheaddeur, een standaard stroomaansluiting en verlichting. Op iedere etage bevinden zich sanitaire voorzieningen met toilet, voorportaal en wasbak, waardoor de units ook geschikt zijn voor langduriger gebruik als werkruimte.\n' +
              '\n' +
              'De ligging van ParkRand is ideaal: nabij de A12 en N207, met uitstekende verbindingen richting Gouda, Boskoop, Zoetermeer en Rotterdam. \n' +
              'Station Waddinxveen Triangel ligt op loopafstand, met elk kwartier een treinverbinding richting Gouda en Alphen a/d Rijn – ideaal voor ondernemers en gebruikers die ook met het openbaar vervoer reizen.\n' +
              '\n' +
              'Daarbij sluit de uitstraling van het complex naadloos aan op de architectuur van Park Triangel. Denk aan industriële details, metselwerk en een groene inrichting die is doorgetrokken vanuit de woonwijk. Het maakt van ParkRand de enige werklocatie binnen deze levendige woonomgeving met ruim 2.900 huishoudens.\n' +
              '\n' +
              'Indeling\n' +
              'TYPE G3 – 18 m²\n' +
              'De unit biedt circa 18 m² bruto vloeroppervlakte en is gelegen op de derde verdieping. Alle verdiepingen zijn per voertuig bereikbaar via een hellingsbaan. De units zijn standaard voorzien van verlichting, wandcontactdozen en een handmatig bedienbare overheaddeur met een doorrijbreedte van circa 2,60 tot 2,70 meter. Dankzij de functionele indeling en ruime afmetingen zijn deze units geschikt voor uiteenlopende doeleinden, zoals opslag, werkruimte of kleinschalige zakelijke activiteiten. \n' +
              '\n' +
              'Afmetingen:\n' +
              '- Lengte: 6,10 meter\n' +
              '- Breedte: 2,90 meter\n' +
              '\n' +
              'Hoogte & doorrijhoogte per verdieping:\n' +
              '- Eerste verdieping: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Tweede verdieping: hoogte ca. 2,70 m | doorrijhoogte ca. 2,35 m\n' +
              '- Derde verdieping: hoogte ca. 2,80 m | doorrijhoogte ca. 2,20 m\n' +
              '\n' +
              'Op elke verdieping van het gebouw bevindt zich een centrale sanitaire ruimte, voorzien van een toilet, een wastafel met koudwaterkraan en een uitstortgootsteen. Deze faciliteiten maken de garageboxen geschikt voor zowel kort als langer gebruik, bijvoorbeeld als werkruimte of atelier. De sanitaire voorzieningen worden centraal onderhouden via de Vereniging van Eigenaren (VvE), wat bijdraagt aan een nette en verzorgde uitstraling van het complex.\n' +
              '\n' +
              'Huurcondities:\n' +
              'Type G3 18 m2 BESCHIKBAAR:\n' +
              'Vanaf € 350,- per maand excl. BTW, € 4200,- per jaar excl. BTW.\n' +
              '\n' +
              'Servicekosten:\n' +
              'Bijdrage servicekosten € 32,- per maand per unit.\n' +
              '\n' +
              'Huurtermijn:\n' +
              'In overleg.\n' +
              '\n' +
              'Huuringangsdatum:\n' +
              'Beschikbaar na oplevering, verwacht in oktober 2025.\n' +
              '\n' +
              'Huurprijsherziening:\n' +
              'Jaarlijks, voor het eerst één jaar na huuringangsdatum, op basis van de wijziging van het maandprijsindexcijfer volgens de consumentenprijsindex (CPI), reeks CPI-werknemers Laag (2015=100) zoals gepubliceerd door het Centraal Bureau voor de Statistiek (CBS).\n' +
              '\n' +
              'Huurbetaling:\n' +
              'Per maand vooruit.\n' +
              '\n' +
              'Waarborgsom:\n' +
              'Drie (3) maanden huur incl. BTW.\n' +
              '\n' +
              'Huurovereenkomst:\n' +
              'Conform model ROZ.\n' +
              '\n' +
              'BTW:\n' +
              'Verhuurder wenst te opteren voor BTW-belaste huur en verhuur. Ingeval huurder de BTW niet kan verrekenen zal de huurprijs worden verhoogd ter compensatie van de gevolgen van het vervallen van de mogelijkheid om te opteren voor BTW-belaste huur.\n' +
              '\n' +
              'Bijzonderheden\n' +
              '- 24/7 toegankelijk via een elektrische schuifpoort\n' +
              '- Elke box is bereikbaar met de auto\n' +
              '- Voorzien van (elektrische) overheaddeur, verlichting en stroomaansluiting\n' +
              '- Sanitaire voorzieningen op elke verdieping (toilet, voorportaal, wasbak)\n' +
              '- Elke unit heeft een eigen postadres, dus ook interessant om zakelijk te gebruiken\n' +
              '- Veilig en representatief terrein met cameratoezicht\n' +
              '\n' +
              'De verhuur is gestart en de eerste units worden binnenkort opgeleverd. Wil jij verzekerd zijn van een garagebox op deze unieke en goed bereikbare locatie? Neem vandaag nog contact met ons op voor de mogelijkheden!\n' +
              '\n' +
              'Deze vrijblijvende objectinformatie is met de meeste zorg samengesteld, maar voor de juistheid daarvan kunnen wij geen aansprakelijkheid aanvaarden. Ook kan aan de vermelde gegevens geen enkel recht worden ontleend. Alle informatie is enkel en alleen bedoeld voor de presentatie van het object en niet meer dan een uitnodiging tot het doen van een bod. Eventueel bijgevoegde plattegronden zijn slechts indicatief.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.122Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.138Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.154Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031143_x8t5f',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MULTIFUNCTIONELE GARAGEBOXEN TE HUUR IN WADDINXVEEN\n' +
              'Modern garageboxcomplex in Business Parkrand met 24/7 toegang en zakelijke mogelijkheden\n' +
              '\n' +
              'Aan de Zuidelijke Rondweg in Waddinxveen, direct naast de nieuwbouwwijk Park Triangel, is Business ParkRand ontwikkeld: een moderne werkomgeving met hoogwaardige garageboxen die zowel voor zakelijk als particulier gebruik beschikbaar zijn. Of u nu een startende ondernemer bent, een zzp’er met behoefte aan werkruimte, of juist op zoek bent naar veilige en toegankelijke opslag of stalling, hier vindt u de perfecte oplossing.\n' +
              '\n' +
              'Midden op het terrein van de eerste fase is een gebouw gerealiseerd met in totaal 184 garageboxen, verspreid over vier verdiepingen. De boxen variëren in grootte van circa 15 m² tot 51 m². Dankzij een eigen postadres per unit zijn de garageboxen ook zakelijk inzetbaar, bijvoorbeeld als inschrijfadres.\n' +
              '\n' +
              'De boxen zijn 24 uur per dag toegankelijk via een beveiligde elektrische schuifpoort. Elke garagebox is bereikbaar met de auto en voorzien van een (elektrische) overheaddeur, een standaard stroomaansluiting en verlichting. Op iedere etage bevinden zich sanitaire voorzieningen met toilet, voorportaal en wasbak, waardoor de units ook geschikt zijn voor langduriger gebruik als werkruimte.\n' +
              '\n' +
              'De ligging van ParkRand is ideaal: nabij de A12 en N207, met uitstekende verbindingen richting Gouda, Boskoop, Zoetermeer en Rotterdam. \n' +
              'Station Waddinxveen Triangel ligt op loopafstand, met elk kwartier een treinverbinding richting Gouda en Alphen a/d Rijn – ideaal voor ondernemers en gebruikers die ook met het openbaar vervoer reizen.\n' +
              '\n' +
              'Daarbij sluit de uitstraling van het complex naadloos aan op de architectuur van Park Triangel. Denk aan industriële details, metselwerk en een groene inrichting die is doorgetrokken vanuit de woonwijk. Het maakt van ParkRand de enige werklocatie binnen deze levendige woonomgeving met ruim 2.900 huishoudens.\n' +
              '\n' +
              'Indeling\n' +
              'TYPE G2 – 15 m²\n' +
              'De units bieden circa 15 m² bruto vloeroppervlakte en zijn gelegen op diverse etages binnen het complex. Alle verdiepingen zijn per voertuig bereikbaar via een hellingsbaan. De units zijn standaard voorzien van verlichting, wandcontactdozen en een handmatig bedienbare overheaddeur met een doorrijbreedte van circa 2,60 tot 2,70 meter. Dankzij de functionele indeling en ruime afmetingen zijn deze units geschikt voor uiteenlopende doeleinden, zoals opslag, werkruimte of kleinschalige zakelijke activiteiten. Enkele units zijn uitgevoerd met meerwerkopties, zoals een elektrisch bedienbare overheaddeur of een extra groep.\n' +
              '\n' +
              'Afmetingen:\n' +
              '- Lengte: 5,10 meter\n' +
              '- Breedte: 2,90 meter\n' +
              '\n' +
              'Hoogte & doorrijhoogte per verdieping:\n' +
              '- Eerste verdieping: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Tweede verdieping: hoogte ca. 2,70 m | doorrijhoogte ca. 2,35 m\n' +
              '- Derde verdieping: hoogte ca. 2,80 m | doorrijhoogte ca. 2,20 m\n' +
              '\n' +
              'Op elke verdieping van het gebouw bevindt zich een centrale sanitaire ruimte, voorzien van een toilet, een wastafel met koudwaterkraan en een uitstortgootsteen. Deze faciliteiten maken de garageboxen geschikt voor zowel kort als langer gebruik, bijvoorbeeld als werkruimte of atelier. De sanitaire voorzieningen worden centraal onderhouden via de Vereniging van Eigenaren (VvE), wat bijdraagt aan een nette en verzorgde uitstraling van het complex.\n' +
              '\n' +
              'Huurcondities:\n' +
              'Type G2 15 m2 BESCHIKBAAR:\n' +
              'Vanaf € 200,- per maand excl. BTW, € 2.400,- per jaar excl. BTW.\n' +
              '\n' +
              'Servicekosten:\n' +
              'Bijdrage servicekosten € 27,- per maand per unit.\n' +
              '\n' +
              'Huurtermijn:\n' +
              'In overleg.\n' +
              '\n' +
              'Huuringangsdatum:\n' +
              'Beschikbaar na oplevering, verwacht in september 2025.\n' +
              '\n' +
              'Huurprijsherziening:\n' +
              'Jaarlijks, voor het eerst één jaar na huuringangsdatum, op basis van de wijziging van het maandprijsindexcijfer volgens de consumentenprijsindex (CPI), reeks CPI-werknemers Laag (2015=100) zoals gepubliceerd door het Centraal Bureau voor de Statistiek (CBS).\n' +
              '\n' +
              'Huurbetaling:\n' +
              'Per maand vooruit.\n' +
              '\n' +
              'Waarborgsom:\n' +
              'Drie (3) maanden huur incl. BTW.\n' +
              '\n' +
              'Huurovereenkomst:\n' +
              'Conform model ROZ.\n' +
              '\n' +
              'BTW:\n' +
              'Verhuurder wenst te opteren voor BTW-belaste huur en verhuur. Ingeval huurder de BTW niet kan verrekenen zal de huurprijs worden verhoogd ter compensatie van de gevolgen van het vervallen van de mogelijkheid om te opteren voor BTW-belaste huur.\n' +
              '\n' +
              'Bijzonderheden\n' +
              '- 24/7 toegankelijk via een elektrische schuifpoort\n' +
              '- Elke box is bereikbaar met de auto\n' +
              '- Voorzien van (elektrische) overheaddeur, verlichting en stroomaansluiting\n' +
              '- Sanitaire voorzieningen op elke verdieping (toilet, voorportaal, wasbak)\n' +
              '- Elke unit heeft een eigen postadres, dus ook interessant om zakelijk te gebruiken\n' +
              '- Veilig en representatief terrein met cameratoezicht\n' +
              '\n' +
              'De verhuur is gestart en de eerste units worden binnenkort opgeleverd. Wil jij verzekerd zijn van een garagebox op deze unieke en goed bereikbare locatie? Neem vandaag nog contact met ons op voor de mogelijkheden!\n' +
              '\n' +
              'Deze vrijblijvende objectinformatie is met de meeste zorg samengesteld, maar voor de juistheid daarvan kunnen wij geen aansprakelijkheid aanvaarden. Ook kan aan de vermelde gegevens geen enkel recht worden ontleend. Alle informatie is enkel en alleen bedoeld voor de presentatie van het object en niet meer dan een uitnodiging tot het doen van een bod. Eventueel bijgevoegde plattegronden zijn slechts indicatief.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.153Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.170Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.189Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031177_phsyc',
  source: 'funda',
  duration: '12ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MULTIFUNCTIONELE GARAGEBOXEN TE HUUR IN WADDINXVEEN\n' +
              'Modern garageboxcomplex in Business Parkrand met 24/7 toegang en zakelijke mogelijkheden\n' +
              '\n' +
              'Aan de Zuidelijke Rondweg in Waddinxveen, direct naast de nieuwbouwwijk Park Triangel, is Business ParkRand ontwikkeld: een moderne werkomgeving met hoogwaardige garageboxen die zowel voor zakelijk als particulier gebruik beschikbaar zijn. Of u nu een startende ondernemer bent, een zzp’er met behoefte aan werkruimte, of juist op zoek bent naar veilige en toegankelijke opslag of stalling, hier vindt u de perfecte oplossing.\n' +
              '\n' +
              'Midden op het terrein van de eerste fase is een gebouw gerealiseerd met in totaal 184 garageboxen, verspreid over vier verdiepingen. De boxen variëren in grootte van circa 15 m² tot 51 m². Dankzij een eigen postadres per unit zijn de garageboxen ook zakelijk inzetbaar, bijvoorbeeld als inschrijfadres.\n' +
              '\n' +
              'De boxen zijn 24 uur per dag toegankelijk via een beveiligde elektrische schuifpoort. Elke garagebox is bereikbaar met de auto en voorzien van een (elektrische) overheaddeur, een standaard stroomaansluiting en verlichting. Op iedere etage bevinden zich sanitaire voorzieningen met toilet, voorportaal en wasbak, waardoor de units ook geschikt zijn voor langduriger gebruik als werkruimte.\n' +
              '\n' +
              'De ligging van ParkRand is ideaal: nabij de A12 en N207, met uitstekende verbindingen richting Gouda, Boskoop, Zoetermeer en Rotterdam. \n' +
              'Station Waddinxveen Triangel ligt op loopafstand, met elk kwartier een treinverbinding richting Gouda en Alphen a/d Rijn – ideaal voor ondernemers en gebruikers die ook met het openbaar vervoer reizen.\n' +
              '\n' +
              'Daarbij sluit de uitstraling van het complex naadloos aan op de architectuur van Park Triangel. Denk aan industriële details, metselwerk en een groene inrichting die is doorgetrokken vanuit de woonwijk. Het maakt van ParkRand de enige werklocatie binnen deze levendige woonomgeving met ruim 2.900 huishoudens.\n' +
              '\n' +
              'Indeling\n' +
              'TYPE G12 – 37 m² (begane grond)\n' +
              'Op de begane grond van het complex zijn twee boxen beschikbaar van elk circa 37 m² bruto vloeroppervlakte. De units zijn voorzien van standaard elektra, inclusief verlichting en wandcontactdozen, en beschikken over een handmatig bedienbare overheaddeur met een royale doorrijbreedte én -hoogte van circa 3 meter. Dankzij de praktische indeling en goede bereikbaarheid zijn de ruimtes ideaal te gebruiken als opslag, werkruimte of voor kleinschalige bedrijfsactiviteiten. In enkele gevallen zijn de units uitgerust met extra opties, zoals een elektrisch bedienbare overheaddeur of een extra stroomgroep.\n' +
              '\n' +
              'Afmetingen:\n' +
              '- Lengte: 6,10 meter\n' +
              '- Breedte: 5,90 meter\n' +
              '\n' +
              'Hoogte & doorrijhoogte per verdieping:\n' +
              '- Begane grond: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Eerste verdieping: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Tweede verdieping: hoogte ca. 2,70 m | doorrijhoogte ca. 2,35 m\n' +
              '- Derde verdieping: hoogte ca. 2,80 m | doorrijhoogte ca. 2,20 m\n' +
              '\n' +
              'Op elke verdieping van het gebouw bevindt zich een centrale sanitaire ruimte, voorzien van een toilet, een wastafel met koudwaterkraan en een uitstortgootsteen. Deze faciliteiten maken de garageboxen geschikt voor zowel kort als langer gebruik, bijvoorbeeld als werkruimte of atelier. De sanitaire voorzieningen worden centraal onderhouden via de Vereniging van Eigenaren (VvE), wat bijdraagt aan een nette en verzorgde uitstraling van het complex.\n' +
              '\n' +
              'Huurcondities:\n' +
              'Type G12 37 m2 BESCHIKBAAR:\n' +
              'Vanaf € 490,- per maand excl. BTW, € 5.880,- per jaar excl. BTW.\n' +
              '\n' +
              'Servicekosten:\n' +
              'Bijdrage servicekosten € 65,- per maand excl. BTW per unit.\n' +
              '\n' +
              'Huurtermijn:\n' +
              'In overleg.\n' +
              '\n' +
              'Huuringangsdatum:\n' +
              'Beschikbaar na oplevering, verwacht in september 2025.\n' +
              '\n' +
              'Huurprijsherziening:\n' +
              'Jaarlijks, voor het eerst één jaar na huuringangsdatum, op basis van de wijziging van het maandprijsindexcijfer volgens de consumentenprijsindex (CPI), reeks CPI-werknemers Laag (2015=100) zoals gepubliceerd door het Centraal Bureau voor de Statistiek (CBS).\n' +
              '\n' +
              'Huurbetaling:\n' +
              'Per maand vooruit.\n' +
              '\n' +
              'Waarborgsom:\n' +
              'Drie (3) maanden huur incl. BTW.\n' +
              '\n' +
              'Huurovereenkomst:\n' +
              'Conform model ROZ.\n' +
              '\n' +
              'BTW:\n' +
              'Verhuurder wenst te opteren voor BTW-belaste huur en verhuur. Ingeval huurder de BTW niet kan verrekenen zal de huurprijs worden verhoogd ter compensatie van de gevolgen van het vervallen van de mogelijkheid om te opteren voor BTW-belaste huur.\n' +
              '\n' +
              'Bijzonderheden\n' +
              '- 24/7 toegankelijk via een elektrische schuifpoort\n' +
              '- Elke box is bereikbaar met de auto\n' +
              '- Voorzien van (elektrische) overheaddeur, verlichting en stroomaansluiting\n' +
              '- Sanitaire voorzieningen op elke verdieping (toilet, voorportaal, wasbak)\n' +
              '- Elke unit heeft een eigen postadres, dus ook interessant om zakelijk te gebruiken\n' +
              '- Veilig en representatief terrein met cameratoezicht\n' +
              '\n' +
              'De verhuur is gestart en de eerste units worden binnenkort opgeleverd. Wil jij verzekerd zijn van een garagebox op deze unieke en goed bereikbare locatie? Neem vandaag nog contact met ons op voor de mogelijkheden!\n' +
              '\n' +
              'Deze vrijblijvende objectinformatie is met de meeste zorg samengesteld, maar voor de juistheid daarvan kunnen wij geen aansprakelijkheid aanvaarden. Ook kan aan de vermelde gegevens geen enkel recht worden ontleend. Alle informatie is enkel en alleen bedoeld voor de presentatie van het object en niet meer dan een uitnodiging tot het doen van een bod. Eventueel bijgevoegde plattegronden zijn slechts indicatief.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.189Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.207Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031195_hp33j',
  source: 'funda',
  duration: '12ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MULTIFUNCTIONELE GARAGEBOXEN TE HUUR IN WADDINXVEEN\n' +
              'Modern garageboxcomplex in Business Parkrand met 24/7 toegang en zakelijke mogelijkheden\n' +
              '\n' +
              'Aan de Zuidelijke Rondweg in Waddinxveen, direct naast de nieuwbouwwijk Park Triangel, is Business ParkRand ontwikkeld: een moderne werkomgeving met hoogwaardige garageboxen die zowel voor zakelijk als particulier gebruik beschikbaar zijn. Of u nu een startende ondernemer bent, een zzp’er met behoefte aan werkruimte, of juist op zoek bent naar veilige en toegankelijke opslag of stalling, hier vindt u de perfecte oplossing.\n' +
              '\n' +
              'Midden op het terrein van de eerste fase is een gebouw gerealiseerd met in totaal 184 garageboxen, verspreid over vier verdiepingen. De boxen variëren in grootte van circa 15 m² tot 51 m². Dankzij een eigen postadres per unit zijn de garageboxen ook zakelijk inzetbaar, bijvoorbeeld als inschrijfadres.\n' +
              '\n' +
              'De boxen zijn 24 uur per dag toegankelijk via een beveiligde elektrische schuifpoort. Elke garagebox is bereikbaar met de auto en voorzien van een (elektrische) overheaddeur, een standaard stroomaansluiting en verlichting. Op iedere etage bevinden zich sanitaire voorzieningen met toilet, voorportaal en wasbak, waardoor de units ook geschikt zijn voor langduriger gebruik als werkruimte.\n' +
              '\n' +
              'De ligging van ParkRand is ideaal: nabij de A12 en N207, met uitstekende verbindingen richting Gouda, Boskoop, Zoetermeer en Rotterdam. \n' +
              'Station Waddinxveen Triangel ligt op loopafstand, met elk kwartier een treinverbinding richting Gouda en Alphen a/d Rijn – ideaal voor ondernemers en gebruikers die ook met het openbaar vervoer reizen.\n' +
              '\n' +
              'Daarbij sluit de uitstraling van het complex naadloos aan op de architectuur van Park Triangel. Denk aan industriële details, metselwerk en een groene inrichting die is doorgetrokken vanuit de woonwijk. Het maakt van ParkRand de enige werklocatie binnen deze levendige woonomgeving met ruim 2.900 huishoudens.\n' +
              '\n' +
              'Indeling\n' +
              'TYPE G13 – 42 m² (begane grond)\n' +
              'Op de begane grond van het complex zijn twee boxen beschikbaar van elk circa 42 tot 44 m² bruto vloeroppervlakte. De units zijn voorzien van standaard elektra, inclusief verlichting en wandcontactdozen, en beschikken over een handmatig bedienbare overheaddeur met een royale doorrijbreedte én -hoogte van circa 3 meter. Dankzij de praktische indeling en goede bereikbaarheid zijn de ruimtes ideaal te gebruiken als opslag, werkruimte of voor kleinschalige bedrijfsactiviteiten. In enkele gevallen zijn de units uitgerust met extra opties, zoals een elektrisch bedienbare overheaddeur of een extra stroomgroep.\n' +
              '\n' +
              'Afmetingen:\n' +
              '- Lengte: 6,80 meter\n' +
              '- Breedte: 5,90 meter\n' +
              '\n' +
              'Hoogte & doorrijhoogte per verdieping:\n' +
              '- Eerste verdieping: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Tweede verdieping: hoogte ca. 2,70 m | doorrijhoogte ca. 2,35 m\n' +
              '- Derde verdieping: hoogte ca. 2,80 m | doorrijhoogte ca. 2,20 m\n' +
              '\n' +
              'Op elke verdieping van het gebouw bevindt zich een centrale sanitaire ruimte, voorzien van een toilet, een wastafel met koudwaterkraan en een uitstortgootsteen. Deze faciliteiten maken de garageboxen geschikt voor zowel kort als langer gebruik, bijvoorbeeld als werkruimte of atelier. De sanitaire voorzieningen worden centraal onderhouden via de Vereniging van Eigenaren (VvE), wat bijdraagt aan een nette en verzorgde uitstraling van het complex.\n' +
              '\n' +
              'Huurcondities:\n' +
              'Type G13 42 m2 BESCHIKBAAR:\n' +
              'Vanaf € 535,- per maand excl. BTW, € 6.420,- per jaar excl. BTW.\n' +
              '\n' +
              'Servicekosten:\n' +
              'Bijdrage servicekosten € 74,- per maand per unit.\n' +
              '\n' +
              'Huurtermijn:\n' +
              'In overleg.\n' +
              '\n' +
              'Huuringangsdatum:\n' +
              'Beschikbaar na oplevering, verwacht in september 2025.\n' +
              '\n' +
              'Huurprijsherziening:\n' +
              'Jaarlijks, voor het eerst één jaar na huuringangsdatum, op basis van de wijziging van het maandprijsindexcijfer volgens de consumentenprijsindex (CPI), reeks CPI-werknemers Laag (2015=100) zoals gepubliceerd door het Centraal Bureau voor de Statistiek (CBS).\n' +
              '\n' +
              'Huurbetaling:\n' +
              'Per maand vooruit.\n' +
              '\n' +
              'Waarborgsom:\n' +
              'Drie (3) maanden huur incl. BTW.\n' +
              '\n' +
              'Huurovereenkomst:\n' +
              'Conform model ROZ.\n' +
              '\n' +
              'BTW:\n' +
              'Verhuurder wenst te opteren voor BTW-belaste huur en verhuur. Ingeval huurder de BTW niet kan verrekenen zal de huurprijs worden verhoogd ter compensatie van de gevolgen van het vervallen van de mogelijkheid om te opteren voor BTW-belaste huur.\n' +
              '\n' +
              'Bijzonderheden\n' +
              '- 24/7 toegankelijk via een elektrische schuifpoort\n' +
              '- Elke box is bereikbaar met de auto\n' +
              '- Voorzien van (elektrische) overheaddeur, verlichting en stroomaansluiting\n' +
              '- Sanitaire voorzieningen op elke verdieping (toilet, voorportaal, wasbak)\n' +
              '- Elke unit heeft een eigen postadres, dus ook interessant om zakelijk te gebruiken\n' +
              '- Veilig en representatief terrein met cameratoezicht\n' +
              '\n' +
              'De verhuur is gestart en de eerste units worden binnenkort opgeleverd. Wil jij verzekerd zijn van een garagebox op deze unieke en goed bereikbare locatie? Neem vandaag nog contact met ons op voor de mogelijkheden!\n' +
              '\n' +
              'Deze vrijblijvende objectinformatie is met de meeste zorg samengesteld, maar voor de juistheid daarvan kunnen wij geen aansprakelijkheid aanvaarden. Ook kan aan de vermelde gegevens geen enkel recht worden ontleend. Alle informatie is enkel en alleen bedoeld voor de presentatie van het object en niet meer dan een uitnodiging tot het doen van een bod. Eventueel bijgevoegde plattegronden zijn slechts indicatief.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.207Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.228Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'consecutive_failures',
    message: '3 consecutive transformation failures detected',
    value: 3,
    threshold: 3,
    timestamp: '2025-09-08T23:10:31.228Z'
  },
  level: 'error',
  message: '3 consecutive transformation failures detected',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031216_gn4di',
  source: 'funda',
  duration: '12ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MULTIFUNCTIONELE GARAGEBOXEN TE HUUR IN WADDINXVEEN\n' +
              'Modern garageboxcomplex in Business Parkrand met 24/7 toegang en zakelijke mogelijkheden\n' +
              '\n' +
              'Aan de Zuidelijke Rondweg in Waddinxveen, direct naast de nieuwbouwwijk Park Triangel, is Business ParkRand ontwikkeld: een moderne werkomgeving met hoogwaardige garageboxen die zowel voor zakelijk als particulier gebruik beschikbaar zijn. Of u nu een startende ondernemer bent, een zzp’er met behoefte aan werkruimte, of juist op zoek bent naar veilige en toegankelijke opslag of stalling, hier vindt u de perfecte oplossing.\n' +
              '\n' +
              'Midden op het terrein van de eerste fase is een gebouw gerealiseerd met in totaal 184 garageboxen, verspreid over vier verdiepingen. De boxen variëren in grootte van circa 15 m² tot 51 m². Dankzij een eigen postadres per unit zijn de garageboxen ook zakelijk inzetbaar, bijvoorbeeld als inschrijfadres.\n' +
              '\n' +
              'De boxen zijn 24 uur per dag toegankelijk via een beveiligde elektrische schuifpoort. Elke garagebox is bereikbaar met de auto en voorzien van een (elektrische) overheaddeur, een standaard stroomaansluiting en verlichting. Op iedere etage bevinden zich sanitaire voorzieningen met toilet, voorportaal en wasbak, waardoor de units ook geschikt zijn voor langduriger gebruik als werkruimte.\n' +
              '\n' +
              'De ligging van ParkRand is ideaal: nabij de A12 en N207, met uitstekende verbindingen richting Gouda, Boskoop, Zoetermeer en Rotterdam. \n' +
              'Station Waddinxveen Triangel ligt op loopafstand, met elk kwartier een treinverbinding richting Gouda en Alphen a/d Rijn – ideaal voor ondernemers en gebruikers die ook met het openbaar vervoer reizen.\n' +
              '\n' +
              'Daarbij sluit de uitstraling van het complex naadloos aan op de architectuur van Park Triangel. Denk aan industriële details, metselwerk en een groene inrichting die is doorgetrokken vanuit de woonwijk. Het maakt van ParkRand de enige werklocatie binnen deze levendige woonomgeving met ruim 2.900 huishoudens.\n' +
              '\n' +
              'Indeling\n' +
              'TYPE G17 – 51 m² (BEGANE GROND)\n' +
              'Bouwnummer 21 is gelegen op de begane grond van het complex, direct naast de hoofdentree – een gunstige en goed bereikbare locatie. Met een bruto vloeroppervlakte van circa 51 m² biedt deze unit extra ruimte en flexibiliteit.\n' +
              '\n' +
              'De unit heeft een afmeting van circa 8,10 meter breed en 6 meter diep. Voorzien van een handmatig bedienbare overheaddeur met een royale doorrijhoogte en -breedte van circa 3 meter. Daarnaast beschikt de unit over een geïntegreerde loopdeur en krachtstroomaansluiting, wat het gebruiksgemak en de functionele inzetbaarheid verder vergroot. Dankzij de ruime opzet en strategische ligging is bouwnummer 21 ideaal voor opslag, werkruimte of kleinschalige bedrijfsactiviteiten.\n' +
              '\n' +
              'Afmetingen:\n' +
              '- Lengte: 6,00 meter\n' +
              '- Breedte: 8,10 meter\n' +
              '\n' +
              'Hoogte & doorrijhoogte per verdieping:\n' +
              '- Eerste verdieping: hoogte ca. 3,35 m | doorrijhoogte ca. 3,00 m\n' +
              '- Tweede verdieping: hoogte ca. 2,70 m | doorrijhoogte ca. 2,35 m\n' +
              '- Derde verdieping: hoogte ca. 2,80 m | doorrijhoogte ca. 2,20 m\n' +
              '\n' +
              'Op elke verdieping van het gebouw bevindt zich een centrale sanitaire ruimte, voorzien van een toilet, een wastafel met koudwaterkraan en een uitstortgootsteen. Deze faciliteiten maken de garageboxen geschikt voor zowel kort als langer gebruik, bijvoorbeeld als werkruimte of atelier. De sanitaire voorzieningen worden centraal onderhouden via de Vereniging van Eigenaren (VvE), wat bijdraagt aan een nette en verzorgde uitstraling van het complex.\n' +
              '\n' +
              'Huurcondities:\n' +
              'Type G17 - 51 m2 BESCHIKBAAR:\n' +
              'Vanaf € 600,- per maand excl. BTW, € 7.200,- per jaar excl. BTW.\n' +
              '\n' +
              'Servicekosten:\n' +
              'Bijdrage servicekosten € 90,- per maand per unit.\n' +
              '\n' +
              'Huurtermijn:\n' +
              'In overleg.\n' +
              '\n' +
              'Huuringangsdatum:\n' +
              'Beschikbaar na oplevering, verwacht in september 2025.\n' +
              '\n' +
              'Huurprijsherziening:\n' +
              'Jaarlijks, voor het eerst één jaar na huuringangsdatum, op basis van de wijziging van het maandprijsindexcijfer volgens de consumentenprijsindex (CPI), reeks CPI-werknemers Laag (2015=100) zoals gepubliceerd door het Centraal Bureau voor de Statistiek (CBS).\n' +
              '\n' +
              'Huurbetaling:\n' +
              'Per maand vooruit.\n' +
              '\n' +
              'Waarborgsom:\n' +
              'Drie (3) maanden huur incl. BTW.\n' +
              '\n' +
              'Huurovereenkomst:\n' +
              'Conform model ROZ.\n' +
              '\n' +
              'BTW:\n' +
              'Verhuurder wenst te opteren voor BTW-belaste huur en verhuur. Ingeval huurder de BTW niet kan verrekenen zal de huurprijs worden verhoogd ter compensatie van de gevolgen van het vervallen van de mogelijkheid om te opteren voor BTW-belaste huur.\n' +
              '\n' +
              'Bijzonderheden\n' +
              '- 24/7 toegankelijk via een elektrische schuifpoort\n' +
              '- Elke box is bereikbaar met de auto\n' +
              '- Voorzien van (elektrische) overheaddeur, verlichting en stroomaansluiting\n' +
              '- Sanitaire voorzieningen op elke verdieping (toilet, voorportaal, wasbak)\n' +
              '- Elke unit heeft een eigen postadres, dus ook interessant om zakelijk te gebruiken\n' +
              '- Veilig en representatief terrein met cameratoezicht\n' +
              '\n' +
              'De verhuur is gestart en de eerste units worden binnenkort opgeleverd. Wil jij verzekerd zijn van een garagebox op deze unieke en goed bereikbare locatie? Neem vandaag nog contact met ons op voor de mogelijkheden!\n' +
              '\n' +
              'Deze vrijblijvende objectinformatie is met de meeste zorg samengesteld, maar voor de juistheid daarvan kunnen wij geen aansprakelijkheid aanvaarden. Ook kan aan de vermelde gegevens geen enkel recht worden ontleend. Alle informatie is enkel en alleen bedoeld voor de presentatie van het object en niet meer dan een uitnodiging tot het doen van een bod. Eventueel bijgevoegde plattegronden zijn slechts indicatief.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.228Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.246Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.264Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031254_1jpu5',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'TE HUUR!\n' +
              '\n' +
              'Uitzonderlijk luxe hoek appartement op de begane grond van 102m² die alles biedt wat je nodig hebt: een royale tuin (ruim 90m²!) op het zuidoosten, een moderne open woonkeuken met kookeiland én een eigen parkeerplaats. De woning heeft energielabel A++. \n' +
              '\n' +
              'Gelegen op het gloednieuwe Centrumeiland, geniet je hier van de perfecte combinatie van comfort en duurzaamheid. De woning is hoogwaardig afgewerkt en voorzien van alle moderne gemakken. Denk aan vloerverwarming, een stijlvolle PVC-vloer, fraaie inbouwkasten, en een geïntegreerd soundsysteem in het plafond. De grote schuifpuien verbinden binnen en buiten naadloos, terwijl de elegante zonwerende screens het geheel compleet maken. Dankzij de extra hoge plafonds profiteer je bovendien van een zee aan licht.\n' +
              '\n' +
              'INDELING \n' +
              'Je betreedt de woning via de gezamenlijke voordeur met direct links de eigen entree van de woning óf nog logischer, via de achtertuin, waar je ook de auto parkeert. De keuken is een echte eyecatcher: luxe uitgevoerd (Siemens apparatuur) en voorzien van veel kastruimte, hoogwaardige inbouwapparatuur en een ruim kookeiland met bar. Een heerlijke plek om de dag te beginnen! Vanuit de keuken loop je door naar de woonkamer dat dankzij de grote raampartijen een prachtig uitzicht biedt op de tuin. De wandpanelen en de stalen deuren zorgen ervoor dat de woning luxe en sfeer uitstraalt. De tuin is toegankelijk via prachtige houten schuifdeuren, die je op mooie dagen helemaal open kunt zetten om binnen en buiten naadloos met elkaar te verbinden. Wordt het te zonnig? Dan zorgen de elektrische screens voor aangename schaduw. De tuin is maar liefst 9 meter breed! \n' +
              '\n' +
              'Via fraaie stalen deuren zijn de beide slaapkamers te bereiken aan de voorzijde van de woning. De master bedroom is voorzien van een strakke inbouwkast en biedt enorm veel ruimte. De andere (slaap)kamer is nu ingericht als verlengde van de woonkamer maar kan makkelijk als tweede slaapkamer worden ingericht. Langs de wand, fijne grote inbouwkasten, met o.a. de wasmachine en droger netjes weggewerkt. \n' +
              '\n' +
              'Centraal in de woning bevinden zich een separaat toilet en de luxe badkamer met inloopdouche en wastafel. Achter de woning ligt de eigen parkeerplaats, waar de voorbereidingen voor een laadpaal al zijn getroffen. Er is een deel parkeerplaats, ideaal voor bakfietsen of als er een bezoek komt. In de hal (bij de voordeur) bevindt zich een trapkast waar o.a. de warmtepomp en de boiler opgesteld staan.\n' +
              '\n' +
              'OMGEVING\n' +
              'Centrumeiland is een van de nieuwste uitbreidingen van IJburg en zal uiteindelijk zo’n 1300 woningen tellen. Dit duurzame en autoluwe gebied biedt volop ruimte voor groen, pleinen en voetgangers. De woning ligt op een steenworp afstand van het water en het Haveneiland van IJburg, waardoor je hier de perfecte balans vindt tussen stadse levendigheid en rust.\n' +
              '\n' +
              'Op Centrumeiland worden diverse buurtveldjes, een plein met sport- en spelvoorzieningen, winkels en scholen gerealiseerd. Daarnaast komt er een prachtige wandel- en hardlooproute langs de oevers rondom het eiland. Binnen enkele minuten sta je op het strand van IJburg of peddel je met een SUP het meer op. In de directe omgeving vind je tal van voorzieningen: meerdere basisscholen, een middelbare school, diverse kinderopvangcentra en supermarkten. De Pampuslaan en IJburglaan bieden een divers aanbod aan winkels en horecagelegenheden, terwijl de jachthaven en het Winkelcentrum IJburg extra mogelijkheden bieden voor ontspanning en boodschappen. Sportliefhebbers kunnen terecht bij hockey-, tennis- en voetbalclubs, evenals diverse watersportfaciliteiten zoals windsurfen. Voor natuurliefhebbers ligt het Diemerpark op slechts vijf minuten fietsen. Dit prachtige natuurgebied, inclusief de Diemer Vijfhoek, biedt een ideale plek om te wandelen en vogels te spotten. Ook Boerderij op IJburg en het sfeervolle Paviljoen Puur, perfect voor een lunch of borrel op zondag, zijn in de buurt. De bereikbaarheid is uitstekend: tramlijn 26 (met een halte om de hoek) brengt je in slechts 15 minuten naar Amsterdam Centraal, en met de auto ben je snel op de A1, A2, A9 of via de IJburglaan op de A10 en in het centrum van de stad.\n' +
              '\n' +
              'VOORWAARDEN\n' +
              '- Contract voor bepaalde tijd (Model C)\n' +
              '- Huurprijs is inclusief stookkosten (exclusief water, elektra en overige gebruikerskosten)\n' +
              "- De woning wordt gestoffeerd opgeleverd (de foto's zijn ter impressie)\n" +
              '- Enkele meubels zoals barkrukken blijven achter\n' +
              '- Woning wordt opgeleverd zoals de staat tijdens de bezichtiging\n' +
              '- Inkomenseis minimaal 2x de maandhuur (netto)\n' +
              '- Borg is gelijk aan 2 maanden huur\n' +
              '- Garantstelling is niet mogelijk, vast inkomen vereist\n' +
              '- Niet geschikt voor woningdelers\n' +
              '- Huisdieren en roken niet toegestaan\n' +
              '- Kandidaat-huurders dienen diverse documenten aan te leveren ter inkomensverificatie\n' +
              '- Op korte termijn beschikbaar\n' +
              '- Onder voorbehoud gunning verhuurder.\n' +
              '\n' +
              'BIJZONDERHEDEN \n' +
              '- Duurzaam & energiezuinig: Energielabel A++, voorzien van een warmtepomp, airconditioning en vloerverwarming. \n' +
              '- Hoogwaardige afwerking: kwalitatief gebouwde woning met luxe materialen, hoge stalen deuren en moderne installaties. \n' +
              '- Ruim 9 meter brede tuin met schuifpuien (incl. screens) die binnen en buiten verbinden. \n' +
              '- Dolby atmos geluidssysteem aanwezig\n' +
              '- Eigen parkeerplaats direct achter de woning\n' +
              '- Woonoppervlakte: 102m² (meetrapport aanwezig). \n' +
              '- Nieuwbouw uit 2024\n' +
              '- Ideale ligging: op slechts vijf minuten lopen van het populaire strand van IJburg, winkels aan de Pampuslaan en de eindhalte van tram 26 (15 minuten naar CS) ligt om de hoek. \n' +
              '\n' +
              'De informatie is met grote zorgvuldigheid samengesteld doch voor de juistheid van de inhoud kunnen wij niet instaan en er kunnen derhalve geen rechten aan worden ontleend. De inhoud is puur informatief en mag niet worden beschouwd als een aanbod. Daar waar gesproken wordt over inhoud, oppervlakten of afmetingen moeten deze worden beschouwd als indicatief en als circa maten. U dient als koper zelf onderzoek te verrichten naar zaken die voor u van belang zijn. Wij raden u in dat verband aan uw eigen NVM-makelaar in te schakelen.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.264Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.279Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.295Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.309Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031299_boktb',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'JA! Prachtig, ruim en deels gemeubileerd appartement in de bruisende wijk ‘De Pijp’.\n' +
              'Deze lichte doorzonwoning beschikt over een royale woonkeuken, sfeervolle woonkamer-en-suite, twee slaapkamers en een luxe badkamer. Via de trap bereikt u het zonnige dakterras van ca. 31 m².\n' +
              'Met alle voorzieningen binnen handbereik is dit een instapklaar appartement waar u direct van kunt genieten!\n' +
              '\n' +
              'INDELING \n' +
              'DERDE VERDIEPING\n' +
              '- Gemeenschappelijke trapopgang naar de eigen voordeur op de tweede verdieping;\n' +
              '- Vanuit de entree leidt een trap naar de derde verdieping;\n' +
              '- Hier bevindt zich de overloop met garderobe en een hal die toegang biedt tot alle vertrekken, inclusief een separaat toilet;\n' +
              '- De lichte woonkamer-en-suite heeft aan de voorzijde een royaal zitgedeelte met erker en op maat gemaakte wandkast;\n' +
              '- Aan de achterzijde ligt de nette woonkeuken met inbouwapparatuur: 5-pits gasfornuis, afzuigkap, vaatwasser, koelkast, vriezer, oven en magnetron;\n' +
              '- De keuken biedt tevens ruimte voor een royale eettafel waar u met een groot gezelschap kunt dineren.\n' +
              '\n' +
              'SLAPEN EN BADEN\n' +
              '- De master bedroom aan de voorzijde beschikt over twee vaste kasten;\n' +
              '- De tweede slaapkamer aan de rustige achterzijde is ook ideaal als thuiskantoor;\n' +
              '- De recent gerenoveerde badkamer is uitgerust met een ruime inloopdouche, ligbad en dubbele wastafel met geïntegreerde kranen.\n' +
              '\n' +
              'DAKTERRAS\n' +
              '- Via de trap op de overloop is de vierde verdieping te bereiken;\n' +
              '- Hier bevindt zich een hal met afgesloten bergkast en openslaande deuren naar het riante dakterras;\n' +
              '- Het terras biedt de hele dag zon, veel privacy en is perfect om te dineren of te borrelen met vrienden;\n' +
              '- Met een oppervlakte van ca. 31 m² is dit de ideale afsluiting van de woning.\n' +
              '\n' +
              'HUURCONTRACT\n' +
              '- Huurtermijn: Type A, minimaal 12 maanden;\n' +
              '- Borg: 2 maanden kale huur;\n' +
              '- Huurprijs is exclusief gas/water/elektra/internet;\n' +
              '- Per direct beschikbaar;\n' +
              '- De woning wordt deels gemeubileerd aangeboden;\n' +
              '- Geen huisdieren en rokers toegestaan;\n' +
              '- Ideaal voor een gezin of een koppel.\n' +
              '\n' +
              'WOONOPPERVLAK \n' +
              '- Het gebruiksoppervlak wonen bedraagt ca. 93,50 m²;  \n' +
              '- De gebouwgebonden buitenruimte bedraagt ca. 30,60 m2.\n' +
              'NB. De opgegeven oppervlakte is door een gespecialiseerd bedrijf gemeten conform NEN2580.\n' +
              '\n' +
              'LOCATIE \n' +
              '- Gelegen in de levendige wijk ‘De Pijp’ (Stadsdeel Zuid), grenzend aan de Rivierenbuurt;\n' +
              '- Alle voorzieningen binnen loopafstand;\n' +
              '- Boodschappen doet u op de Rijnstraat of de Van Woustraat met diverse supermarkten en speciaalzaken;\n' +
              '- Tal van restaurants en cafés in de Rivierenbuurt, De Pijp en langs de Weesperzijde;\n' +
              '- Voor ontspanning ligt het Sarphatipark en Martin Luther Kingpark enkele minuten fietsen en de Amstel om de hoek;\n' +
              '- Een omgeving die nooit zal vervelen.\n' +
              '\n' +
              'BEREIKBAARHEID EN PARKEREN \n' +
              '- Goed bereikbaar met de auto via de ring A10 (afslag S110);\n' +
              '- Nabij tram- en bushaltes (tram 3, 4, 12 en bus 62 en 65);\n' +
              '- Metrohaltes De Pijp en Wibautstraat, evenals NS-station Amstel, zijn snel bereikbaar;\n' +
              '- Parkeren op de openbare weg is betaald van maandag t/m zaterdag van 09.00 tot 24.00 uur en op zondag van 12.00 tot 24.00 uur (bron: gemeente Amsterdam).\n' +
              '\n' +
              'BIJZONDERHEDEN\n' +
              '- Voorzien van dubbele beglazing;\n' +
              '- Twee slaapkamers en een modern gerenoveerde badkamer;\n' +
              '- Sfeervolle en lichte woonkamer-en-suite;\n' +
              '- Ruime woonkeuken;\n' +
              '- Riant dakterras van ca. 31 m²;\n' +
              '- Alle voorzieningen binnen handbereik;\n' +
              '- Deels gemeubileerd.\n' +
              '\n' +
              '-----------------------------------------------------------------------------------\n' +
              '\n' +
              'YES! Beautiful, spacious and partly furnished apartment in the vibrant neighborhood ‘De Pijp’.\n' +
              'This bright through-living apartment features a generous kitchen-diner, an atmospheric living room en suite, two bedrooms and a luxury bathroom. A staircase leads you to the sunny roof terrace of approx. 31 m².\n' +
              'With all amenities within easy reach, this apartment is ready to move into and enjoy right away!\n' +
              '\n' +
              'LAYOUT\n' +
              'THIRD FLOOR\n' +
              '- Shared staircase leading to the private front door on the second floor;\n' +
              '- From the entrance, a staircase leads up to the third floor;\n' +
              '- On this floor you will find the landing with wardrobe space and a hallway giving access to all rooms, including a separate toilet;\n' +
              '- The bright living room en suite offers at the front a spacious sitting area with bay window and custom-made wall cabinet;\n' +
              '- At the rear is the well-equipped kitchen with built-in appliances: 5-burner gas hob, extractor hood, dishwasher, refrigerator, freezer, oven and microwave;\n' +
              '- The kitchen also provides enough space for a large dining table, perfect for entertaining.\n' +
              '\n' +
              'SLEEPING & BATHING\n' +
              '- The master bedroom at the front includes two built-in wardrobes;\n' +
              '- The second bedroom at the quiet rear is also ideal as a home office;\n' +
              '- The recently renovated bathroom features a spacious walk-in shower, bathtub and double washbasin with integrated taps.\n' +
              '\n' +
              'ROOF TARRACE\n' +
              '- From the landing, stairs lead to the fourth floor;\n' +
              '- Here you enter a hallway with a storage cupboard and French doors opening onto the generous roof terrace;\n' +
              '- The terrace enjoys sun all day, plenty of privacy and is perfect for dining or drinks with friends;\n' +
              '- With a surface of approx. 31 m², it completes the home.\n' +
              '\n' +
              'RENTAL CONDITIONS\n' +
              '- Rental period: Type A, minimum of 12 months;\n' +
              '- Deposit: 2 months’ rent;\n' +
              '- Rent is excluding gas/water/electricity/internet;\n' +
              '- Available immediately;\n' +
              '- The property is offered partly furnished;\n' +
              '- No pets or smoking allowed;\n' +
              '- Ideal for a family or a couple.\n' +
              '\n' +
              'LIVING AREA\n' +
              '- Usable living space: approx. 93.5 m²;\n' +
              '- External outdoor space: approx. 30.6 m².\n' +
              'Note: the stated surface has been measured by a specialized company in accordance with NEN2580.\n' +
              '\n' +
              'LOCATION\n' +
              '- Situated in the lively neighborhood ‘De Pijp’ (Amsterdam Zuid), bordering the Rivierenbuurt;\n' +
              '- All amenities are within walking distance;\n' +
              '- Groceries can be done on Rijnstraat or Van Woustraat with a wide range of supermarkets and specialty shops;\n' +
              '- Numerous restaurants and cafés can be found in Rivierenbuurt, De Pijp and along the Weesperzijde;\n' +
              '- For relaxation, Sarphatipark and Martin Luther King Park are just a few minutes by bike, with the Amstel river around the corner;\n' +
              '- An area that truly never gets boring.\n' +
              '\n' +
              'ACCESSIBILITY & PARKING\n' +
              '- Easily accessible by car via the A10 ring road (exit S110);\n' +
              '- Close to tram and bus stops (tram 3, 4, 12 and bus 62 and 65);\n' +
              '- Metro stations De Pijp as well as Amstel railway station are within easy reach;\n' +
              '- On-street parking is paid from Monday to Saturday from 09:00 to 24:00 and on Sunday from 12:00 to 24:00 (source: City of Amsterdam).\n' +
              '\n' +
              'PARTICULARS\n' +
              '- Double glazing throughout;\n' +
              '- Two bedrooms and a modern renovated bathroom;\n' +
              '- Bright and atmospheric living room en suite;\n' +
              '- Spacious kitchen-diner;\n' +
              '- Large roof terrace of approx. 31 m²;\n' +
              '- All amenities close by;\n' +
              '- Partly furnished.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.309Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.326Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031316_z1j1g',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Uniek en royaal appartement op de 11e etage van het luxe complex ‘De Engel’\n' +
              '\n' +
              'Dit uitzonderlijke appartement van 250 m² is gelegen op de 11e etage van het prestigieuze complex ‘De Engel’. Wat dit appartement bijzonder maakt, is de volledige privacy: de 11e etage is uitsluitend bereikbaar met een eigen huissleutel, wat zorgt voor een gevoel van exclusiviteit en rust.\n' +
              '\n' +
              'Indeling\n' +
              'Bij binnenkomst wordt u verwelkomd in een ruime hal die toegang biedt tot de diverse vertrekken. De majesteuze woonkamer van maar liefst 100 m² vormt het hart van het appartement. Dankzij de grote raampartijen rondom stroomt het daglicht rijkelijk naar binnen, wat de ruimte een lichte en open sfeer geeft. Het uitzicht is adembenemend en biedt panoramische vergezichten over Maassluis, omliggende kernen, polders en in de verte de skyline van zowel Den Haag als Rotterdam. De loggia, een heerlijk buitenvertrek, is de ideale plek om even tot rust te komen en het uitzicht te bewonderen. De stijlvolle, strakke raamkozijnen van hardhout in het woninggedeelte steken prachtig af tegen de witte wanden, waardoor het interieur een moderne en verfijnde uitstraling krijgt.\n' +
              '\n' +
              'De Siematic keuken is uitgerust met hoogwaardige Siemens apparatuur, waaronder een halogeen kookplaat, afzuigkap, vaatwasser, oven, koelkast en een granieten aanrechtblad dat is verlengd voor extra werkruimte. De keuken is ideaal voor het bereiden van culinaire hoogstandjes, maar biedt ook voldoende ruimte voor een gezellig dinertje.\n' +
              '\n' +
              'Bij de entree en in de woonkeuken is een videofoonsysteem geïnstalleerd, zodat u uw visite veilig en gemakkelijk kunt binnenlaten.\n' +
              '\n' +
              'Het appartement beschikt over drie ruime slaapkamers, waarvan de master bedroom (ca. 20 m²) is uitgerust met een luxe en suite badkamer. Deze royale badkamer is volledig betegeld en voorzien van een ligbad met opstapje, een douchehoek, dubbele wastafel, toilet met hangend closet en urinoir. Slaapkamer 2 en 3 zijn elk ongeveer 13 m² groot. De halfopen hobbykamer van ca. 10 m², die grenst aan de woonkamer, biedt veel mogelijkheden en zou eenvoudig kunnen worden omgevormd tot een vierde slaapkamer. Deze kamers delen een tweede badkamer, die eveneens van alle gemakken is voorzien, inclusief douchehoek, toilet met hangend closet, urinoir en wastafel.\n' +
              '\n' +
              'De wasruimte bevindt zich naast de tweede badkamer en herbergt tevens de tweede wtw-installatie, wat bijdraagt aan het comfort en de energie-efficiëntie van het appartement.\n' +
              '\n' +
              'Dit appartement is voorzien van energielabel A en beschikt over uitstekende isolatie, waaronder betonnen vloeren en HR+ glas in de ramen van zowel het woninggedeelte als de loggia. Het appartement is uitgerust met vloerverwarming en -koeling via een collectieve warmtepompinstallatie. Twee wtw-installaties zorgen voor optimale ventilatie in het gehele appartement. Het gebouw verkeert in uitstekende staat van onderhoud, en de schoonmaak van de algemene ruimtes wordt zorgvuldig verzorgd.\n' +
              '\n' +
              'Onder het complex bevindt zich een afgesloten parkeerkelder met op afstand bedienbare deuren. In deze kelder bevinden zich twee ruime garageboxen van 24 m², ideaal voor het stallen van voertuigen of opslag. Daarnaast is er de mogelijkheid om buiten naast het complex vrij te parkeren. Het complex biedt verder een gezamenlijke tuin, waar u in de zomermaanden heerlijk kunt ontspannen en genieten van de buitenlucht.\n' +
              '\n' +
              'De ligging van het complex is ideaal. Op loopafstand vindt u alle denkbare voorzieningen, waaronder winkelcentrum De Koningshoek, het zorgcomplex ‘De Vloot’, een sportschool en het metrostation, met verbindingen naar Rotterdam en het Hoek van Holland-strand. Maassluis biedt verder een charmant, authentiek centrum met gezellige pleinen, monumentale panden en de Oude Haven. De Waterweg en het park zijn op wandelafstand, perfect voor een rustige wandeling. Uitvalswegen naar Den Haag, Rotterdam en het Westland liggen op korte afstand, wat de locatie uiterst praktisch maakt.\n' +
              '\n' +
              'Bijzonderheden: \n' +
              '- Royaal penthouse met eigen verdieping;\n' +
              '- Voorzien van 1 garagebox in de afgesloten parkeerkelder (mogelijkheid om de tweede garagebox bij te huren voor \n' +
              '€ 250,- per maand);\n' +
              '- Licht appartement vanwege de velen raampartijen; \n' +
              '- Bouwjaar 2002;\n' +
              '- Energielabel A;\n' +
              '- 3 slaapkamers; \n' +
              '- Oplevering in overleg. \n' +
              '\n' +
              'Voorwaarden verhuur: \n' +
              '- Huuringangsdatum: in overleg, kan snel;\n' +
              '- Huurprijs: € 3.250,- per maand, exclusief gas/water/licht, exclusief voorschot van € 300,- per maand (verwarming/koeling en warmwatervoorziening); \n' +
              '- Huurovereenkomst: conform ROZ huurcontract;\n' +
              '- Huurperiode: in overleg;\n' +
              '- Borgstelling bij aanvang huur: 1 maand huur;\n' +
              '- Huurder is verplicht zich in te schrijven bij de gemeente op dit adres; \n' +
              '- Het houden van huisdieren in de woning is enkel met toestemming van verhuurder toegestaan.\n' +
              '\n' +
              'Procedure\n' +
              'Heeft u interesse in dit unieke plekje in Maassluis? Dan is de procedure als volgt:\n' +
              'Stuur een e-mail naar  met hierin uw contactgegevens en de bevestiging van uw interesse.\n' +
              'Wij zullen u vervolgens enkele documenten toesturen.\n' +
              'Uw inschrijving is definitief, zodra wij de documenten ingevuld retour hebben ontvangen.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.326Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.501Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.519Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.541Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.562Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.584Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.606Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.627Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.647Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031636_j8rk4',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: '**** FOR THE ENGLISH TEXT, PLEASE SEE BELOW****\n' +
              '\n' +
              'Luxe 3-slaapkamerappartement in het populaire Bezuidenhout – volledig gerenoveerd en instapklaar\n' +
              '\n' +
              'Welkom in dit prachtige, volledig gerenoveerde 3-slaapkamerappartement in de geliefde wijk Bezuidenhout in Den Haag. Dit moderne appartement is nog door niemand bewoond en is tot in de puntjes afgewerkt met hoogwaardige materialen zoals natuurmarmer, Porcelanosa tegels, Villeroy & Boch sanitair en topmerken als Siemens, Boretti, AEG en Samsung.\n' +
              '\n' +
              'Indeling\n' +
              'Het appartement beschikt over een ruime, lichte woonkamer met grote raampartijen en toegang tot een royaal balkon. De open keuken is modern en volledig uitgerust met luxe inbouwapparatuur. Er zijn drie goed bemeten slaapkamers, allen voorzien van nieuw meubilair, waaronder nieuwe bedden, matrassen en nachtkastjes.\n' +
              'De stijlvolle badkamer is voorzien van een ligbad, een aparte inloop-regendouche en een dubbele wastafel. Daarnaast is er een separaat toilet aanwezig.\n' +
              '\n' +
              'De designvloer met Porcelanosa tegels geeft het appartement een luxe uitstraling. Al het meubilair en apparatuur is nieuw en compleet – direct klaar voor bewoning. Keukengerei zoals borden en bestek kunnen op verzoek geleverd worden.\n' +
              '\n' +
              'Ligging\n' +
              'Bezuidenhout is een van de meest gewilde buurten van Den Haag, met een uitstekende ligging ten opzichte van zowel de stad als de natuur. Binnen enkele minuten wandel je naar het Haagse Bos en Paleis Huis ten Bosch. Het centrum van Den Haag is op loopafstand (ca. 10 minuten), en station Den Haag Centraal ligt om de hoek.\n' +
              '\n' +
              'De uitvalswegen naar Amsterdam, Rotterdam en Utrecht zijn binnen 2 minuten bereikbaar. Internationale scholen (zoals de International School of The Hague) en diverse Nederlandse basisscholen, waaronder de Liduinaschool, bevinden zich op loopafstand. Het is een kindvriendelijke omgeving\n' +
              '\n' +
              'BIJZONDERHEDEN:\n' +
              '* Geheel gerenoveerd in 2025\n' +
              '* Voorzien van energielabel A\n' +
              '* De woning is gemeubileerd beschikbaar en kan volledig voorzien worden van servies, bestek en keukengerei\n' +
              '* Balkon op het zuiden\n' +
              '\n' +
              'VOORWAARDEN BIJ VERHUUR!! \n' +
              '\n' +
              '* NIET BESCHIKBAAR VOOR STUDENTEN!!\n' +
              '* Huurovereenkomst voor de minimale periode van één jaar.\n' +
              '* Waarborgsom minimaal één maand huur, te betalen bij aanvang van de huurovereenkomst.\n' +
              '* Huurprijs is exclusief water, elektriciteit, internet en tv\n' +
              '* Geen huisdieren toegestaan\n' +
              '* Roken in de woning is niet toegestaan\n' +
              '* Om in aanmerking te komen voor deze woning dient men het woningaanvraag formulier volledig ingevuld en ondertekend met bijbehorende bijlage bij ons op kantoor in te leveren. (deze wordt na bezichtiging per mail toegezonden)\n' +
              '* Over het toewijzingsbeleid worden geen uitlatingen gedaan.\n' +
              '* VOOR DE HUURDER GEEN BEMIDDELINGSKOSTEN!!!!\n' +
              '\n' +
              'Luxury 3-Bedroom Apartment in Popular Bezuidenhout – Fully Renovated and Move-in Ready\n' +
              '\n' +
              'Welcome to this stunning, fully renovated 3-bedroom apartment located in the highly sought-after Bezuidenhout district of The Hague. This modern apartment has never been lived in and is finished to the highest standards, using premium materials such as natural marble, Porcelanosa tiles, Villeroy & Boch sanitary ware, and top-brand appliances from Siemens, Boretti, AEG, and Samsung.\n' +
              '\n' +
              'Layout\n' +
              'The apartment features a spacious and bright living room with large windows and access to a generous south-facing balcony. The open-plan kitchen is modern and fully equipped with luxury built-in appliances. There are three well-sized bedrooms, all furnished with brand-new furniture including beds, mattresses, and bedside tables.\n' +
              'The stylish bathroom includes a bathtub, a separate walk-in rain shower, and a double sink. There is also a separate toilet.\n' +
              '\n' +
              'The designer floor with Porcelanosa tiling adds a luxurious touch throughout the apartment. All furniture and appliances are brand new and complete – ready for immediate occupancy. Kitchen essentials such as crockery and cutlery can be provided upon request.\n' +
              '\n' +
              'Location\n' +
              'Bezuidenhout is one of the most desirable areas of The Hague, offering an ideal combination of city convenience and green surroundings. The beautiful Haagse Bos and the royal Huis ten Bosch Palace are just a few minutes’ walk away. The city center is within walking distance (approx. 10 minutes), and Den Haag Central Station is right around the corner.\n' +
              '\n' +
              'Major highways to Amsterdam, Rotterdam, and Utrecht are just 2 minutes away. Several international schools (including the International School of The Hague) and a range of Dutch primary schools, such as the Liduina School, are within walking distance. The area is very child-friendly.\n' +
              '\n' +
              'HIGHLIGHTS:\n' +
              '* Fully renovated in 2025\n' +
              '* Energy label A\n' +
              '* Furnished and available with crockery, cutlery, and kitchen utensils upon request\n' +
              '* South-facing balcony\n' +
              '\n' +
              'RENTAL CONDITIONS:\n' +
              '* NOT AVAILABLE FOR STUDENTS!!\n' +
              '* Minimum rental period: 12 months\n' +
              '* Security deposit of at least one month’s rent, payable upon signing the rental agreement\n' +
              '* Rent is excluding water, electricity, internet, and TV\n' +
              '* No pets allowed\n' +
              '* Smoking is not permitted inside the apartment\n' +
              '* To apply for this property, you must submit a fully completed and signed application form with the required documents to our office. (This form will be provided by email after the viewing)\n' +
              '* No statements will be made regarding the selection process\n' +
              '* NO AGENCY FEES FOR TENANTS!!',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.647Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.675Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.698Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031688_v3kp5',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Residentie Molenwijck \n' +
              '\n' +
              'Gerritse Makelaardij en Den Hollander Vastgoed zijn partners van Residentie Molenwijck. Wilt u alvast informatie over het wonen in deze residentie, voordat u Molenwijck wilt binnenstappen, dan maken wij graag een afspraak met u.\n' +
              '\n' +
              'UW EIGEN THUIS\n' +
              'Wonen en leven in uw eigen thuis. In Residentie Molenwijck woont u zelfstandig in een prachtig, comfortabel appartement waar u uw eigen levensstijl voorzet. Wij bieden u services aan die het leven aangenaam maken. Er is ruimte voor uw hobby’s. U heeft bovendien de mogelijkheid om aan te sluiten bij de unieke, gastvrije leefgemeenschap van Residentie Molenwijck. Ieder op zijn eigen manier.\n' +
              '\n' +
              'Naast de privacy van uw eigen appartement biedt wonen in Residentie Molenwijck nog veel meer, zoals gasten die u aan tafel kunt uitnodigen, de technische dienst is er om uw appartement te onderhouden en in de fitnessruimte houdt u uw gezondheid op peil via een gevarieerd aanbod. Elk jaargetijde is het heerlijk om in ons park een frisse neus te halen. U woont in een prachtige, natuurrijke omgeving, op loopafstand van het unieke dorpshart (mét het Witte Kasteel) van Loon op Zand. Bewoners ontvangen dagelijks een gezonde, versbereide maaltijd in hun appartement of in het restaurant. Vrienden en familie zijn ook van harte welkom.\n' +
              '\n' +
              'Bij vragen kunt u terecht bij de receptie, uw appartement maken we schoon, er is een huiswinkel en het team van de zorg is er om bij nood direct te reageren (24 uur per dag / 7 dagen per week). Binnenshuis is er alle ruimte om elkaar te ontmoeten. Visite ontvangt u in uw eigen appartement, het restaurant of op het terras. Of u er nu voor kiest om samen te bridgen, te biljarten of te genieten van een boek: u bent de regisseur van uw eigen thuis.\n' +
              '\n' +
              'HUURPRIJZEN\n' +
              'Let op: de vermelde huurprijs is afhankelijk van de grootte van het appartement. De all-in huurprijzen variëren van € 3.300 (type A, 1 persoon) tot € 4.800 (type E en G, 2 personen). Bewoners betalen per maand een bedrag voor de huur, woonservices, basiszorg, dagelijks een versbereide maaltijd en ontspanning. Wilt u precies weten welke diensten en faciliteiten worden geboden, neem dan contact met ons op.\n' +
              '\n' +
              'SERVICES\n' +
              'Er zijn tal van services die het wonen en leven in Residentie Molenwijck erg aangenaam maken. Ze vergroten uw gevoel van geborgenheid, gemak en ontspanning. Onder de services voor uw appartement noemen we graag de technische dienst, de keukenbrigade en uw vaste hulp. U kunt de technische dienst inhuren voor het invullen van uw woonwensen. De chef-kok staat paraat om u dagelijks met een steeds wisselend drie-gangen keuzemenu te verwennen. Dat kunt u in uw appartement nuttigen of in het restaurant: aan u de keuze. Uw appartement blijft netjes dankzij uw eigen vaste hulp.\n' +
              '\n' +
              'FACILITEITEN\n' +
              'Genieten van het leven staat voorop in Residentie Molenwijck. Om dat te faciliteren kunt u voor allerhande wensen terecht bij onze voorzieningen. Zo zijn er het restaurant, het zonneterras en het Grand Café. En wat te denken van een fitness, een huiswinkel, een kapsalon en schoonheidssalon, de receptie en een ontspanningsruimte met een speciaal bad en infrarood sauna? In de serene devotieruimte vindt wekelijks een dienst plaats. Alles wat uw leven en dat van uw gasten aangenaam maakt, is onderdeel van dit unieke woonconcept. Het is zelfs mogelijk dat uw gasten blijven overnachten in speciaal daarvoor ingerichte gastenkamers.\n' +
              '\n' +
              'ONTSPANNING\n' +
              'U kunt een keuze maken uit diverse, zeer uiteenlopende activiteiten, die uw leven extra invulling en jus geven. Door deel te nemen aan een activiteit of door zelf mee te helpen bij de organisatie, voelt u zich middenin het leven staan en kunt u met medebewoners gemeenschappelijke interesses delen. Op deze manier is het maken van nieuwe contacten een stuk eenvoudiger. De Vereniging van Bewoners coördineert en beheert de activiteiten die de bewoners organiseren. Er zijn culinaire, culturele, recreatieve en sportieve activiteiten.\n' +
              '\n' +
              'ZORG\n' +
              'Op het moment dat u zorg nodig heeft, kunt u erop vertrouwen dat er zorg is. Wij bieden thuiszorg. Daarnaast is het mogelijk op indicatie uitgebreidere zorg te ontvangen. In de nacht en in het weekend zijn altijd verpleegkundigen of verzorgenden aanwezig. Er is vanzelfsprekend ook aandacht voor bewoners met dementie. Voor hen is er een dagopvang van maandag tot en met vrijdag. Molenwijck werkt met een vast team van verpleegkundigen en verzorgenden, waardoor u ook met hen een relatie kunt opbouwen. Vanzelfsprekend is er in elk appartement alarmering voor uw veiligheid aanwezig.\n' +
              '\n' +
              'KENNISMAKEN?\n' +
              'Is uw interesse gewekt en wilt u eerst eens van gedachten wisselen over het wonen in Residentie Molenwijck? Dan denken wij graag met u mee. Een volgende stap is een kennismaking en het bezichtigen van de Residentie en de (beschikbare) appartementen. De open dagen zijn een gezellige en laagdrempelige manier om eens binnen te kijken, de makelaar kan u met ons in contact brengen. Op de website van Residentie Molenwijck vindt u meer informatie en kunt u ook enkele ervaringsverhalen lezen.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.698Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.719Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.741Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.764Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031753_41umk',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: Build year must be between 1800 and 2030
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: 'Build year must be between 1800 and 2030',
          path: [ 'year' ],
          type: 'custom',
          context: { label: 'year', value: '1750', key: 'year' }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.763Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.786Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.809Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031798_m3c8x',
  source: 'funda',
  duration: '10ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "area" must be greater than or equal to 10
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"area" must be greater than or equal to 10',
          path: [ 'area' ],
          type: 'number.min',
          context: { limit: 10, value: 1, label: 'area', key: 'area' }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.808Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.830Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.852Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.874Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.898Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031886_k7eqt',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'VOOR DE NEDERLANDSE OMSCHRIJVING, ZIE ONDER ENGELSE TEKST\n' +
              '\n' +
              'Situated in one of the most sought-after areas of Amsterdam – the vibrant Zuidas – this exceptionally luxurious city villa is now available for rent. This large corner residence offers a unique combination of space, comfort, privacy, and high-end finishes, spread across multiple floors and equipped with every modern convenience. With five bedrooms, three bathrooms, a generous garden, rooftop terrace, and optional private parking, this is an exceptional opportunity for those seeking the highest standard of living in Amsterdam. This property is offered furnished but can also be rented unfurnished upon consultation.\n' +
              '\n' +
              'GROUND FLOOR:\n' +
              '\n' +
              'The home is entered through a stylish front garden and entrance. The spacious and light-filled living room features a cozy gas fireplace and offers direct access to the lovely rear garden of approximately 50 m² through large sliding doors. The open-plan kitchen is a true culinary haven, fully equipped with premium built-in appliances including a refrigerator, freezer, oven, dishwasher, induction cooktop with integrated extractor, and a built-in wine climate cabinet.\n' +
              '\n' +
              'FIRST FLOOR:\n' +
              '\n' +
              'This level offers three well-sized bedrooms, perfect for children and your guests. This floor also contains two modern bathrooms, each equipped with a shower and toilet.\n' +
              '\n' +
              'SECOND FLOOR:\n' +
              '\n' +
              'The first floor hosts the impressive master bedroom with access to a private balcony. Through a spacious walk-in closet, you reach the luxurious en-suite bathroom, complete with a freestanding bathtub, walk-in shower, double vanity unit, and toilet. Above the master bedroom, a mezzanine has been created and styled as a comfortable lounge with a TV. Additionally, there is a second bedroom on this floor, ideal as a home office.\n' +
              '\n' +
              'THIRD FLOOR:\n' +
              '\n' +
              'A fixed stairs leads to the top floor with the stunning rooftop terrace, where you can relax in and enjoy panoramic views over the Zuidas – a true metropolitan experience from the comfort of your own home.\n' +
              '\n' +
              'BASEMENT LEVEL:\n' +
              '\n' +
              'The basement features a practical laundry room with washer and dryer, a separate toilet room, and a spacious storage room. From here, you have direct access to the underground parking garage, where your private parking space is located right in front of the house – with the option to install a charging station for electric vehicles. This parking space is optionally offered for €500,00 per month.\n' +
              '\n' +
              'LOCATION AND ACCESSIBILITY:\n' +
              '\n' +
              'The property is situated in the heart of the Zuidas, Amsterdam’s international business district. The surrounding area offers a wide range of high-end amenities including shops, restaurants, cafes, sports facilities, and cultural venues. The Vrije Universiteit, Amsterdamse Bos, Gijsbrecht van Aemstelpark, and Amstelpark are also nearby. Within minutes, you can walk to Amsterdam Zuid Station, offering excellent train, metro, and tram connections to Amsterdam city centre, Schiphol Airport, and other parts of the Netherlands. By car, the A10 ring road is easily accessible.\n' +
              '\n' +
              'GEORGE GERSHWINLAAN 7 COMBINES ELEGANCE, COMFORT, AND URBAN SOPHISTICATION IN PERFECT HARMONY. If you are looking for an exclusive rental home where you can move in immediately and enjoy the very best that Amsterdam has to offer, this is your chance.\n' +
              '\n' +
              'Published measurements are not NEN measured.\n' +
              '\n' +
              '-----------------------------------------------------------------------------------------------------------------------------------\n' +
              '\n' +
              'Op een van de meest gewilde locaties van Amsterdam – de bruisende Zuidas – staat deze zeer luxueuze stadsvilla te huur. De riante hoekwoning biedt een unieke combinatie van ruimte, comfort, privacy en luxe, verdeeld over meerdere etages en voorzien van alle denkbare moderne gemakken. Met vijf slaapkamers, drie badkamers, een royale tuin, dakterras en een optionele privéparkeerplaats is dit een uitzonderlijke kans voor wie op zoek is naar het hoogste woonniveau in Amsterdam. Deze woning wordt gemeubileerd aangeboden maar kan in overleg ook gestoffeerd worden gehuurd.\n' +
              '\n' +
              'BEGANE GROND:\n' +
              'U betreedt de woning vanuit de voortuin via een stijlvolle entree. De ruime en lichte woonkamer is voorzien van een sfeervolle gashaard en biedt via een grote schuifpui directe toegang tot de fraai aangelegde achtertuin van ca. 50 m². De open keuken is een waar culinair paradijs, uitgerust met hoogwaardige inbouwapparatuur: een koelkast, vriezer, oven, vaatwasser, inductiekookplaat met geïntegreerde afzuiger en een ingebouwde wijnklimaatkast.\n' +
              '\n' +
              'EERSTE ETAGE:\n' +
              '\n' +
              'TWEEDE ETAGE:\n' +
              'Hier bevindt zich de indrukwekkende master bedroom met toegang tot een balkon. Via een royale inloopkast bereikt u de luxueuze en-suite badkamer, compleet met een vrijstaand ligbad, inloopdouche, dubbel wastafelmeubel en toilet. Boven deze slaapkamer is een entresol ingericht als comfortabele lounge met TV. Daarnaast is er op deze verdieping nog een tweede slaapkamer die prima kan dienen als thuiskantoor.\n' +
              '\n' +
              'DERDE ETAGE:\n' +
              'Via een vaste trap bereikt u het prachtige dakterras. Hier kunt u in alle rust genieten van een panoramisch uitzicht over de Zuidas – een metropolische ervaring vanuit uw eigen woning.\n' +
              '\n' +
              'KELDERVERDIEPING:\n' +
              'In de kelder vindt u een praktische wasruimte met wasmachine en droger, een separaat toilet en een ruime berging. Vanuit hier heeft u directe toegang tot de onderliggende parkeergarage, waar uw privéparkeerplaats zich pal voor de deur bevindt – met de mogelijkheid tot het installeren van een laadpaal voor elektrisch rijden. Deze parkeerplaats wordt optioneel aangeboden voor € 500,00 per maand.\n' +
              '\n' +
              'LOCATIE EN BEREIKBAARHEID:\n' +
              '\n' +
              'De woning is gelegen in het hart van de Zuidas, hét internationale zakendistrict van Amsterdam. De omgeving biedt een scala aan hoogwaardige voorzieningen zoals winkels, horecagelegenheden, sportclubs en culturele instellingen. Ook de Vrije Universiteit, het Amsterdamse Bos, het Gijsbrecht van Aemstelpark en het Amstelpark liggen in de directe omgeving. Binnen enkele minuten wandelen bereikt u Station Amsterdam Zuid, met uitstekende trein-, metro- en tramverbindingen naar onder andere het centrum van Amsterdam, Schiphol en de rest van Nederland. Ook met de auto is de locatie goed bereikbaar via de A10.\n' +
              '\n' +
              'GEORGE GERSHWINLAAN 7 IS EEN WONING DIE STIJL, COMFORT EN STEDELIJKE DYNAMIEK NAADLOOS SAMENBRENGT. Bent u op zoek naar een exclusieve huurwoning waar u direct in kunt trekken en kunt genieten van het allerbeste dat Amsterdam te bieden heeft? Dan is dit uw kans.\n' +
              '\n' +
              'Aangegeven maten zijn niet opgemeten conform de NEN norm.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.897Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.926Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.948Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.969Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373031958_3iagc',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: Build year must be between 1800 and 2030
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: 'Build year must be between 1800 and 2030',
          path: [ 'year' ],
          type: 'custom',
          context: { label: 'year', value: '1673', key: 'year' }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:31.969Z
  },
  dataQuality: { completeness: 88, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:31.996Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:31'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:32.018Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:32'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1757373032007_5f71x',
  source: 'funda',
  duration: '11ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:1026:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Wilheminakade 505 Rotterdam\n' +
              '\n' +
              "Laat u verleiden door dit luxueuze en smaakvol ingerichte (deels gemeubileerde) 4 kamer (hoek)appartement met eigentijdse klasse, gelegen op de 29ste verdieping van het prestigieuze appartementencomplex ''De Rotterdam'. Het ruime appartement met veel lichtinval door de grote raampartijen is tot in de puntjes afgewerkt en heeft een fenomenaal uitzicht op de Maas en de Erasmusbrug. De prachtige eiken houten visgraat parketvloeren zijn voorzien van comfortabele vloerverwarming en –koeling.\n" +
              '\n' +
              'Daarnaast beschikt het appartement over een heerlijk balkon over de gehele lengte van de woning van ruim 25 m² (15 x 1,7m) en gelegen op het Zuidwesten, waardoor u tot de zonsondergang van de zon en het uitzicht kunt genieten.\n' +
              '\n' +
              'Het appartement is gesitueerd op de Kop van Zuid, het trendy ‘Manhattan aan de Maas’, een combinatie van indrukwekkende kantoor- en woontorens met gerenoveerde, monumentale pakhuizen waar altijd iets te beleven valt. Zowel een aantal oude pakhuizen als nieuwe torens bieden nu plek aan culturele instellingen en restaurants. Met de komst van het Nederlands Fotomuseum, filmhuis en jazzpodium LantarenVenster en het Luxor Theater is de Kop van Zuid een echte culturele hotspot met internationale allure.\n' +
              '\n' +
              'Verder heeft u op steenworpafstand de Rijnhavenbrug naar Katendrecht met een diversiteit aan gezellige restaurants.\n' +
              '\n' +
              'De Wilhelminapier biedt in feite alles wat nodig is voor een ultiem comfortabel stadsleven. Moderne architectuur, historie, cultuur, vele uitgaansmogelijkheden, maar ook een goede supermarkt heeft u in de onmiddellijke nabijheid. Een beetje winkelen, naar het Luxor theater of de bioscoop, gewoon een kopje koffie drinken of uitgebreid uit eten. Alles is op een paar minuten loopafstand te vinden. Daarnaast ligt de Wilhelminapier enorm centraal voor fiets, deelvervoer, auto en OV. Met de metro bent u binnen 10 minuten op Rotterdam Centraal en met 25 treinminuten checkt u in op Schiphol. In alle garages zijn elektrische laadpalen te vinden en de verschillende deelvervoersconcepten staan altijd tot uw beschikking.\n' +
              '\n' +
              'In overleg met de verhuurder is het tevens mogelijk het appartement deels gemeubileerd of gestoffeerd te huren.\n' +
              '\n' +
              'Het gebouw\n' +
              '\n' +
              "‘De Rotterdam’, een knap staaltje architectuur naar een ontwerp van Rem Koolhaas is de letterlijke interpretatie van een 'vertical city'. In een totaaloppervlakte van honderdzestig duizend m2 worden wonen, winkelen en recreëren met elkaar verenigd door een combinatie van luxe appartementen, een viersterren hotel, kantoren, winkels, horeca, fitness en parkeren. ‘De Rotterdam’ is een gebouw dat bruist. Een dynamische locatie waar 24 uur per dag wordt geleefd.\n" +
              '\n' +
              "In ‘De Rotterdam’ heeft u de beschikking over een fulltime service manager die allerhande praktische zaken voor u regelt. Hij staat u tot dienst en ziet er op toe dat alles binnen het gebouw vloeiend verloopt. Op het gebied van veiligheid is voorzien in 24/7 camerabewaking in de lobby, de liften en de toegang tot de parkeergarage van het appartementencomplex. Via de servicemanagers persoonlijk of digitaal kunt u allerlei diensten regelen zoals een schoenmaker, stomerijservice, linnenreiniging, slotenmaker en fietsreparatie en kunt u deze ook dag en nacht regelen via de website 'services' die alleen toegankelijk is voor bewoners.  Bovendien kunt u voor een overnachting van uw gasten 1 van de 2 gastenverblijven in het gebouw op de 25ste verdieping snel en makkelijk reserveren. Natuurlijk betaalt u alleen voor de diensten die u afneemt.\n" +
              '\n' +
              'Uw auto kunt u parkeren in de onderliggende Q-park garage, waar u desgewenst een parkeerplek kunt huren. Het schoonhouden van uw appartement kunt u indien u dat wenst regelen met de huishoudelijke hulp van de verhuurder.\n' +
              '\n' +
              'Omgeving\n' +
              '\n' +
              '‘De Rotterdam’ is gesitueerd op de Wilhelminapier op de Kop van Zuid,\n' +
              '\n' +
              'De Kop van Zuid is een Walhalla voor architectuurliefhebbers. Naast ‘onze eigen’ Rem Koolhaas zetten de beroemdste star architecten ter wereld zetten hier gebouwen neer tussen de historische pakhuizen; Álvaro Siza, Renzo Piano, Francine Houben en Norman Foster.\n' +
              '\n' +
              'Door het behoud van de historische gebouwen blijft de herinnering aan de tijd dat veel Europeanen vanaf deze plek per stoomschip naar Amerika vertrokken behouden. Een voorbeeld hiervan is de Cruise Terminal, waar nu de meest majestueuze cruiseschepen aanmeren. En het beroemde Hotel New York, waar stoere scheepselementen nog altijd doen terugdenken aan al die landverhuizers.\n' +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Strakke nette entree met videofooninstallatie, meerdere liften, een trappenhuis en brievenbussen.\n' +
              '\n' +
              '29ste verdieping:\n' +
              '\n' +
              'Entree, hal met toegang tot de meterkast, separaat toilet met fonteintje, ingebouwde garderobekast, berging met wasmachine en droger aansluiting en toegang via prachtige zwarte dubbele taatsdeuren tot een woonkamer met open keuken van maar liefst 60m2. De lichte woonkamer is voorzien van grote raampartijen welke een spectaculair uitzicht geven over Rotterdam en de wijde omgeving. De moderne, witte open keuken is geheel greeploos en voorzien van diverse inbouwapparatuur zoals: oven, combi oven magnetron, inductiekookplaat met geïntegreerde afzuigkap. De keuken is voorzien van een prachtig Dekton aanrechtblad en gootsteen, Quooker, vaatwasser en  koelvries combinatie.\n' +
              '\n' +
              'De hoofd slaapkamer en tweede slaapkamer met inbouwkast bereikt u vanuit de hal. Vanuit de woonkamer komt u terecht in de derde slaapkamer. Ook de slaapkamers zijn door de transparante gevel voorzien van een mooie raampartij. Wakker worden met een spectaculair uitzicht is hierdoor zeker mogelijk. Vanuit de hal heeft u toegang tot de geheel strak betegelde badkamer. De badkamer is voorzien van een ligbad, dubbele wastafel en een inloopdouche.\n' +
              '\n' +
              'Kenmerken:\n' +
              '\n' +
              '- Transparante gevel van vloer tot plafond;\n' +
              '\n' +
              '- Zonwerend, hoog rendement isolatieglas;\n' +
              '\n' +
              '- Uniek uitzicht;\n' +
              '\n' +
              '- Vloerverwarming- en koeling;\n' +
              '\n' +
              '- Afwerking van keuken en het sanitair met topmerken; Miele keukenapparatuur, sanitair van Villeroy & Boch;\n' +
              '\n' +
              '- Video-intercom systeem;\n' +
              '\n' +
              '- 24/7 camerabewaking in lobby, liften en toegang parkeergarage;\n' +
              '\n' +
              '- Parkeerplek te huur in onderliggende Q-park garage, huur is ca. € 222,- per maand;\n' +
              '\n' +
              '- Algemene fietsenberging op de 1ste verdieping;\n' +
              '\n' +
              '- Mogelijkheid tot het huren van een gastenverblijf op de 25ste verdieping;\n' +
              '\n' +
              '- Op loopafstand van diverse restaurants;\n' +
              '\n' +
              '- Servicekosten € 200,- p. m.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '\n' +
              '- Bouwjaar 2013, geperfectioneerd in het jaar 2020 met een nieuwe keuken, nieuw stucwerk, actuele kleurstelling schilderwerk, glad gestucte plafonds en eikenhouten visgraatvloeren;\n' +
              '\n' +
              '- Woonoppervlakte ca.145m²;\n' +
              '\n' +
              '- Inhoud ca.478m³.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-09-08T23:10:32.018Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-09-09 00:10:32'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:10:32.045Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:10:32'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/hengelo-ov/appartement-appendage-17/43038663/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:10:40'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:10:43'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:10:46'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:11:03'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:11:05'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:11:05'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:11:26'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:11:28'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:11:28'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/heemstede/huis-glipper-dreef-34/89512641/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:11:38'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/den-haag/appartement-batjanstraat-4/89499514/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:11:40'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:11:45'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/hengelo-ov/appartement-appendage-43/43038550/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:11:55'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:01'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:18'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:20'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:24'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:37'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:42'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:46'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/zoetermeer/huis-vierde-stationsstraat-444/89494140/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:12:53'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:12:58'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/voorburg/appartement-nieuwe-havenstraat-248/89418838/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:12:59'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:13:07.994Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:13:07'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (43%) is below threshold (80%)',
    value: 43,
    threshold: 80,
    timestamp: '2025-09-08T23:13:08.021Z'
  },
  level: 'error',
  message: 'Transformation success rate (43%) is below threshold (80%)',
  timestamp: '2025-09-09 00:13:08'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/hengelo-ov/appartement-appendage-119/43038554/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:13:11'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:13:13'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:13:32'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:13:52'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/haarlem/appartement-pieterstraat-4-b/89510236/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:14:03'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:14:26'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:14:45'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: 'Protocol error (Target.closeTarget): No target with given id found',
  level: 'error',
  message: 'Error closing detail page',
  timestamp: '2025-09-09 00:15:05'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  url: 'https://www.funda.nl/detail/huur/leiden/appartement-troelstraplein-11/43037359/',
  maxRetries: 3,
  level: 'error',
  message: 'Max retries exceeded for verification page',
  timestamp: '2025-09-09 00:15:17'
}
