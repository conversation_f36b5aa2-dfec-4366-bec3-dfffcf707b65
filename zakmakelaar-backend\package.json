{"name": "zakmakelaar-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node --no-deprecation src/index.js", "dev": "nodemon --no-deprecation src/index.js", "setup": "node setup-env.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:scraper": "node src/test-scraper.js", "test:huurwoningen": "node src/test-huurwoningen-scraper.js", "test:all-scrapers": "node src/test-all-scrapers.js", "test:ai": "node src/test-ai-features.js", "test:unified-schema": "node run-unified-schema-tests.js", "test:performance": "node run-performance-tests.js", "create-test-user": "node create-test-user.js", "test:auto-app": "node test-auto-application.js", "test:auto-app:health": "node test-auto-application.js --health", "test:form-automation": "node test-form-automation.js", "test:form-automation:integration": "node test-form-automation-integration.js", "test:puppeteer": "node test-puppeteer-basic.js", "docker:build": "docker build -f Dockerfile.puppeteer-test -t <PERSON><PERSON><PERSON>kel<PERSON>-puppeteer-test .", "docker:test": "docker-compose -f docker-compose.test.yml up --build puppeteer-test", "docker:test:clean": "docker-compose -f docker-compose.test.yml down -v", "test:real-form-automation": "node test-real-form-automation.js", "test:screenshots": "node test-screenshot-functionality.js", "test:notifications": "node test-notification-system.js", "monitor:auto-app": "node monitor-auto-application.js", "monitor:auto-app:status": "node start-auto-app-monitor.js", "monitor:auto-app:once": "node start-auto-app-monitor.js --once", "monitor:auto-app:detailed": "node monitor-auto-application-status.js", "monitor:auto-app:live": "node auto-app-live-logger.js", "validate:config": "node validate-config.js", "benchmark": "node src/benchmark-scraper.js", "monitor": "node src/start-monitoring.js", "debug:price": "node src/debug-price-extraction.js", "test:process-existing": "node test-process-existing-listings.js", "debug:auto-app": "node debug-auto-application.js", "monitor:queue": "node test-automatic-processing.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.21.0", "@sendgrid/mail": "^8.1.5", "bcrypt": "^6.0.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cache": "^5.1.2", "node-cron": "^4.2.1", "node-schedule": "^2.1.1", "puppeteer": "^24.11.1", "redis": "^5.5.6", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.8.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"chromium": "^3.0.3", "jest": "^30.0.4", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.10", "supertest": "^7.1.3"}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "!src/test-*.js", "!src/debug-*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}