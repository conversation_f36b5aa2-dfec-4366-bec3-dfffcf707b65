const puppeteer = require("puppeteer");
const { loggers } = require("./logger");
const {
  classifyError,
  handleErrorWithRecovery,
  createHealth<PERSON><PERSON><PERSON>,
  NetworkError,
  TimeoutError,
  BrowserError,
  ParsingError,
} = require("../utils/scraperErrors");

// Try to use chromium if available
let chromiumPath = undefined;
try {
  const chromium = require("chromium");
  chromiumPath = chromium.path;
  console.log("Using Chromium for scraping");
} catch (error) {
  console.log("Chromium not available, using default Chrome");
}

// Browser pool for better resource management
class BrowserPool {
  constructor(maxBrowsers = 2) {
    this.browsers = [];
    this.maxBrowsers = maxBrowsers;
    this.currentIndex = 0;
  }

  async getBrowser() {
    if (this.browsers.length < this.maxBrowsers) {
      const browser = await puppeteer.launch({
        headless: "new",
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-infobars",
          "--window-position=0,0",
          "--ignore-certificate-errors",
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding",
          "--disable-dev-shm-usage",
          "--disable-extensions",
          "--disable-web-security",
          "--disable-features=VizDisplayCompositor",
          "--no-first-run",
          "--no-default-browser-check",
          "--disable-default-apps",
          "--disable-popup-blocking",
          "--disable-translate",
          "--disable-background-networking",
          "--disable-sync",
          "--metrics-recording-only",
          "--no-report-upload",
          "--disable-gpu-sandbox",
          "--disable-software-rasterizer",
        ],
        ignoreDefaultArgs: ["--disable-extensions"],
        executablePath: process.env.CHROME_EXECUTABLE_PATH || chromiumPath,
        timeout: 30000,
      });
      this.browsers.push(browser);
      return browser;
    }

    // Round-robin selection
    const browser = this.browsers[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.browsers.length;
    return browser;
  }

  async closeAll() {
    await Promise.all(this.browsers.map((browser) => browser.close()));
    this.browsers = [];
  }

  async getVisibleBrowser() {
    // Launch a visible browser with the same configuration as getBrowser()
    // but with headless: false for testing and demonstration
    const browser = await puppeteer.launch({
      headless: false, // Force visible mode
      slowMo: 0, // Remove slow motion to allow faster typing
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-infobars",
        "--window-position=100,100", // Position window for visibility
        "--window-size=1200,800", // Set reasonable size
        "--ignore-certificate-errors",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-dev-shm-usage",
        "--disable-extensions",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-default-apps",
        "--disable-popup-blocking",
        "--disable-translate",
        "--disable-background-networking",
        "--disable-sync",
        "--metrics-recording-only",
        "--no-report-upload",
        "--disable-gpu-sandbox",
        "--disable-software-rasterizer",
      ],
      ignoreDefaultArgs: ["--disable-extensions"],
      executablePath: process.env.CHROME_EXECUTABLE_PATH || chromiumPath,
      timeout: 30000,
    });
    return browser;
  }
}

const browserPool = new BrowserPool();

// Data validation and normalization utilities
// Import the enhanced version that uses the unified schema transformer
const {
  validateAndNormalizeListing: enhancedValidator,
} = require("./transformationIntegration");

// Original implementation kept for fallback
const validateAndNormalizeListingOriginal = (listingData) => {
  if (!listingData.title || !listingData.url || !listingData.location) {
    return null; // Invalid listing
  }

  // Normalize title
  let normalizedTitle = listingData.title.trim();
  // Remove extra whitespace and normalize
  normalizedTitle = normalizedTitle.replace(/\s+/g, " ");
  // Ensure proper capitalization
  if (normalizedTitle.length > 0) {
    normalizedTitle =
      normalizedTitle.charAt(0).toUpperCase() + normalizedTitle.slice(1);
  }

  // Normalize price
  let normalizedPrice = listingData.price;
  if (normalizedPrice && typeof normalizedPrice === "string") {
    // Clean up HTML entities, non-breaking spaces, and extra content
    normalizedPrice = normalizedPrice
      .replace(/&nbsp;/g, " ")
      .replace(/\u00A0/g, " ") // Non-breaking space character (160)
      .replace(/\s+/g, " ")
      .trim();

    // Extract only the price part (before any newlines or extra content)
    const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
    if (priceLineMatch) {
      normalizedPrice = priceLineMatch[1].trim();
    }

    // Handle common Dutch price formats
    if (
      normalizedPrice.toLowerCase().includes("op aanvraag") ||
      normalizedPrice.toLowerCase().includes("on request")
    ) {
      normalizedPrice = "Prijs op aanvraag";
    } else {
      // Extract numeric value and format consistently
      const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/); // Modified to capture numbers with thousands separators
      if (priceMatch) {
        // Handle European number formats where comma can be thousands separator
        let extractedPrice = priceMatch[1].trim();

        let numericPrice;

        // Special case for the format in the screenshot: €3,950 (treat as 3950, not 3.95)
        if (
          extractedPrice.match(/^\d{1,3},\d{3}$/) &&
          !normalizedPrice.includes(".")
        ) {
          // This is the format from the screenshot - comma is a thousands separator
          numericPrice = parseFloat(extractedPrice.replace(/,/g, ""));
        }
        // Case 1: Format with multiple thousands separators and decimal comma (e.g., 1.234.567,89)
        else if (extractedPrice.match(/\d{1,3}(\.\d{3})+,\d+$/)) {
          numericPrice = parseFloat(
            extractedPrice.replace(/\./g, "").replace(",", ".")
          );
        }
        // Case 2: Format with single thousands separator and decimal comma (e.g., 3.950,00)
        else if (extractedPrice.match(/\d{1,3}\.\d{3},\d+$/)) {
          numericPrice = parseFloat(
            extractedPrice.replace(/\./g, "").replace(",", ".")
          );
        }
        // Case 3: Format with comma as thousands separator (e.g., 3,950)
        else if (extractedPrice.match(/\d{1,3},\d{3}$/)) {
          numericPrice = parseFloat(extractedPrice.replace(/,/g, ""));
        }
        // Case 4: Format with period as thousands separator (e.g., 3.950)
        else if (extractedPrice.match(/\d{1,3}\.\d{3}$/)) {
          numericPrice = parseFloat(extractedPrice.replace(/\./g, ""));
        }
        // Case 5: Regular decimal format or other formats
        else {
          numericPrice = parseFloat(extractedPrice.replace(",", "."));
        }
        if (numericPrice > 0) {
          normalizedPrice = `€ ${Math.round(numericPrice).toLocaleString(
            "nl-NL"
          )}`;
          // Add per month if it seems to be a rental price
          if (numericPrice < 10000) {
            normalizedPrice += " per maand";
          }
        }
      }
    }
  } else {
    normalizedPrice = "Prijs op aanvraag";
  }

  // Normalize location
  let normalizedLocation = listingData.location.replace(/\s+/g, " ").trim();

  // Remove common prefixes and clean up
  normalizedLocation = normalizedLocation
    .replace(/^(huis|appartement|kamer|woning)\s+/i, "")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase()); // Title case

  // Validate URL
  const isValidUrl =
    listingData.url.startsWith("http") &&
    (listingData.url.includes("funda.nl") ||
      listingData.url.includes("pararius.nl") ||
      listingData.url.includes("huurwoningen.nl"));

  if (!isValidUrl) {
    return null;
  }

  // Validate property type
  const validPropertyTypes = [
    "huis",
    "appartement",
    "kamer",
    "parkeergelegenheid",
    "woning",
    "studio",
  ];
  let normalizedPropertyType = listingData.propertyType || "woning";
  if (!validPropertyTypes.includes(normalizedPropertyType)) {
    normalizedPropertyType = "woning";
  }

  return {
    ...listingData,
    title: normalizedTitle,
    price: normalizedPrice,
    location: normalizedLocation,
    propertyType: normalizedPropertyType,
    dateAdded: new Date(),
  };
};

// Use the original implementation for now
// In a future update, we can switch to using the enhanced validator
const validateAndNormalizeListing = validateAndNormalizeListingOriginal;

// Anti-detection utilities
const getRandomUserAgent = () => {
  const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
  ];
  return userAgents[Math.floor(Math.random() * userAgents.length)];
};

const getRandomDelay = (min = 2000, max = 8000) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const setupPageStealth = async (page) => {
  // Set random user agent
  await page.setUserAgent(getRandomUserAgent());

  // Set realistic viewport
  const viewports = [
    { width: 1920, height: 1080 },
    { width: 1366, height: 768 },
    { width: 1440, height: 900 },
    { width: 1536, height: 864 },
  ];
  const viewport = viewports[Math.floor(Math.random() * viewports.length)];
  await page.setViewport({ ...viewport, deviceScaleFactor: 1 });

  // Override webdriver detection
  await page.evaluateOnNewDocument(() => {
    Object.defineProperty(navigator, "webdriver", {
      get: () => undefined,
    });
  });

  // Add realistic headers
  await page.setExtraHTTPHeaders({
    "Accept-Language": "en-US,en;q=0.9,nl;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    Accept:
      "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    Connection: "keep-alive",
    "Upgrade-Insecure-Requests": "1",
  });
};

// Metrics and monitoring
class ScrapingMetrics {
  constructor() {
    this.metrics = {
      totalScrapes: 0,
      successfulScrapes: 0,
      failedScrapes: 0,
      totalListingsFound: 0,
      totalListingsSaved: 0,
      duplicatesSkipped: 0,
      averageScrapingTime: 0,
      lastScrapeTime: null,
      errorsByType: {},
      // Per-site metrics
      siteMetrics: {
        funda: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
        pararius: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
        huurwoningen: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
      },
    };
  }

  recordScrapeStart() {
    this.startTime = Date.now();
    this.metrics.totalScrapes++;
  }

  recordScrapeSuccess(
    listingsFound,
    listingsSaved,
    duplicatesSkipped,
    site = null
  ) {
    const duration = Date.now() - this.startTime;
    this.metrics.successfulScrapes++;
    this.metrics.totalListingsFound += listingsFound;
    this.metrics.totalListingsSaved += listingsSaved;
    this.metrics.duplicatesSkipped += duplicatesSkipped;
    this.metrics.lastScrapeTime = new Date();

    // Update per-site metrics if site is specified
    if (site && this.metrics.siteMetrics[site]) {
      this.metrics.siteMetrics[site].listingsFound += listingsFound;
      this.metrics.siteMetrics[site].listingsSaved += listingsSaved;
      this.metrics.siteMetrics[site].duplicatesSkipped += duplicatesSkipped;
      this.metrics.siteMetrics[site].successfulScrapes++;
      this.metrics.siteMetrics[site].lastScrapeTime = new Date();
    }

    // Update average scraping time
    this.metrics.averageScrapingTime =
      (this.metrics.averageScrapingTime * (this.metrics.successfulScrapes - 1) +
        duration) /
      this.metrics.successfulScrapes;
  }

  recordScrapeFailure(error, site = null) {
    this.metrics.failedScrapes++;

    // Update per-site metrics if site is specified
    if (site && this.metrics.siteMetrics[site]) {
      this.metrics.siteMetrics[site].failedScrapes++;
    }

    // Classify and log the error
    const classifiedError = classifyError(error);

    const errorType = classifiedError.code || "UNKNOWN_ERROR";
    this.metrics.errorsByType[errorType] =
      (this.metrics.errorsByType[errorType] || 0) + 1;

    // Log the error with context
    loggers.scraper.error("Scrape failure", {
      error: classifiedError,
      context: "scrape_failure",
      site: site,
      totalScrapes: this.metrics.totalScrapes,
      failedScrapes: this.metrics.failedScrapes,
    });
  }

  getMetrics() {
    return {
      ...this.metrics,
      successRate:
        this.metrics.totalScrapes > 0
          ? (
              (this.metrics.successfulScrapes / this.metrics.totalScrapes) *
              100
            ).toFixed(2) + "%"
          : "0%",
      averageScrapingTimeFormatted: `${(
        this.metrics.averageScrapingTime / 1000
      ).toFixed(2)}s`,
    };
  }

  // Method to reset metrics to zero
  resetMetrics() {
    this.metrics = {
      totalScrapes: 0,
      successfulScrapes: 0,
      failedScrapes: 0,
      totalListingsFound: 0,
      totalListingsSaved: 0,
      duplicatesSkipped: 0,
      averageScrapingTime: 0,
      lastScrapeTime: null,
      errorsByType: {},
      siteMetrics: {
        funda: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
        pararius: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
        huurwoningen: {
          listingsFound: 0,
          listingsSaved: 0,
          duplicatesSkipped: 0,
          successfulScrapes: 0,
          failedScrapes: 0,
          lastScrapeTime: null,
        },
      },
    };
    console.log("Metrics reset to zero");
  }

  // Test method to populate sample data for dashboard verification
  populateTestData() {
    this.metrics.totalScrapes = 5;
    this.metrics.successfulScrapes = 4;
    this.metrics.failedScrapes = 1;
    this.metrics.totalListingsFound = 150;
    this.metrics.totalListingsSaved = 145;
    this.metrics.duplicatesSkipped = 5;
    this.metrics.averageScrapingTime = 45000; // 45 seconds
    this.metrics.lastScrapeTime = new Date();

    // Populate site-specific data
    this.metrics.siteMetrics.funda.listingsFound = 75;
    this.metrics.siteMetrics.funda.listingsSaved = 73;
    this.metrics.siteMetrics.funda.duplicatesSkipped = 2;
    this.metrics.siteMetrics.funda.successfulScrapes = 2;
    this.metrics.siteMetrics.funda.failedScrapes = 0;
    this.metrics.siteMetrics.funda.lastScrapeTime = new Date();

    this.metrics.siteMetrics.pararius.listingsFound = 45;
    this.metrics.siteMetrics.pararius.listingsSaved = 43;
    this.metrics.siteMetrics.pararius.duplicatesSkipped = 2;
    this.metrics.siteMetrics.pararius.successfulScrapes = 1;
    this.metrics.siteMetrics.pararius.failedScrapes = 1;
    this.metrics.siteMetrics.pararius.lastScrapeTime = new Date();

    this.metrics.siteMetrics.huurwoningen.listingsFound = 30;
    this.metrics.siteMetrics.huurwoningen.listingsSaved = 29;
    this.metrics.siteMetrics.huurwoningen.duplicatesSkipped = 1;
    this.metrics.siteMetrics.huurwoningen.successfulScrapes = 1;
    this.metrics.siteMetrics.huurwoningen.failedScrapes = 0;
    this.metrics.siteMetrics.huurwoningen.lastScrapeTime = new Date();

    console.log("Test data populated for dashboard verification");
  }
}

const scrapingMetrics = new ScrapingMetrics();

// Helper function to determine if an error is retryable
const isRetryableError = (error) => {
  const retryableErrors = [
    "TimeoutError",
    "NetworkError",
    "ECONNRESET",
    "ENOTFOUND",
    "ECONNREFUSED",
    "ERR_NETWORK_CHANGED",
  ];

  return retryableErrors.some(
    (errorType) => error.message.includes(errorType) || error.name === errorType
  );
};

// Helper function to scroll the page to load all content
async function autoScroll(page) {
  await page.evaluate(async () => {
    await new Promise((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;

        // Add some random delay to make it look more human-like
        const randomDelay = Math.floor(Math.random() * 40) + 10;
        setTimeout(() => {}, randomDelay);

        if (totalHeight >= scrollHeight - window.innerHeight) {
          clearInterval(timer);
          resolve();
        }
      }, 100);
    });
  });
}

// Cleanup function for graceful shutdown
const cleanup = async () => {
  console.log("Cleaning up scraper resources...");
  await browserPool.closeAll();
  console.log("Scraper cleanup completed");
};

// Get scraping metrics
const getScrapingMetrics = () => {
  return scrapingMetrics.getMetrics();
};

module.exports = {
  browserPool,
  validateAndNormalizeListing,
  getRandomUserAgent,
  getRandomDelay,
  setupPageStealth,
  scrapingMetrics,
  isRetryableError,
  autoScroll,
  cleanup,
  getScrapingMetrics,
};
