/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/application`; params?: Router.UnknownInputParams; } | { pathname: `/auto-application-dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/auto-application-settings`; params?: Router.UnknownInputParams; } | { pathname: `/autonomous-settings`; params?: Router.UnknownInputParams; } | { pathname: `/autonomous-status`; params?: Router.UnknownInputParams; } | { pathname: `/change-password`; params?: Router.UnknownInputParams; } | { pathname: `/contract-review`; params?: Router.UnknownInputParams; } | { pathname: `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/listing-details`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/notification-history`; params?: Router.UnknownInputParams; } | { pathname: `/notification-settings`; params?: Router.UnknownInputParams; } | { pathname: `/preferences`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/safety-controls`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/add-property`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/edit-property`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/profile`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/property-details`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/tenant-screening`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/verification`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/application`; params?: Router.UnknownOutputParams; } | { pathname: `/auto-application-dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/auto-application-settings`; params?: Router.UnknownOutputParams; } | { pathname: `/autonomous-settings`; params?: Router.UnknownOutputParams; } | { pathname: `/autonomous-status`; params?: Router.UnknownOutputParams; } | { pathname: `/change-password`; params?: Router.UnknownOutputParams; } | { pathname: `/contract-review`; params?: Router.UnknownOutputParams; } | { pathname: `/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/listing-details`; params?: Router.UnknownOutputParams; } | { pathname: `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/notification-history`; params?: Router.UnknownOutputParams; } | { pathname: `/notification-settings`; params?: Router.UnknownOutputParams; } | { pathname: `/preferences`; params?: Router.UnknownOutputParams; } | { pathname: `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/safety-controls`; params?: Router.UnknownOutputParams; } | { pathname: `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/property-owner/add-property`; params?: Router.UnknownOutputParams; } | { pathname: `/property-owner/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/property-owner/edit-property`; params?: Router.UnknownOutputParams; } | { pathname: `/property-owner/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/property-owner/property-details`; params?: Router.UnknownOutputParams; } | { pathname: `/property-owner/tenant-screening`; params?: Router.UnknownOutputParams; } | { pathname: `/property-owner/verification`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/application${`?${string}` | `#${string}` | ''}` | `/auto-application-dashboard${`?${string}` | `#${string}` | ''}` | `/auto-application-settings${`?${string}` | `#${string}` | ''}` | `/autonomous-settings${`?${string}` | `#${string}` | ''}` | `/autonomous-status${`?${string}` | `#${string}` | ''}` | `/change-password${`?${string}` | `#${string}` | ''}` | `/contract-review${`?${string}` | `#${string}` | ''}` | `/dashboard${`?${string}` | `#${string}` | ''}` | `/edit-profile${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/listing-details${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/notification-history${`?${string}` | `#${string}` | ''}` | `/notification-settings${`?${string}` | `#${string}` | ''}` | `/preferences${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/safety-controls${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/property-owner/add-property${`?${string}` | `#${string}` | ''}` | `/property-owner/dashboard${`?${string}` | `#${string}` | ''}` | `/property-owner/edit-property${`?${string}` | `#${string}` | ''}` | `/property-owner/profile${`?${string}` | `#${string}` | ''}` | `/property-owner/property-details${`?${string}` | `#${string}` | ''}` | `/property-owner/tenant-screening${`?${string}` | `#${string}` | ''}` | `/property-owner/verification${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/application`; params?: Router.UnknownInputParams; } | { pathname: `/auto-application-dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/auto-application-settings`; params?: Router.UnknownInputParams; } | { pathname: `/autonomous-settings`; params?: Router.UnknownInputParams; } | { pathname: `/autonomous-status`; params?: Router.UnknownInputParams; } | { pathname: `/change-password`; params?: Router.UnknownInputParams; } | { pathname: `/contract-review`; params?: Router.UnknownInputParams; } | { pathname: `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/listing-details`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/notification-history`; params?: Router.UnknownInputParams; } | { pathname: `/notification-settings`; params?: Router.UnknownInputParams; } | { pathname: `/preferences`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/safety-controls`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/add-property`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/edit-property`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/profile`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/property-details`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/tenant-screening`; params?: Router.UnknownInputParams; } | { pathname: `/property-owner/verification`; params?: Router.UnknownInputParams; } | `/+not-found${`?${string}` | `#${string}` | ''}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
