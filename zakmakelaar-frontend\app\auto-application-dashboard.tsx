import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { AutonomousStatusDashboard } from '../components/autonomous/AutonomousStatusDashboard';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import { useAuthStore } from '../store/authStore';
import {
  autoApplicationService,
  AutoApplicationStats,
  ApplicationQueue,
  ApplicationResult,
  ScraperIntegrationStats,
} from '../services/autoApplicationService';

const { width } = Dimensions.get('window');

// Theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Header Component
const Header = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <View style={styles.backButtonInner}>
            <Ionicons name="chevron-back" size={24} color={THEME.primary} />
          </View>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Auto-Application Dashboard</Text>
        </View>

        <TouchableOpacity
          onPress={() => router.push('/auto-application-settings')}
          style={styles.settingsButton}
          activeOpacity={0.8}
        >
          <Ionicons name="settings-outline" size={24} color="#ffffff" />
        </TouchableOpacity>
      </Animated.View>
    </LinearGradient>
  );
};

// Stats Card Component
const StatsCard = ({
  title,
  value,
  subtitle,
  icon,
  color,
  trend,
}: {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color: string;
  trend?: { value: number; isPositive: boolean };
}) => (
  <Animated.View
    style={[styles.statsCard, { borderLeftColor: color }]}
    entering={FadeInUp.duration(600)}
  >
    <View style={styles.statsCardHeader}>
      <View style={[styles.statsIcon, { backgroundColor: color }]}>
        <Ionicons name={icon as any} size={20} color="#ffffff" />
      </View>
      <View style={styles.statsContent}>
        <Text style={styles.statsValue}>{value}</Text>
        <Text style={styles.statsTitle}>{title}</Text>
        {subtitle && <Text style={styles.statsSubtitle}>{subtitle}</Text>}
      </View>
      {trend && (
        <View style={styles.trendContainer}>
          <Ionicons
            name={trend.isPositive ? "trending-up" : "trending-down"}
            size={16}
            color={trend.isPositive ? THEME.success : THEME.danger}
          />
          <Text style={[
            styles.trendText,
            { color: trend.isPositive ? THEME.success : THEME.danger }
          ]}>
            {Math.abs(trend.value)}%
          </Text>
        </View>
      )}
    </View>
  </Animated.View>
);

// Queue Item Component
const QueueItem = ({
  item,
  onRemove,
  onUpdatePriority,
}: {
  item: ApplicationQueue;
  onRemove: (id: string) => void;
  onUpdatePriority: (id: string, priority: number) => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return THEME.warning;
      case 'processing': return THEME.primary;
      case 'completed': return THEME.success;
      case 'failed': return THEME.danger;
      case 'cancelled': return THEME.gray;
      default: return THEME.gray;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'processing': return 'sync-outline';
      case 'completed': return 'checkmark-circle-outline';
      case 'failed': return 'close-circle-outline';
      case 'cancelled': return 'ban-outline';
      default: return 'help-circle-outline';
    }
  };

  // Display attempts more intuitively: if item is not pending and attempts is 0/undefined, show 1
  const attemptsUsed = (() => {
    const raw = Number(item.attempts ?? 0);
    if (['processing', 'completed', 'failed', 'cancelled'].includes(item.status) && raw === 0) {
      return 1;
    }
    return isNaN(raw) ? 0 : raw;
  })();
  const attemptsMax = Number(item.maxAttempts ?? 3);

  return (
    <Animated.View
      style={styles.queueItem}
      entering={FadeInDown.duration(400)}
    >
      <View style={styles.queueItemHeader}>
        <View style={styles.queueItemTitle}>
          <Text style={styles.queueItemName} numberOfLines={1}>
            {item.listingTitle}
          </Text>
          <View style={styles.queueItemMeta}>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Ionicons name={getStatusIcon(item.status) as any} size={12} color="#ffffff" />
              <Text style={styles.statusText}>{item.status}</Text>
            </View>
            <Text style={styles.priorityText}>Priority: {item.priority}</Text>
          </View>
        </View>

        <View style={styles.queueItemActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onUpdatePriority(item._id, Math.min(10, item.priority + 1))}
          >
            <Ionicons name="arrow-up" size={16} color={THEME.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onUpdatePriority(item._id, Math.max(1, item.priority - 1))}
          >
            <Ionicons name="arrow-down" size={16} color={THEME.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.removeButton]}
            onPress={() => onRemove(item._id)}
          >
            <Ionicons name="trash-outline" size={16} color={THEME.danger} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.queueItemDetails}>
        <Text style={styles.queueItemDetail}>
          Scheduled: {new Date(item.scheduledFor).toLocaleString()}
        </Text>
        <Text style={styles.queueItemDetail}>
          Attempts: {attemptsUsed}/{attemptsMax}
        </Text>

      </View>

      {item.errorMessage && (
        <View style={styles.errorContainer}>
          <Ionicons name="warning" size={14} color={THEME.danger} />
          <Text style={styles.errorText}>{item.errorMessage}</Text>
        </View>
      )}
    </Animated.View>
  );
};

// Helper: derive a human-readable title from various possible fields
const deriveListingTitle = (raw: any): string => {
  try {
    // 1) Preferred explicit fields
    const direct = raw?.listingTitle || raw?.title || raw?.name;
    if (direct && typeof direct === 'string') return direct;

    // 2) Common snapshot shapes
    const snap = raw?.listingSnapshot || raw?.snapshot || raw?.listing;
    const snapTitle = snap?.title || snap?.heading || snap?.address || snap?.name;
    if (snapTitle && typeof snapTitle === 'string') return snapTitle;

    // 3) Derive from URL
    const url: string | undefined = snap?.url || raw?.listingUrl;
    if (url && typeof url === 'string') {
      try {
        const u = new URL(url);
        const segments = u.pathname.split('/').filter(Boolean);
        // Look for the last non-numeric, non-id-like segment
        let candidate = segments.reverse().find((seg) => !/^\d+$/.test(seg));
        if (!candidate && segments.length > 0) candidate = segments[0];
        if (candidate) {
          const decoded = decodeURIComponent(candidate)
            .replace(/-/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
          // Title case
          const titled = decoded
            .split(' ')
            .map((w) => (w ? w.charAt(0).toUpperCase() + w.slice(1) : w))
            .join(' ');
          // Remove common prefixes that aren’t helpful
          return titled.replace(/^Appartement\s+|^Huis\s+/i, '').trim() || 'Unknown Property';
        }
      } catch {}
    }

    // 4) Fallback
    return 'Unknown Property';
  } catch {
    return 'Unknown Property';
  }
};

// Utility function to safely process application result data
const processApplicationResult = (result: any): ApplicationResult | null => {
  try {
    // Validate required fields
    if (!result || !result._id) {
      console.warn('Missing required fields in result:', result);
      return null;
    }

    // Ensure all required fields have fallback values
      return {
      ...result,
      _id: result._id,
      listingTitle: deriveListingTitle(result),
      status: result.status || 'unknown',
      submittedAt: result.submittedAt || result.createdAt || new Date(),
      userId: result.userId || '',
      queueId: result.queueId || '',
      listingId: result.listingId || '',
      listingUrl: result.listingUrl || '',
      responseTime: result.responseTime || 0,
      formData: result.formData || {},
      screenshots: result.screenshots || [],
      metadata: result.metadata || {},
      createdAt: result.createdAt || result.submittedAt || new Date(),
      // Handle nested objects safely
      metrics: result.metrics ? {
        successProbability: result.metrics.successProbability || 0,
        processingTime: result.metrics.processingTime || 0,
        ...result.metrics
      } : undefined,
      response: result.response ? {
        responseTime: result.response.responseTime || result.responseTime || 0,
        ...result.response
      } : undefined,
      errorDetails: result.errorDetails || undefined,
      landlordResponse: result.landlordResponse || undefined
    };
  } catch (error) {
    console.error('Error processing application result:', error, result);
    return null;
  }
};

// Score helpers
const normalizeScore = (val: any): number | null => {
  if (val == null) return null;
  const n = Number(val);
  if (Number.isNaN(n)) return null;
  if (n <= 1) return Math.round(n * 100);
  return Math.round(Math.max(0, Math.min(100, n)));
};

const getResultScoreText = (result: any): string => {
  const s1 = normalizeScore(result?.successScore);
  const s2 = normalizeScore(result?.metrics?.successProbability);
  const s3 = normalizeScore(result?.aiContent?.confidence);
  let chosen: number | null = null;
  if (s1 != null) chosen = s1;
  else if (s2 != null) chosen = (s2 === 50 && s3 != null) ? s3 : s2; // Prefer AI confidence over placeholder 50
  else if (s3 != null) chosen = s3;
  return chosen != null ? `${chosen}%` : 'N/A';
};

// Recent Result Component
const RecentResult = ({ result }: { result: ApplicationResult }) => {
  // Debug log the result data
  console.log('🔍 DEBUG: Rendering result:', {
    id: result._id,
    title: result.listingTitle,
    status: result.status,
    submittedAt: result.submittedAt,
    hasMetrics: !!result.metrics,
    hasResponse: !!result.response,
    hasLandlordResponse: !!result.landlordResponse
  });
  
  const statusColor = result.status === 'success' ? THEME.success :
    result.status === 'failed' ? THEME.danger : THEME.gray;
    
  // Safely get formatted data
  let timeAgo = 'Unknown';
  try {
    const formatted = autoApplicationService.formatApplicationResult(result);
    timeAgo = formatted.timeAgo;
  } catch (error) {
    console.warn('Error formatting result:', error);
    // Fallback time calculation
    if (result.submittedAt) {
      const now = new Date();
      const submitted = new Date(result.submittedAt);
      const diffMs = now.getTime() - submitted.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);
      
      if (diffMins < 1) timeAgo = "Just now";
      else if (diffMins < 60) timeAgo = `${diffMins}m ago`;
      else if (diffHours < 24) timeAgo = `${diffHours}h ago`;
      else timeAgo = `${diffDays}d ago`;
    }
  }

  return (
    <Animated.View
      style={styles.resultItem}
      entering={FadeInDown.duration(400)}
    >
      <View style={styles.resultHeader}>
        <View style={[styles.resultStatus, { backgroundColor: statusColor }]} />
        <View style={styles.resultContent}>
          <Text style={styles.resultTitle} numberOfLines={1}>
            {result.listingTitle || deriveListingTitle(result)}
          </Text>
          <Text style={styles.resultTime}>
            {timeAgo}
          </Text>
          <Text style={styles.resultStatusText}>
            Status: {result.status || 'unknown'}
          </Text>
        </View>
        <View style={styles.resultMeta}>
          <Text style={styles.resultScore}>
            {getResultScoreText(result)}
          </Text>
          <Text style={styles.resultResponseTime}>
            {typeof (result.response?.responseTime ?? result.responseTime) === 'number'
              ? `${result.response?.responseTime ?? result.responseTime}ms`
              : 'N/A'}
          </Text>
        </View>
      </View>

      {/* Show more detailed status information */}
      <View style={styles.resultDetails}>
        {result.submittedAt && (
          <Text style={styles.resultDetail}>
            Submitted: {new Date(result.submittedAt).toLocaleString()}
          </Text>
        )}
        {result.errorDetails && (
          <View style={styles.errorContainer}>
            <Ionicons name="warning" size={14} color={THEME.danger} />
            <Text style={styles.errorText}>
              {result.errorDetails.message || 'Unknown error'}
            </Text>
          </View>
        )}
      </View>

      {result.landlordResponse && (
        <View style={styles.landlordResponse}>
          <Text style={styles.landlordResponseText}>
            Landlord: {result.landlordResponse.status}
          </Text>
          {result.landlordResponse.message && (
            <Text style={styles.landlordResponseMessage}>
              "{result.landlordResponse.message}"
            </Text>
          )}
          {result.landlordResponse.receivedAt && (
            <Text style={styles.resultDetail}>
              Received: {new Date(result.landlordResponse.receivedAt).toLocaleString()}
            </Text>
          )}
        </View>
      )}
    </Animated.View>
  );
};

export default function AutoApplicationDashboardScreen() {
  const router = useRouter();
  const { user } = useAuthStore();

  // State
  const [stats, setStats] = useState<AutoApplicationStats | null>(null);
  const [queue, setQueue] = useState<ApplicationQueue[]>([]);
  const [recentResults, setRecentResults] = useState<ApplicationResult[]>([]);
  const [scraperStats, setScraperStats] = useState<ScraperIntegrationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { tab } = useLocalSearchParams<{ tab?: string }>();
  const initialTab = (tab === 'autonomous' ? 'autonomous' : 'overview') as 'overview' | 'queue' | 'results' | 'autonomous';
  const [activeTab, setActiveTab] = useState<'overview' | 'queue' | 'results' | 'autonomous'>(initialTab);
  const [pausingQueue, setPausingQueue] = useState(false);
  const [resumingQueue, setResumingQueue] = useState(false);

  // Load data
  const loadData = useCallback(async (isRefresh = false) => {
    if (!user?.id) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const [statsResponse, queueResponse, resultsResponse, scraperResponse] = await Promise.allSettled([
        autoApplicationService.getStats(user.id),
        autoApplicationService.getQueue(user.id),
        autoApplicationService.getResults(user.id, 1, 10),
        autoApplicationService.getScraperIntegrationStats(),
      ]);

      if (statsResponse.status === 'fulfilled' && statsResponse.value.success) {
        setStats(statsResponse.value.data || null);
      }

      if (queueResponse.status === 'fulfilled' && queueResponse.value.success) {
        setQueue(queueResponse.value.data || []);
      }

      if (resultsResponse.status === 'fulfilled' && resultsResponse.value.success) {
        console.log('🔍 DEBUG: Results API response:', {
          success: resultsResponse.value.success,
          data: resultsResponse.value.data,
          rawResults: resultsResponse.value.data?.results,
          resultsCount: resultsResponse.value.data?.results?.length || 0
        });
        
        const rawResults = resultsResponse.value.data?.results || [];
        console.log('🔍 DEBUG: Raw results from API:', rawResults);
        
        // Process and validate each result
        const processedResults = rawResults
          .map((rawResult: any, index: number) => {
            console.log(`🔍 DEBUG: Processing result ${index}:`, rawResult);
            const processed = processApplicationResult(rawResult);
            if (!processed) {
              console.warn(`Failed to process result ${index}:`, rawResult);
            }
            return processed;
          })
          .filter((result): result is ApplicationResult => result !== null);
        
        console.log('🔍 DEBUG: Processed results:', {
          originalCount: rawResults.length,
          processedCount: processedResults.length,
          firstProcessed: processedResults[0] || null
        });
        
        setRecentResults(processedResults);
      } else {
        console.log('🔍 DEBUG: Results API failed:', {
          status: resultsResponse.status,
          error: resultsResponse.status === 'rejected' ? resultsResponse.reason : 'fulfilled but not success',
          response: resultsResponse.status === 'fulfilled' ? resultsResponse.value : undefined
        });
      }

      if (scraperResponse.status === 'fulfilled' && scraperResponse.value.success) {
        setScraperStats(scraperResponse.value.data || null);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Queue management
  const handleRemoveFromQueue = async (queueId: string) => {
    try {
      const response = await autoApplicationService.removeFromQueue(queueId);
      if (response.success) {
        setQueue(queue.filter(item => item._id !== queueId));
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert('Success', 'Item removed from queue');
      } else {
        // Handle API response errors
        const errorMessage = response.error || response.message || 'Failed to remove item from queue';
        console.error('API Error removing from queue:', response);
        Alert.alert('Error', errorMessage);
      }
    } catch (error: any) {
      // Handle network/server errors
      console.error('Network Error removing from queue:', {
        message: error.message,
        status: error.status,
        code: error.code,
        response: error.response?.data
      });
      
      let errorMessage = 'Failed to remove item from queue';
      
      if (error.status === 500) {
        // Check if this is the specific "not a function" error
        if (error.message && error.message.includes('is not a function')) {
          console.error('Backend method not implemented:', error.message);
          errorMessage = 'Queue removal feature is not yet implemented on the server. Please contact support.';
          
          // Don't remove from local state for this error type
          Alert.alert('Feature Not Available', 
            'Queue removal is temporarily unavailable. The feature is being implemented on the server.',
            [{ text: 'OK', style: 'default' }]
          );
          return; // Exit early, don't show generic error
        }
        
        // For other 500 errors, remove from local state as workaround
        console.warn('Server error - removing item locally as workaround');
        setQueue(queue.filter(item => item._id !== queueId));
        errorMessage = 'Server error occurred. Item removed locally. Please refresh to sync with server.';
        
        // Still refresh to sync state
        console.log('Refreshing queue after server error...');
        setTimeout(() => loadData(true), 1000);
      } else if (error.status === 404) {
        errorMessage = 'Queue item not found. It may have already been processed.';
        // Remove from local state since it doesn't exist on server
        setQueue(queue.filter(item => item._id !== queueId));
      } else if (error.status === 0) {
        errorMessage = 'Network connection error. Please check your internet connection.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Alert.alert('Error', errorMessage);
    }
  };

  const handleUpdatePriority = async (queueId: string, priority: number) => {
    try {
      const response = await autoApplicationService.updateQueuePriority(queueId, priority);
      if (response.success && response.data) {
        setQueue(queue.map(item =>
          item._id === queueId ? { ...item, priority } : item
        ));
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      console.error('Error updating priority:', error);
      Alert.alert('Error', 'Failed to update priority');
    }
  };

  const handlePauseQueue = async () => {
    if (!user?.id || pausingQueue) return;

    try {
      setPausingQueue(true);
      const response = await autoApplicationService.pauseQueue(user.id);
      if (response.success) {
        const successMessage = response.message || 'Auto-application queue paused';
        Alert.alert('Success', successMessage);
        loadData();
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        throw new Error(response.message || 'Failed to pause queue');
      }
    } catch (error: any) {
      console.error('Error pausing queue:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to pause queue';
      Alert.alert('Error', errorMessage);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setPausingQueue(false);
    }
  };

  const handleResumeQueue = async () => {
    if (!user?.id || resumingQueue) return;

    try {
      setResumingQueue(true);
      const response = await autoApplicationService.resumeQueue(user.id);
      if (response.success) {
        const successMessage = response.message || 'Auto-application queue resumed';
        Alert.alert('Success', successMessage);
        loadData();
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        throw new Error(response.message || 'Failed to resume queue');
      }
    } catch (error: any) {
      console.error('Error resuming queue:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to resume queue';
      Alert.alert('Error', errorMessage);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setResumingQueue(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={THEME.primary} />
          <Text style={styles.loadingText}>Loading dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header />

      {/* Tab Navigation */}
      <Animated.View
        style={styles.tabContainer}
        entering={FadeInUp.duration(600).delay(200)}
      >
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsScrollContent}
        >
          {[
            { key: 'overview', label: 'Overview', icon: 'analytics-outline' },
            { key: 'queue', label: 'Queue', icon: 'list-outline' },
            { key: 'results', label: 'Results', icon: 'checkmark-done-outline' },
            { key: 'autonomous', label: 'Autonomous', icon: 'flash-outline' },
          ].map((t) => (
            <TouchableOpacity
              key={t.key}
              style={[styles.tab, activeTab === t.key && styles.activeTab]}
              onPress={() => {
                setActiveTab(t.key as any);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              activeOpacity={0.9}
            >
              <Ionicons
                name={t.icon as any}
                size={18}
                color={activeTab === t.key ? THEME.primary : THEME.gray}
              />
              <Text
                numberOfLines={1}
                style={[styles.tabText, activeTab === t.key && styles.activeTabText]}
              >
                {t.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </Animated.View>

      {activeTab === 'autonomous' ? (
        <AutonomousStatusDashboard />
      ) : (
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadData(true)}
              colors={[THEME.primary]}
            />
          }
        >
        {activeTab === 'overview' && (
          <>
            {/* Stats Overview */}
            <Animated.View
              style={styles.statsContainer}
              entering={FadeInUp.duration(600).delay(400)}
            >
              <Text style={styles.sectionTitle}>Statistics</Text>
              <View style={styles.statsGrid}>
                <StatsCard
                  title="Total Applications"
                  value={stats?.totalApplications || 0}
                  icon="paper-plane-outline"
                  color={THEME.primary}
                />
                <StatsCard
                  title="Success Rate"
                  value={`${stats?.successRate || 0}%`}
                  icon="checkmark-circle-outline"
                  color={THEME.success}
                />
                <StatsCard
                  title="This Week"
                  value={stats?.applicationsThisWeek || 0}
                  subtitle="applications"
                  icon="calendar-outline"
                  color={THEME.accent}
                />
                <StatsCard
                  title="Avg Response"
                  value={`${stats?.averageResponseTime || 0}ms`}
                  icon="time-outline"
                  color={THEME.warning}
                />
              </View>
            </Animated.View>

            {/* Scraper Integration Stats */}
            {scraperStats && (
              <Animated.View
                style={styles.scraperStatsContainer}
                entering={FadeInUp.duration(600).delay(600)}
              >
                <Text style={styles.sectionTitle}>Scraper Integration</Text>
                <View style={styles.scraperStatsContent}>
                  <View style={styles.scraperStatItem}>
                    <Text style={styles.scraperStatValue}>{scraperStats.autoApplicationsTriggered}</Text>
                    <Text style={styles.scraperStatLabel}>Auto-Apps Triggered</Text>
                  </View>
                  <View style={styles.scraperStatItem}>
                    <Text style={styles.scraperStatValue}>{scraperStats.processedListingsCount}</Text>
                    <Text style={styles.scraperStatLabel}>Listings Processed</Text>
                  </View>
                  <View style={styles.scraperStatItem}>
                    <Text style={styles.scraperStatValue}>{scraperStats.duplicatesSkipped}</Text>
                    <Text style={styles.scraperStatLabel}>Duplicates Skipped</Text>
                  </View>
                </View>
              </Animated.View>
            )}

            {/* Quick Actions */}
            <Animated.View
              style={styles.quickActionsContainer}
              entering={FadeInUp.duration(600).delay(800)}
            >
              <Text style={styles.sectionTitle}>Quick Actions</Text>
              <View style={styles.quickActionsGrid}>
                <TouchableOpacity
                  style={[styles.quickAction, pausingQueue && styles.quickActionDisabled]}
                  onPress={handlePauseQueue}
                  disabled={pausingQueue}
                >
                  {pausingQueue ? (
                    <ActivityIndicator size="small" color={THEME.warning} />
                  ) : (
                    <Ionicons name="pause-circle-outline" size={24} color={THEME.warning} />
                  )}
                  <Text style={styles.quickActionText}>
                    {pausingQueue ? 'Pausing...' : 'Pause Queue'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.quickAction, resumingQueue && styles.quickActionDisabled]}
                  onPress={handleResumeQueue}
                  disabled={resumingQueue}
                >
                  {resumingQueue ? (
                    <ActivityIndicator size="small" color={THEME.success} />
                  ) : (
                    <Ionicons name="play-circle-outline" size={24} color={THEME.success} />
                  )}
                  <Text style={styles.quickActionText}>
                    {resumingQueue ? 'Resuming...' : 'Resume Queue'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickAction}
                  onPress={() => router.push('/auto-application-settings')}
                >
                  <Ionicons name="settings-outline" size={24} color={THEME.primary} />
                  <Text style={styles.quickActionText}>Settings</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickAction}
                  onPress={() => loadData()}
                >
                  <Ionicons name="refresh-outline" size={24} color={THEME.gray} />
                  <Text style={styles.quickActionText}>Refresh</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          </>
        )}

        {activeTab === 'queue' && (
          <Animated.View
            style={styles.queueContainer}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <View style={styles.queueHeader}>
              <Text style={styles.sectionTitle}>Application Queue ({queue.length})</Text>
              <TouchableOpacity
                style={styles.queueAction}
                onPress={() => loadData()}
              >
                <Ionicons name="refresh-outline" size={20} color={THEME.primary} />
              </TouchableOpacity>
            </View>

            {queue.length === 0 ? (
              <View style={styles.emptyState}>
                <Ionicons name="list-outline" size={48} color={THEME.gray} />
                <Text style={styles.emptyStateText}>No applications in queue</Text>
                <Text style={styles.emptyStateSubtext}>
                  Applications will appear here when they match your criteria
                </Text>
              </View>
            ) : (
              <View style={styles.queueList}>
                {queue.map((item) => (
                  <QueueItem
                    key={item._id}
                    item={item}
                    onRemove={handleRemoveFromQueue}
                    onUpdatePriority={handleUpdatePriority}
                  />
                ))}
              </View>
            )}
          </Animated.View>
        )}

        {activeTab === 'results' && (
          <Animated.View
            style={styles.resultsContainer}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <View style={styles.resultsHeader}>
              <Text style={styles.sectionTitle}>Recent Results ({recentResults.length})</Text>
              <TouchableOpacity
                style={styles.queueAction}
                onPress={() => loadData(true)}
              >
                <Ionicons name="refresh-outline" size={20} color={THEME.primary} />
              </TouchableOpacity>
            </View>

            {recentResults.length === 0 ? (
              <View style={styles.emptyState}>
                <Ionicons name="checkmark-done-outline" size={48} color={THEME.gray} />
                <Text style={styles.emptyStateText}>No application results yet</Text>
                <Text style={styles.emptyStateSubtext}>
                  Once the auto-application system processes listings and submits applications, the results will appear here.
                </Text>
                <TouchableOpacity
                  style={styles.refreshButton}
                  onPress={() => loadData(true)}
                >
                  <Ionicons name="refresh-outline" size={16} color={THEME.primary} />
                  <Text style={styles.refreshButtonText}>Refresh</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.resultsList}>
                {recentResults.map((result, index) => {
                  // Validate result data before rendering
                  if (!result || !result._id) {
                    console.warn(`Invalid result at index ${index}:`, result);
                    return (
                      <View key={`invalid-${index}`} style={styles.resultItem}>
                        <Text style={styles.errorText}>Invalid result data</Text>
                      </View>
                    );
                  }
                  
                  try {
                    return <RecentResult key={result._id} result={result} />;
                  } catch (error) {
                    console.error(`Error rendering result ${result._id}:`, error);
                    return (
                      <View key={`error-${result._id}`} style={styles.resultItem}>
                        <View style={styles.errorContainer}>
                          <Ionicons name="warning" size={14} color={THEME.danger} />
                          <Text style={styles.errorText}>Error displaying result</Text>
                        </View>
                      </View>
                    );
                  }
                })}
              </View>
            )}
          </Animated.View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 12,
    paddingVertical: 6,
    paddingHorizontal: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 2,
  },
  tabsScrollContent: {
    paddingHorizontal: 4,
    gap: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 18,
    gap: 6,
    borderWidth: 1,
    borderColor: 'transparent',
    minWidth: 110,
  },
  activeTab: {
    backgroundColor: 'rgba(67,97,238,0.12)',
    borderColor: THEME.primary,
  },
  tabText: {
    fontSize: 13,
    fontWeight: '600',
    color: THEME.gray,
  },
  activeTabText: {
    color: THEME.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 16,
  },
  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    width: (width - 52) / 2,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsCardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statsIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  statsContent: {
    flex: 1,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME.dark,
    marginBottom: 4,
  },
  statsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.gray,
  },
  statsSubtitle: {
    fontSize: 12,
    color: THEME.gray,
    marginTop: 2,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  scraperStatsContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scraperStatsContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  scraperStatItem: {
    alignItems: 'center',
  },
  scraperStatValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: THEME.primary,
    marginBottom: 4,
  },
  scraperStatLabel: {
    fontSize: 12,
    color: THEME.gray,
    textAlign: 'center',
  },
  quickActionsContainer: {
    marginBottom: 24,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickAction: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    width: (width - 52) / 2,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.dark,
    marginTop: 8,
  },
  quickActionDisabled: {
    opacity: 0.6,
  },
  queueContainer: {
    marginBottom: 24,
  },
  queueHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  queueAction: {
    padding: 8,
  },
  queueList: {
    gap: 12,
  },
  queueItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  queueItemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  queueItemTitle: {
    flex: 1,
    marginRight: 12,
  },
  queueItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 4,
  },
  queueItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#ffffff',
  },
  priorityText: {
    fontSize: 12,
    color: THEME.gray,
  },
  queueItemActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: THEME.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeButton: {
    backgroundColor: '#fee2e2',
  },
  queueItemDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 8,
  },
  queueItemDetail: {
    fontSize: 12,
    color: THEME.gray,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fee2e2',
    padding: 8,
    borderRadius: 6,
    gap: 6,
  },
  errorText: {
    fontSize: 12,
    color: THEME.danger,
    flex: 1,
  },
  resultsContainer: {
    marginBottom: 24,
  },
  resultsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  resultsList: {
    gap: 12,
  },
  resultItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultStatus: {
    width: 4,
    height: 40,
    borderRadius: 2,
    marginRight: 12,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 4,
  },
  resultTime: {
    fontSize: 12,
    color: THEME.gray,
    marginBottom: 4,
  },
  resultStatusText: {
    fontSize: 12,
    fontWeight: '500',
    color: THEME.primary,
  },
  resultDetails: {
    marginTop: 8,
    gap: 4,
  },
  resultDetail: {
    fontSize: 11,
    color: THEME.gray,
  },
  resultMeta: {
    alignItems: 'flex-end',
  },
  resultScore: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.primary,
    marginBottom: 2,
  },
  resultResponseTime: {
    fontSize: 12,
    color: THEME.gray,
  },
  landlordResponse: {
    marginTop: 12,
    padding: 12,
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
  },
  landlordResponseText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.dark,
    marginBottom: 4,
  },
  landlordResponseMessage: {
    fontSize: 12,
    color: THEME.gray,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.gray,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: 'center',
    paddingHorizontal: 40,
    marginBottom: 16,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  refreshButtonText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '500',
  },
  bottomSpacing: {
    height: 40,
  },
});