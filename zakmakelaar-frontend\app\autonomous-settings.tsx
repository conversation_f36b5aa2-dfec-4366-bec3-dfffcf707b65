import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Switch,
  Alert,
  TextInput,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  FadeIn,
  FadeInUp,
  SlideInRight,
} from "react-native-reanimated";
import { LinearGradient } from "expo-linear-gradient";
import { useAIStore, AutonomousSettings } from "../store/aiStore";
import { useAuthStore } from "../store/authStore";
import { NumberInput } from "../components/preferences/NumberInput";
import { PrimaryButton } from "../components/PrimaryButton";

// Define theme colors
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

// Enhanced Header Component to match dashboard
const Header = ({
  showBackButton = true,
  onBackPress,
}: {
  showBackButton?: boolean;
  onBackPress?: () => void;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onBackPress?.();
            }}
            style={styles.backButton}
            activeOpacity={0.8}
          >
            <View style={styles.backButtonInner}>
              <Ionicons name="chevron-back" size={24} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Autonomous Settings</Text>
            <Text style={styles.headerSubtitle}>Configure AI automation</Text>
          </View>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

// Settings Section Component
const SettingsSection = ({
  title,
  description,
  children,
}: {
  title: string;
  description?: string;
  children: React.ReactNode;
}) => (
  <Animated.View style={styles.section} entering={FadeInUp.duration(400)}>
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {description && (
        <Text style={styles.sectionDescription}>{description}</Text>
      )}
    </View>
    <View style={styles.sectionContent}>{children}</View>
  </Animated.View>
);

// Toggle Setting Component
const ToggleSetting = ({
  title,
  description,
  value,
  onValueChange,
  disabled = false,
  icon,
}: {
  title: string;
  description?: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
  icon?: string;
}) => {
  const handleToggle = (newValue: boolean) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onValueChange(newValue);
  };

  return (
    <View style={[styles.settingItem, disabled && styles.settingItemDisabled]}>
      <View style={styles.settingContent}>
        {icon && (
          <View style={styles.settingIcon}>
            <Ionicons
              name={icon as any}
              size={20}
              color={value ? THEME.accent : THEME.gray}
            />
          </View>
        )}
        <View style={styles.settingText}>
          <Text
            style={[
              styles.settingTitle,
              disabled && styles.settingTitleDisabled,
            ]}
          >
            {title}
          </Text>
          {description && (
            <Text
              style={[
                styles.settingDescription,
                disabled && styles.settingDescriptionDisabled,
              ]}
            >
              {description}
            </Text>
          )}
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={handleToggle}
        disabled={disabled}
        trackColor={{ false: THEME.lightGray, true: THEME.accent }}
        thumbColor={value ? THEME.light : THEME.gray}
      />
    </View>
  );
};

// Number Input Setting Component
const NumberInputSetting = ({
  title,
  description,
  value,
  minimumValue,
  maximumValue,
  step = 1,
  onValueChange,
  prefix = "",
  suffix = "",
  disabled = false,
  icon,
}: {
  title: string;
  description?: string;
  value: number;
  minimumValue: number;
  maximumValue: number;
  step?: number;
  onValueChange: (value: number) => void;
  prefix?: string;
  suffix?: string;
  disabled?: boolean;
  icon?: string;
}) => {
  return (
    <View style={[styles.settingItem, disabled && styles.settingItemDisabled]}>
      <View style={styles.settingContent}>
        {icon && (
          <View
            style={[
              styles.settingIcon,
              { backgroundColor: `rgba(67, 97, 238, 0.1)` },
            ]}
          >
            <Ionicons name={icon as any} size={20} color={THEME.primary} />
          </View>
        )}
        <View style={styles.settingText}>
          <Text
            style={[
              styles.settingTitle,
              disabled && styles.settingTitleDisabled,
            ]}
          >
            {title}
          </Text>
          {description && (
            <Text
              style={[
                styles.settingDescription,
                disabled && styles.settingDescriptionDisabled,
              ]}
            >
              {description}
            </Text>
          )}
        </View>
      </View>
      <View style={styles.numberInputContainer}>
        <NumberInput
          value={value}
          onValueChange={onValueChange}
          minimumValue={minimumValue}
          maximumValue={maximumValue}
          step={step}
          prefix={prefix}
          suffix={suffix}
          style={disabled && styles.numberInputDisabled}
        />
      </View>
    </View>
  );
};

// Input Setting Component
const InputSetting = ({
  title,
  description,
  value,
  onValueChange,
  placeholder,
  keyboardType = "default",
  disabled = false,
}: {
  title: string;
  description?: string;
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  keyboardType?: "default" | "numeric" | "email-address";
  disabled?: boolean;
}) => {
  return (
    <View style={[styles.settingItem, disabled && styles.settingItemDisabled]}>
      <View style={styles.settingContent}>
        <View style={styles.settingText}>
          <Text
            style={[
              styles.settingTitle,
              disabled && styles.settingTitleDisabled,
            ]}
          >
            {title}
          </Text>
          {description && (
            <Text
              style={[
                styles.settingDescription,
                disabled && styles.settingDescriptionDisabled,
              ]}
            >
              {description}
            </Text>
          )}
        </View>
      </View>
      <TextInput
        style={[styles.textInput, disabled && styles.textInputDisabled]}
        value={value}
        onChangeText={onValueChange}
        placeholder={placeholder}
        keyboardType={keyboardType}
        editable={!disabled}
      />
    </View>
  );
};

// Status Indicator Component
const StatusIndicator = ({
  isActive,
  currentActivity,
  applicationsToday,
  applicationsThisWeek,
  pausedReason,
}: {
  isActive: boolean;
  currentActivity?: string;
  applicationsToday: number;
  applicationsThisWeek: number;
  pausedReason?: string;
}) => {
  const pulseScale = useSharedValue(1);

  useEffect(() => {
    if (isActive) {
      pulseScale.value = withTiming(1.1, { duration: 1000 });
      setTimeout(() => {
        pulseScale.value = withTiming(1, { duration: 1000 });
      }, 1000);
    }
  }, [isActive]);

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));

  return (
    <Animated.View
      style={[
        styles.statusCard,
        { backgroundColor: isActive ? THEME.success : THEME.lightGray },
      ]}
      entering={FadeIn.duration(600)}
    >
      <View style={styles.statusHeader}>
        <Animated.View style={[styles.statusIndicator, pulseStyle]}>
          <View
            style={[
              styles.statusDot,
              { backgroundColor: isActive ? THEME.light : THEME.gray },
            ]}
          />
        </Animated.View>
        <View style={styles.statusText}>
          <Text
            style={[
              styles.statusTitle,
              { color: isActive ? THEME.light : THEME.dark },
            ]}
          >
            Autonomous Mode {isActive ? "Active" : "Inactive"}
          </Text>
          {currentActivity && (
            <Text
              style={[
                styles.statusActivity,
                { color: isActive ? "rgba(255,255,255,0.8)" : THEME.gray },
              ]}
            >
              {currentActivity}
            </Text>
          )}
          {!isActive && pausedReason && (
            <Text
              style={[
                styles.statusActivity,
                { color: THEME.danger, fontWeight: '600' },
              ]}
            >
              ⚠️ {pausedReason}
            </Text>
          )}
        </View>
      </View>

      <View style={styles.statusMetrics}>
        <View style={styles.statusMetric}>
          <Text
            style={[
              styles.statusMetricValue,
              { color: isActive ? THEME.light : THEME.dark },
            ]}
          >
            {applicationsToday}
          </Text>
          <Text
            style={[
              styles.statusMetricLabel,
              { color: isActive ? "rgba(255,255,255,0.8)" : THEME.gray },
            ]}
          >
            Today
          </Text>
        </View>
        <View style={styles.statusMetric}>
          <Text
            style={[
              styles.statusMetricValue,
              { color: isActive ? THEME.light : THEME.dark },
            ]}
          >
            {applicationsThisWeek}
          </Text>
          <Text
            style={[
              styles.statusMetricLabel,
              { color: isActive ? "rgba(255,255,255,0.8)" : THEME.gray },
            ]}
          >
            This Week
          </Text>
        </View>
      </View>
    </Animated.View>
  );
};

// Blocking Info Card Component
const BlockingInfoCard = ({
  pausedReason,
  applicationsToday,
}: {
  pausedReason?: string;
  applicationsToday?: number;
}) => {
  if (!pausedReason) return null;

  // Parse the reason to provide helpful information
  const isLimitIssue = pausedReason.includes("Daily limit reached");
  const limitMatch = pausedReason.match(/(\d+)\/(\d+) applications used/);
  const isGenericNotActive = pausedReason === "Not active" || pausedReason.includes("Not active");
  
  let title = "Autonomous Mode Paused";
  let explanation = pausedReason;
  let actionMessage = "";
  let icon = "pause-circle" as const;
  let cardColor = THEME.warning;
  
  // Handle the generic "Not active" state with a helpful default message
  if (isGenericNotActive) {
    title = "Autonomous Mode Not Available";
    if (applicationsToday && applicationsToday >= 20) {
      explanation = `You've submitted ${applicationsToday} applications today, which may have reached your daily limit.`;
      actionMessage = "Your daily application limit resets tomorrow. You can still apply to properties manually, or increase your daily limit in the Safety Limits section below.";
    } else if (applicationsToday) {
      explanation = `You've submitted ${applicationsToday} applications today. Autonomous mode may be blocked due to system restrictions or reaching your daily limit.`;
      actionMessage = "The system may be preventing autonomous applications. Try again later, or check your settings below.";
    } else {
      explanation = "Autonomous mode cannot be started at this time. This is likely due to reaching your daily application limit or system restrictions.";
      actionMessage = "Please check if you've used all your daily applications. The limit will reset tomorrow, or you can increase your daily limit in the Safety Limits section below.";
    }
    icon = "time";
    cardColor = THEME.primary;
  }
  
  if (isLimitIssue && limitMatch) {
    const [, used, total] = limitMatch;
    title = "Daily Application Limit Reached";
    explanation = `You've used all ${total} of your daily applications for today.`;
    actionMessage = "Autonomous mode will automatically resume tomorrow when your daily limit resets. You can still apply to properties manually in the meantime.";
    icon = "time";
    cardColor = THEME.primary;
  } else if (pausedReason.includes("profile incomplete")) {
    title = "Profile Setup Required";
    explanation = "Your profile information needs to be completed before autonomous mode can be activated.";
    actionMessage = "Please complete your profile in the Settings section to enable autonomous applications.";
    icon = "person-circle";
    cardColor = THEME.accent;
  } else if (pausedReason.includes("documents missing")) {
    title = "Documents Required";
    explanation = "Required documents are missing from your profile.";
    actionMessage = "Please upload all required documents in your profile settings to enable autonomous applications.";
    icon = "document";
    cardColor = THEME.accent;
  }

  return (
    <Animated.View
      style={[styles.blockingCard, { borderLeftColor: cardColor }]}
      entering={FadeInUp.duration(400).delay(300)}
    >
      <View style={styles.blockingHeader}>
        <View style={[styles.blockingIcon, { backgroundColor: `${cardColor}15` }]}>
          <Ionicons name={icon} size={24} color={cardColor} />
        </View>
        <View style={styles.blockingContent}>
          <Text style={styles.blockingTitle}>{title}</Text>
          <Text style={styles.blockingExplanation}>{explanation}</Text>
        </View>
      </View>
      {actionMessage && (
        <View style={styles.blockingAction}>
          <Text style={styles.blockingActionText}>{actionMessage}</Text>
        </View>
      )}
      {(isLimitIssue || isGenericNotActive) && (
        <View style={styles.blockingTip}>
          <Ionicons name="bulb" size={16} color={THEME.warning} />
          <Text style={styles.blockingTipText}>
            {isLimitIssue 
              ? "Tip: You can increase your daily limit in the Safety Limits section below."
              : "Tip: Try increasing your daily limit in the Safety Limits section, or wait until tomorrow for the limit to reset."
            }
          </Text>
        </View>
      )}
    </Animated.View>
  );
};

// Main Component
export default function AutonomousSettingsScreen() {
  const router = useRouter();
  const { user } = useAuthStore();
  const {
    autonomousSettings,
    autonomousStatus,
    updateAutonomousSettings,
    startAutonomousMode,
    stopAutonomousMode,
  } = useAIStore();

  const [localSettings, setLocalSettings] =
    useState<AutonomousSettings>(autonomousSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Update local settings when store changes
  useEffect(() => {
    setLocalSettings(autonomousSettings);
  }, [autonomousSettings]);

  // Check for changes
  useEffect(() => {
    const hasChanges =
      JSON.stringify(localSettings) !== JSON.stringify(autonomousSettings);
    setHasChanges(hasChanges);
  }, [localSettings, autonomousSettings]);

  const handleBackPress = () => {
    if (hasChanges) {
      Alert.alert(
        "Unsaved Changes",
        "You have unsaved changes. Do you want to save them before leaving?",
        [
          {
            text: "Discard",
            style: "destructive",
            onPress: () => router.back(),
          },
          { text: "Save", onPress: handleSaveSettings },
        ]
      );
    } else {
      router.back();
    }
  };

  const updateLocalSetting = <K extends keyof AutonomousSettings>(
    key: K,
    value: AutonomousSettings[K]
  ) => {
    setLocalSettings((prev) => ({ ...prev, [key]: value }));
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      const success = await updateAutonomousSettings(localSettings);
      if (success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert("Success", "Autonomous settings saved successfully!");
      } else {
        throw new Error("Failed to save settings");
      }
    } catch (error) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert("Error", "Failed to save settings. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleToggleAutonomousMode = async () => {
    try {
      if (autonomousStatus.isActive) {
        const success = await stopAutonomousMode();
        if (success) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
      } else {
        // Check if settings are valid before starting
        if (localSettings.autoApplyThreshold < 50) {
          Alert.alert(
            "Low Match Threshold",
            "Your auto-apply threshold is quite low. This may result in applications to properties that don't match your preferences well. Continue?",
            [
              { text: "Cancel", style: "cancel" },
              { text: "Continue", onPress: startAutonomous },
            ]
          );
        } else {
          await startAutonomous();
        }
      }
    } catch (error) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        "Error",
        "Failed to toggle autonomous mode. Please try again."
      );
    }
  };

  const startAutonomous = async () => {
    const success = await startAutonomousMode();
    if (success) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };

  const formatPrice = (value: number) => `€${value}`;
  const formatPercentage = (value: number) => `${value}%`;

  return (
    <SafeAreaView style={styles.container}>
      <Header onBackPress={handleBackPress} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Status Section */}
        <Animated.View entering={FadeInUp.duration(600).delay(200)}>
          <StatusIndicator
            isActive={autonomousStatus.isActive}
            currentActivity={autonomousStatus.currentActivity}
            applicationsToday={autonomousStatus.applicationsToday}
            applicationsThisWeek={autonomousStatus.applicationsThisWeek}
            pausedReason={autonomousStatus.pausedReason}
          />
        </Animated.View>

        {/* Blocking Information Card */}
        <BlockingInfoCard 
          pausedReason={autonomousStatus.pausedReason} 
          applicationsToday={autonomousStatus.applicationsToday}
        />

        {/* Master Toggle */}
        <Animated.View entering={FadeInUp.duration(600).delay(500)}>
          <SettingsSection
            title="Autonomous Mode"
            description="Enable AI to automatically apply to properties that match your criteria"
          >
            <ToggleSetting
              title="Enable Autonomous Mode"
              description="When enabled, the AI will automatically submit applications for highly matching properties"
              value={autonomousStatus.isActive}
              onValueChange={handleToggleAutonomousMode}
              icon="flash"
            />
          </SettingsSection>
        </Animated.View>

        {/* Application Criteria */}
        <Animated.View entering={FadeInUp.duration(600).delay(600)}>
          <SettingsSection
            title="Application Criteria"
            description="Configure when the AI should automatically apply to properties"
          >
            <NumberInputSetting
              title="Auto-Apply Threshold"
              description="Minimum match score required for automatic applications"
              value={localSettings.autoApplyThreshold}
              minimumValue={50}
              maximumValue={100}
              step={5}
              onValueChange={(value) =>
                updateLocalSetting("autoApplyThreshold", value)
              }
              suffix="%"
              disabled={!localSettings.enabled}
              icon="radio-button-on"
            />

            <NumberInputSetting
              title="Minimum Match Score"
              description="Properties below this score won't be considered for auto-apply"
              value={localSettings.autoApplyMinMatchScore}
              minimumValue={60}
              maximumValue={100}
              step={5}
              onValueChange={(value) =>
                updateLocalSetting("autoApplyMinMatchScore", value)
              }
              suffix="%"
              disabled={!localSettings.enabled}
              icon="checkmark-circle"
            />

            <NumberInputSetting
              title="Maximum Price"
              description="Don't auto-apply to properties above this price"
              value={localSettings.autoApplyMaxPrice}
              minimumValue={500}
              maximumValue={10000}
              step={50}
              onValueChange={(value) =>
                updateLocalSetting("autoApplyMaxPrice", value)
              }
              prefix="€"
              disabled={!localSettings.enabled}
              icon="cash"
            />
          </SettingsSection>
        </Animated.View>

        {/* Application Limits */}
        <Animated.View entering={FadeInUp.duration(600).delay(700)}>
          <SettingsSection
            title="Safety Limits"
            description="Set limits to control autonomous application frequency"
          >
            <NumberInputSetting
              title="Daily Application Limit"
              description="Maximum applications per day"
              value={localSettings.maxApplicationsPerDay}
              minimumValue={1}
              maximumValue={20}
              step={1}
              onValueChange={(value) =>
                updateLocalSetting("maxApplicationsPerDay", value)
              }
              disabled={!localSettings.enabled}
              icon="calendar"
            />

            <NumberInputSetting
              title="Weekly Application Limit"
              description="Maximum applications per week"
              value={localSettings.maxApplicationsPerWeek}
              minimumValue={5}
              maximumValue={50}
              step={5}
              onValueChange={(value) =>
                updateLocalSetting("maxApplicationsPerWeek", value)
              }
              disabled={!localSettings.enabled}
              icon="calendar-outline"
            />

            <NumberInputSetting
              title="Budget Override Limit"
              description="Allow applications up to this percentage over your budget"
              value={localSettings.maxBudgetOverride}
              minimumValue={0}
              maximumValue={25}
              step={5}
              onValueChange={(value) =>
                updateLocalSetting("maxBudgetOverride", value)
              }
              suffix="%"
              disabled={!localSettings.enabled}
              icon="trending-up"
            />
          </SettingsSection>
        </Animated.View>

        {/* Application Style */}
        <Animated.View entering={FadeInUp.duration(600).delay(800)}>
          <SettingsSection
            title="Application Style"
            description="Configure how your applications are generated"
          >
            <View style={styles.styleSelector}>
              {(["professional", "personal", "creative"] as const).map(
                (style, index) => (
                  <Animated.View
                    key={style}
                    entering={SlideInRight.duration(600).delay(
                      850 + index * 100
                    )}
                  >
                    <TouchableOpacity
                      style={[
                        styles.styleOption,
                        localSettings.defaultApplicationStyle === style &&
                          styles.styleOptionSelected,
                        !localSettings.enabled && styles.styleOptionDisabled,
                      ]}
                      onPress={() => {
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                        updateLocalSetting("defaultApplicationStyle", style);
                      }}
                      disabled={!localSettings.enabled}
                      activeOpacity={0.8}
                    >
                      <Text
                        style={[
                          styles.styleOptionText,
                          localSettings.defaultApplicationStyle === style &&
                            styles.styleOptionTextSelected,
                          !localSettings.enabled &&
                            styles.styleOptionTextDisabled,
                        ]}
                      >
                        {style.charAt(0).toUpperCase() + style.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  </Animated.View>
                )
              )}
            </View>

            <ToggleSetting
              title="Include Personal Touch"
              description="Add personalized elements to generated applications"
              value={localSettings.includePersonalTouch}
              onValueChange={(value) =>
                updateLocalSetting("includePersonalTouch", value)
              }
              disabled={!localSettings.enabled}
              icon="heart"
            />
          </SettingsSection>
        </Animated.View>

        {/* Safety Controls */}
        <Animated.View entering={FadeInUp.duration(600).delay(900)}>
          <SettingsSection
            title="Safety Controls"
            description="Additional safety measures for autonomous operations"
          >
            <ToggleSetting
              title="Confirm Expensive Properties"
              description="Require manual confirmation for properties significantly above budget"
              value={localSettings.requireConfirmationForExpensive}
              onValueChange={(value) =>
                updateLocalSetting("requireConfirmationForExpensive", value)
              }
              disabled={!localSettings.enabled}
              icon="warning"
            />

            <ToggleSetting
              title="Pause on Multiple Rejections"
              description="Automatically pause autonomous mode after several rejections"
              value={localSettings.pauseOnMultipleRejections}
              onValueChange={(value) =>
                updateLocalSetting("pauseOnMultipleRejections", value)
              }
              disabled={!localSettings.enabled}
              icon="pause"
            />
          </SettingsSection>
        </Animated.View>

        {/* Notification Settings */}
        <Animated.View entering={FadeInUp.duration(600).delay(1000)}>
          <SettingsSection
            title="Notifications"
            description="Configure notifications for autonomous activities"
          >
            <ToggleSetting
              title="Notify on Application"
              description="Get notified when an application is automatically submitted"
              value={localSettings.notifyOnApplication}
              onValueChange={(value) =>
                updateLocalSetting("notifyOnApplication", value)
              }
              disabled={!localSettings.enabled}
              icon="notifications"
            />

            <ToggleSetting
              title="Notify on Response"
              description="Get notified when landlords respond to your applications"
              value={localSettings.notifyOnResponse}
              onValueChange={(value) =>
                updateLocalSetting("notifyOnResponse", value)
              }
              disabled={!localSettings.enabled}
              icon="mail"
            />

            <ToggleSetting
              title="Daily Summary"
              description="Receive a daily summary of autonomous activities"
              value={localSettings.dailySummary}
              onValueChange={(value) =>
                updateLocalSetting("dailySummary", value)
              }
              disabled={!localSettings.enabled}
              icon="document-text"
            />
          </SettingsSection>
        </Animated.View>

        {/* Safety Controls Link */}
        <Animated.View entering={FadeInUp.duration(600).delay(1100)}>
          <SettingsSection
            title="Advanced Safety"
            description="Access advanced safety controls and monitoring"
          >
            <TouchableOpacity
              style={styles.safetyControlsButton}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                router.push("/safety-controls");
              }}
              activeOpacity={0.8}
            >
              <View style={styles.safetyControlsContent}>
                <View style={styles.safetyControlsIcon}>
                  <Ionicons
                    name="shield-checkmark"
                    size={24}
                    color={THEME.danger}
                  />
                </View>
                <View style={styles.safetyControlsText}>
                  <Text style={styles.safetyControlsTitle}>
                    Safety Controls
                  </Text>
                  <Text style={styles.safetyControlsDescription}>
                    Monitor limits, reset counters, and emergency controls
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
              </View>
            </TouchableOpacity>
          </SettingsSection>
        </Animated.View>

        {/* Save Button */}
        {hasChanges && (
          <View style={styles.saveButtonContainer}>
            <PrimaryButton
              title="Save Settings"
              onPress={handleSaveSettings}
              isLoading={isSaving}
              primaryColor={THEME.accent}
              secondaryColor={THEME.secondary}
            />
          </View>
        )}

        <View style={styles.bottomSpacer} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "bold",
    color: THEME.primary,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME.light,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  section: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    overflow: "hidden",
  },
  sectionHeader: {
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: THEME.dark,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 15,
    color: THEME.gray,
    lineHeight: 22,
  },
  sectionContent: {
    padding: 24,
  },
  settingItem: {
    marginBottom: 24,
  },
  settingItemDisabled: {
    opacity: 0.6,
  },
  settingContent: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: THEME.dark,
    marginBottom: 6,
  },
  settingTitleDisabled: {
    color: THEME.gray,
  },
  settingDescription: {
    fontSize: 15,
    color: THEME.gray,
    lineHeight: 22,
  },
  settingDescriptionDisabled: {
    color: "#d1d5db",
  },
  numberInputContainer: {
    marginTop: 12,
  },
  numberInputDisabled: {
    opacity: 0.6,
  },
  textInput: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    backgroundColor: THEME.light,
    borderRadius: 12,
    fontSize: 16,
    color: THEME.dark,
  },
  textInputDisabled: {
    opacity: 0.6,
  },
  styleSelector: {
    flexDirection: "row",
    marginBottom: 20,
    gap: 12,
  },
  styleOption: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 2,
    borderColor: "#e5e7eb",
    backgroundColor: THEME.light,
    borderRadius: 16,
    alignItems: "center",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  styleOptionSelected: {
    borderColor: THEME.accent,
    backgroundColor: "rgba(247, 37, 133, 0.05)",
    shadowColor: THEME.accent,
    shadowOpacity: 0.2,
  },
  styleOptionDisabled: {
    opacity: 0.5,
  },
  styleOptionText: {
    fontSize: 15,
    fontWeight: "600",
    color: THEME.dark,
  },
  styleOptionTextSelected: {
    color: THEME.accent,
  },
  styleOptionTextDisabled: {
    color: THEME.gray,
  },
  statusCard: {
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  statusHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  statusIndicator: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  statusDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  statusText: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 4,
  },
  statusActivity: {
    fontSize: 15,
  },
  statusMetrics: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.2)",
  },
  statusMetric: {
    alignItems: "center",
  },
  statusMetricValue: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 4,
  },
  statusMetricLabel: {
    fontSize: 13,
    fontWeight: "500",
  },
  saveButtonContainer: {
    marginTop: 8,
    marginBottom: 20,
  },
  bottomSpacer: {
    height: 20,
  },
  safetyControlsButton: {
    backgroundColor: THEME.light,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  safetyControlsContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  safetyControlsIcon: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  safetyControlsText: {
    flex: 1,
  },
  safetyControlsTitle: {
    fontSize: 17,
    fontWeight: "bold",
    color: THEME.dark,
    marginBottom: 4,
  },
  safetyControlsDescription: {
    fontSize: 15,
    color: THEME.gray,
    lineHeight: 22,
  },
  // Blocking Info Card styles
  blockingCard: {
    backgroundColor: THEME.light,
    borderRadius: 16,
    marginBottom: 20,
    padding: 20,
    borderLeftWidth: 4,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  blockingHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  blockingIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  blockingContent: {
    flex: 1,
  },
  blockingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: THEME.dark,
    marginBottom: 8,
  },
  blockingExplanation: {
    fontSize: 15,
    color: THEME.gray,
    lineHeight: 22,
  },
  blockingAction: {
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  blockingActionText: {
    fontSize: 14,
    color: THEME.dark,
    lineHeight: 20,
    fontWeight: '500',
  },
  blockingTip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fffbeb',
    borderRadius: 8,
    padding: 12,
  },
  blockingTipText: {
    fontSize: 13,
    color: THEME.warning,
    marginLeft: 8,
    flex: 1,
    fontWeight: '500',
  },
});
