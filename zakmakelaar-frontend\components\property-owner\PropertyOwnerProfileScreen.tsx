import React, { useState, useEffect } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
  ActivityIndicator,
  TextInput,
  ToastAndroid,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeInUp,
  SlideInRight,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useAuthStore } from '../../store/authStore';
import { propertyOwnerService } from '../../services';
import { styles, THEME } from './PropertyOwnerProfileScreen.styles';

// Enhanced Header Component
const Header = ({
  showBackButton = false,
  onBack,
}: {
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={styles.header}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onBack?.();
            }}
            style={styles.backButton}
            activeOpacity={0.8}
          >
            <View style={styles.backButtonInner}>
              <Ionicons name="chevron-back" size={24} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Property Owner Profile</Text>
            <Text style={styles.headerSubtitle}>Manage your account</Text>
          </View>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

interface OwnerProfile {
  businessRegistration: string;
  companyName: string;
  address: string;
  phone: string;
  website?: string;
  description?: string;
  verificationStatus?: 'pending' | 'verified' | 'rejected';
  notificationSettings: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

export function PropertyOwnerProfileScreen() {
  const router = useRouter();
  const { logout } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailUpdates, setEmailUpdates] = useState(true);
  
  const [profile, setProfile] = useState<OwnerProfile>({
    businessRegistration: '',
    companyName: '',
    address: '',
    phone: '',
    website: '',
    description: '',
    verificationStatus: 'pending',
    notificationSettings: {
      email: true,
      push: true,
      sms: false,
    }
  });

  const [editedProfile, setEditedProfile] = useState<OwnerProfile | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    fetchOwnerProfile();
  }, []);

  const fetchOwnerProfile = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await propertyOwnerService.getOwnerProfile();
      setProfile(response.data);
      setNotificationsEnabled(response.data.notificationSettings?.push ?? true);
      setEmailUpdates(response.data.notificationSettings?.email ?? true);
    } catch (err: any) {
      setError(err.message || 'Failed to load profile');
      console.error('Error fetching owner profile:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: async () => {
          // The logout function now handles navigation automatically
          await logout();
        },
      },
    ]);
  };

  const handleEditProfile = () => {
    setEditedProfile({...profile});
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setEditedProfile(null);
    setIsEditing(false);
  };

  // Validate profile data before saving
  const validateProfile = (profile: OwnerProfile): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (!profile.companyName || profile.companyName.trim() === '') {
      errors.push('Company name is required');
    }
    
    if (!profile.businessRegistration || profile.businessRegistration.trim() === '') {
      errors.push('Business registration number is required');
    }
    
    if (!profile.address || profile.address.trim() === '') {
      errors.push('Address is required');
    }
    
    if (!profile.phone || profile.phone.trim() === '') {
      errors.push('Phone number is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };
  
  // Show toast message
  const showToast = (message: string) => {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    } else {
      // For iOS, use Alert as a fallback
      Alert.alert('Notification', message);
    }
  };

  const handleSaveProfile = async () => {
    if (!editedProfile) return;
    
    // Validate profile data
    const validation = validateProfile(editedProfile);
    if (!validation.isValid) {
      setError(validation.errors.join('\n'));
      return;
    }
    
    setIsSaving(true);
    setError(null);
    
    try {
      const updatedProfile = {
        ...editedProfile,
        notificationSettings: {
          email: emailUpdates,
          push: notificationsEnabled,
          sms: editedProfile.notificationSettings.sms,
        }
      };
      
      await propertyOwnerService.updateOwnerProfile(updatedProfile);
      setProfile(updatedProfile);
      setIsEditing(false);
      setEditedProfile(null);
      
      // Show success message
      showToast('Profile updated successfully');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (err: any) {
      setError(err.message || 'Failed to update profile');
      console.error('Error updating owner profile:', err);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleChangePassword = () => {
    router.push("/change-password" as any);
  };

  const handleSupport = () => {
    Alert.alert("Support", "Contact <NAME_EMAIL>");
  };

  const handleAbout = () => {
    Alert.alert(
      "About ZakMakelaar",
      "ZakMakelaar v1.0.0\n\nYour trusted partner in finding the perfect home in the Netherlands."
    );
  };

  const handleVerificationDocuments = () => {
    router.push("/property-owner/verification" as any);
  };

  const handleResetWelcomeScreen = () => {
    Alert.alert(
      "Reset Welcome Screen",
      "This will show the welcome screen again the next time you open the app. Are you sure?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reset",
          style: "default",
          onPress: async () => {
            try {
              const { welcomeService } = await import('../../services/welcomeService');
              await welcomeService.resetWelcomeStatus();
              showToast('Welcome screen will be shown next time');
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            } catch (error) {
              console.error('Error resetting welcome screen:', error);
              showToast('Failed to reset welcome screen');
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            }
          }
        }
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={[THEME.primary, THEME.secondary]}
          style={{
            padding: 24,
            borderRadius: 16,
            alignItems: 'center',
            justifyContent: 'center',
            width: '80%',
            shadowColor: THEME.dark,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 5,
          }}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <ActivityIndicator size="large" color={THEME.light} />
          <Text style={styles.loadingText}>Loading profile...</Text>
          <Text style={{
            marginTop: 8,
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.8)',
            textAlign: 'center',
          }}>Please wait while we fetch your information</Text>
        </LinearGradient>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
    >
      <Header
        showBackButton={true}
        onBack={() => router.back()}
      />

      {error && (
        <Animated.View 
          style={styles.errorContainer}
          entering={FadeInUp.duration(600)}
        >
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={fetchOwnerProfile}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Profile Section */}
      <Animated.View 
        style={styles.profileCard}
        entering={FadeInUp.duration(600).delay(200)}
      >
        <LinearGradient
          colors={['rgba(67, 97, 238, 0.1)', 'rgba(114, 9, 183, 0.1)']}
          style={styles.profileGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.avatarContainer}>
            <LinearGradient
              colors={[THEME.accent, THEME.secondary]}
              style={styles.avatarGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="business" size={32} color={THEME.light} />
            </LinearGradient>
          </View>
          
          {!isEditing ? (
            <>
              <Text style={styles.companyName}>
                {profile.companyName || "Company Name"}
              </Text>
              <Text style={styles.businessRegistration}>
                Reg: {profile.businessRegistration || "Not provided"}
              </Text>
              <View style={styles.verificationBadge}>
                {profile.verificationStatus === 'verified' ? (
                  <>
                    <Ionicons name="checkmark-circle" size={16} color={THEME.success} />
                    <Text style={[styles.verificationText, { color: THEME.success }]}>Verified</Text>
                  </>
                ) : profile.verificationStatus === 'rejected' ? (
                  <>
                    <Ionicons name="close-circle" size={16} color={THEME.danger} />
                    <Text style={[styles.verificationText, { color: THEME.danger }]}>Rejected</Text>
                  </>
                ) : (
                  <>
                    <Ionicons name="time" size={16} color={THEME.warning} />
                    <Text style={[styles.verificationText, { color: THEME.warning }]}>Pending Verification</Text>
                  </>
                )}
              </View>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  handleEditProfile();
                }}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[THEME.primary, THEME.secondary]}
                  style={styles.editButtonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <Ionicons name="create-outline" size={16} color={THEME.light} />
                  <Text style={styles.editButtonText}>Edit Profile</Text>
                </LinearGradient>
              </TouchableOpacity>
            </>
          ) : (
            <View style={styles.editForm}>
              <Text style={styles.editFormLabel}>Company Name</Text>
              <TextInput
                style={styles.editFormInput}
                value={editedProfile?.companyName}
                onChangeText={(text) => setEditedProfile({...editedProfile!, companyName: text})}
                placeholder="Enter company name"
              />
              
              <Text style={styles.editFormLabel}>Business Registration</Text>
              <TextInput
                style={styles.editFormInput}
                value={editedProfile?.businessRegistration}
                onChangeText={(text) => setEditedProfile({...editedProfile!, businessRegistration: text})}
                placeholder="Enter business registration number"
              />
              
              <Text style={styles.editFormLabel}>Address</Text>
              <TextInput
                style={styles.editFormInput}
                value={editedProfile?.address}
                onChangeText={(text) => setEditedProfile({...editedProfile!, address: text})}
                placeholder="Enter address"
                multiline
              />
              
              <Text style={styles.editFormLabel}>Phone</Text>
              <TextInput
                style={styles.editFormInput}
                value={editedProfile?.phone}
                onChangeText={(text) => setEditedProfile({...editedProfile!, phone: text})}
                placeholder="Enter phone number"
                keyboardType="phone-pad"
              />
              
              <Text style={styles.editFormLabel}>Website</Text>
              <TextInput
                style={styles.editFormInput}
                value={editedProfile?.website}
                onChangeText={(text) => setEditedProfile({...editedProfile!, website: text})}
                placeholder="Enter website URL"
                keyboardType="url"
              />
              
              <Text style={styles.editFormLabel}>Description</Text>
              <TextInput
                style={[styles.editFormInput, styles.textArea]}
                value={editedProfile?.description}
                onChangeText={(text) => setEditedProfile({...editedProfile!, description: text})}
                placeholder="Enter company description"
                multiline
                numberOfLines={4}
              />
              
              <View style={styles.editFormButtons}>
                <TouchableOpacity
                  style={[styles.editFormButton, styles.cancelButton]}
                  onPress={handleCancelEdit}
                  disabled={isSaving}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.editFormButton, styles.saveButton]}
                  onPress={handleSaveProfile}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <ActivityIndicator size="small" color={THEME.light} />
                  ) : (
                    <Text style={styles.saveButtonText}>Save</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}
        </LinearGradient>
      </Animated.View>

      {/* Settings Section */}
      <Animated.View 
        style={styles.section}
        entering={FadeInUp.duration(600).delay(400)}
      >
        <Text style={styles.sectionTitle}>Settings</Text>

        <Animated.View entering={SlideInRight.duration(600).delay(500)}>
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: 'rgba(67, 97, 238, 0.1)' }]}>
                <Ionicons name="notifications-outline" size={20} color={THEME.primary} />
              </View>
              <Text style={styles.settingText}>Push Notifications</Text>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={(value) => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setNotificationsEnabled(value);
                if (!isEditing) {
                  // If not in edit mode, save immediately
                  const updatedProfile = {
                    ...profile,
                    notificationSettings: {
                      ...profile.notificationSettings,
                      push: value,
                      email: profile.notificationSettings.email,
                      sms: profile.notificationSettings.sms
                    }
                  };
                  propertyOwnerService.updateOwnerProfile(updatedProfile)
                    .then(() => setProfile(updatedProfile))
                    .catch(err => console.error('Error updating notifications:', err));
                }
              }}
              trackColor={{ false: "#e5e7eb", true: THEME.accent }}
              thumbColor={THEME.light}
            />
          </View>
        </Animated.View>

        <Animated.View entering={SlideInRight.duration(600).delay(600)}>
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: 'rgba(114, 9, 183, 0.1)' }]}>
                <Ionicons name="mail-outline" size={20} color={THEME.secondary} />
              </View>
              <Text style={styles.settingText}>Email Updates</Text>
            </View>
            <Switch
              value={emailUpdates}
              onValueChange={(value) => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setEmailUpdates(value);
                if (!isEditing) {
                  // If not in edit mode, save immediately
                  const updatedProfile = {
                    ...profile,
                    notificationSettings: {
                      ...profile.notificationSettings,
                      email: value,
                      push: profile.notificationSettings.push,
                      sms: profile.notificationSettings.sms
                    }
                  };
                  propertyOwnerService.updateOwnerProfile(updatedProfile)
                    .then(() => setProfile(updatedProfile))
                    .catch(err => console.error('Error updating notifications:', err));
                }
              }}
              trackColor={{ false: "#e5e7eb", true: THEME.accent }}
              thumbColor={THEME.light}
            />
          </View>
        </Animated.View>

        <Animated.View entering={SlideInRight.duration(600).delay(700)}>
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              handleVerificationDocuments();
            }}
            activeOpacity={0.8}
          >
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: 'rgba(16, 185, 129, 0.1)' }]}>
                <Ionicons name="shield-checkmark-outline" size={20} color={THEME.success} />
              </View>
              <Text style={styles.settingText}>Verification Documents</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View entering={SlideInRight.duration(600).delay(800)}>
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              handleChangePassword();
            }}
            activeOpacity={0.8}
          >
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: 'rgba(247, 37, 133, 0.1)' }]}>
                <Ionicons name="lock-closed-outline" size={20} color={THEME.accent} />
              </View>
              <Text style={styles.settingText}>Change Password</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View entering={SlideInRight.duration(600).delay(850)}>
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              handleResetWelcomeScreen();
            }}
            activeOpacity={0.8}
          >
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: 'rgba(67, 97, 238, 0.1)' }]}>
                <Ionicons name="refresh-outline" size={20} color={THEME.primary} />
              </View>
              <Text style={styles.settingText}>Show Welcome Screen Again</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>

      {/* Support Section */}
      <Animated.View 
        style={styles.section}
        entering={FadeInUp.duration(600).delay(900)}
      >
        <Text style={styles.sectionTitle}>Support</Text>

        <Animated.View entering={SlideInRight.duration(600).delay(1000)}>
          <TouchableOpacity 
            style={styles.settingItem} 
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              handleSupport();
            }}
            activeOpacity={0.8}
          >
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: 'rgba(245, 158, 11, 0.1)' }]}>
                <Ionicons name="help-circle-outline" size={20} color={THEME.warning} />
              </View>
              <Text style={styles.settingText}>Help & Support</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
          </TouchableOpacity>
        </Animated.View>

        <Animated.View entering={SlideInRight.duration(600).delay(1100)}>
          <TouchableOpacity 
            style={styles.settingItem} 
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              handleAbout();
            }}
            activeOpacity={0.8}
          >
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: 'rgba(107, 114, 128, 0.1)' }]}>
                <Ionicons name="information-circle-outline" size={20} color={THEME.gray} />
              </View>
              <Text style={styles.settingText}>About</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={THEME.gray} />
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>

      {/* Logout Section */}
      <Animated.View 
        style={styles.logoutSection}
        entering={FadeInUp.duration(600).delay(1200)}
      >
        <TouchableOpacity 
          style={styles.logoutButton} 
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            handleLogout();
          }}
          activeOpacity={0.8}
        >
          <View style={styles.logoutIconContainer}>
            <Ionicons name="log-out-outline" size={20} color={THEME.danger} />
          </View>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </Animated.View>
    </ScrollView>
  );
}
