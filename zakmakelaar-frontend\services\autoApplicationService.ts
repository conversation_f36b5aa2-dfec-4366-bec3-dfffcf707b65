import { apiService, ApiResponse } from "./api";
import { Listing } from "./listingsService";
import { User } from "./authService";

// Types for Auto-Application System
export interface AutoApplicationSettings {
  _id?: string;
  userId: string;
  enabled: boolean;
  settings: {
    maxApplicationsPerDay: number;
    applicationTemplate: "professional" | "casual" | "student" | "expat";
    autoSubmit: boolean;
    requireManualReview: boolean;
    notificationPreferences: {
      immediate: boolean;
      daily: boolean;
      weekly: boolean;
    };
    language: "english" | "dutch";
  };
  criteria: {
    maxPrice?: number;
    minRooms?: number;
    maxRooms?: number;
    propertyTypes: string[];
    locations: string[];
    excludeKeywords: string[];
    includeKeywords: string[];
    minSize?: number;
    maxSize?: number;
    furnished?: boolean;
    petsAllowed?: boolean;
    smokingAllowed?: boolean;
  };
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    dateOfBirth?: string;
    nationality?: string;
    occupation?: string;
    monthlyIncome?: number;
    employmentStatus?: "employed" | "self-employed" | "student" | "unemployed";
    employer?: string;
    moveInDate?: string;
    leaseDuration?: number;
    numberOfOccupants?: number;
    hasPets?: boolean;
    smokingAllowed?: boolean;
    additionalInfo?: string;
  };
  documents: Array<{
    type: "id" | "income" | "employment" | "bank" | "reference" | "other";
    name: string;
    url: string;
    uploadedAt: Date;
  }>;
  statistics: {
    totalApplications: number;
    successfulApplications: number;
    pendingApplications: number;
    rejectedApplications: number;
    averageResponseTime: number;
    lastApplicationDate?: Date;
  };
  status: {
    isActive: boolean;
    lastActivity?: Date;
    currentQueue: number;
    dailyApplicationsUsed: number;
    weeklyApplicationsUsed: number;
    monthlyApplicationsUsed: number;
    lastResetDate: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ApplicationQueue {
  _id: string;
  userId: string;
  listingId: string;
  listingUrl: string;
  listingTitle: string;
  status: "pending" | "processing" | "completed" | "failed" | "cancelled";
  priority: number;
  scheduledFor: Date;
  attempts: number;
  maxAttempts: number;
  lastAttempt?: Date;
  errorMessage?: string;
  estimatedSubmissionTime?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApplicationResult {
  _id: string;
  userId: string;
  queueId: string;
  listingId: string;
  listingUrl: string;
  listingTitle: string;
  status: "success" | "failed" | "cancelled";
  submittedAt: Date;
  responseTime: number;
  formData: Record<string, any>;
  screenshots: string[];
  // Optional overall score from backend (0-100)
  successScore?: number;
  errorDetails?: {
    message: string;
    stack?: string;
    captchaDetected: boolean;
    blockingDetected: boolean;
  };
  landlordResponse?: {
    status: "accepted" | "rejected" | "pending";
    message?: string;
    receivedAt: Date;
  };
  metadata: {
    applicationVersion?: string;
    browserVersion?: string;
    platform?: string;
    serverId?: string;
    processingNode?: string;
    reviewRequired?: boolean;
    reviewedBy?: string;
    reviewedAt?: Date;
    reviewNotes?: string;
    retentionDate?: Date;
    archived?: boolean;
  };
  metrics?: {
    processingTime?: number;
    formDetectionTime?: number;
    formFillingTime?: number;
    submissionTime?: number;
    formComplexity?: "simple" | "moderate" | "complex" | "very_complex";
    fieldsDetected?: number;
    fieldsSuccessfullyFilled?: number;
    documentsUploaded?: number;
    successProbability?: number; // Could be 0..1 or 0..100 depending on backend
    antiDetectionMeasuresUsed?: string[];
    browserFingerprint?: string;
    userAgentUsed?: string;
    networkLatency?: number;
    pageLoadTime?: number;
  };
  response?: {
    success?: boolean;
    message?: string;
    redirectUrl?: string;
    responseCode?: string;
    responseTime?: number;
    headers?: any;
    cookies?: any;
  };
  createdAt: Date;
}

export interface AutoApplicationStats {
  totalApplications: number;
  successfulApplications: number;
  failedApplications: number;
  pendingApplications: number;
  successRate: number;
  averageResponseTime: number;
  applicationsToday: number;
  applicationsThisWeek: number;
  applicationsThisMonth: number;
  lastApplicationDate?: Date;
  topPerformingCriteria: Array<{
    criteria: string;
    successRate: number;
    applications: number;
  }>;
  recentActivity: Array<{
    date: Date;
    applications: number;
    successes: number;
  }>;
}

export interface ScraperIntegrationStats {
  processedListingsCount: number;
  qualityScoreCacheSize: number;
  currentlyProcessingCount: number;
  cacheExpiryMs: number;
  minQualityScore: number;
  autoApplicationsTriggered: number;
  duplicatesSkipped: number;
  lastProcessingTime?: Date;
}

class AutoApplicationService {
  private baseUrl = "/auto-application";

  // Settings Management
  async getSettings(
    userId: string
  ): Promise<ApiResponse<AutoApplicationSettings>> {
    return apiService.get(`${this.baseUrl}/settings/${userId}`);
  }

  async updateSettings(
    userId: string,
    settings: Partial<AutoApplicationSettings>
  ): Promise<ApiResponse<AutoApplicationSettings>> {
    return apiService.put(`${this.baseUrl}/settings/${userId}`, settings);
  }

  async enableAutoApplication(
    userId: string,
    settings: Partial<AutoApplicationSettings>
  ): Promise<ApiResponse<AutoApplicationSettings>> {
    console.log("📡 autoApplicationService.enableAutoApplication called:");
    console.log("   userId:", userId);
    console.log("   settings:", settings);
    console.log("   endpoint:", `${this.baseUrl}/enable`);
    
    try {
      const result = await apiService.post(`${this.baseUrl}/enable`, { userId, settings });
      console.log("📡 autoApplicationService.enableAutoApplication result:", result);
      return result;
    } catch (error: any) {
      console.error("📡 autoApplicationService.enableAutoApplication error:", error);
      console.error("   Error response:", error.response?.data);
      console.error("   Error status:", error.response?.status);
      throw error;
    }
  }

  async disableAutoApplication(
    userId: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    console.log("🚫 autoApplicationService.disableAutoApplication called for userId:", userId);
    return apiService.post(`${this.baseUrl}/disable`, { userId });
  }

  // Queue Management
  async getQueue(userId: string): Promise<ApiResponse<ApplicationQueue[]>> {
    return apiService.get(`${this.baseUrl}/queue/${userId}`);
  }

  // Global processing controls
  async pauseProcessing(): Promise<ApiResponse<{ success: boolean }>> {
    return apiService.post(`${this.baseUrl}/pause`);
  }

  async resumeProcessing(): Promise<ApiResponse<{ success: boolean }>> {
    return apiService.post(`${this.baseUrl}/resume`);
  }

  async addToQueue(
    userId: string,
    listingId: string,
    priority: number = 5
  ): Promise<ApiResponse<ApplicationQueue>> {
    return apiService.post(`${this.baseUrl}/queue`, {
      userId,
      listingId,
      priority,
    });
  }

  async removeFromQueue(
    queueId: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    // Validate queueId
    if (!queueId || typeof queueId !== 'string' || queueId.trim() === '') {
      console.error('Invalid queueId provided:', queueId);
      return {
        success: false,
        error: 'Invalid queue ID provided',
        message: 'Invalid queue ID provided'
      };
    }

    try {
      console.log(`Attempting to remove queue item: ${queueId}`);
      const response = await apiService.delete(`${this.baseUrl}/queue/${queueId}`, {});
      console.log('Remove from queue response:', response);
      return response;
    } catch (error: any) {
      console.error('Error in removeFromQueue:', error);
      // Re-throw to maintain error handling chain
      throw error;
    }
  }

  async updateQueuePriority(
    queueId: string,
    priority: number
  ): Promise<ApiResponse<ApplicationQueue>> {
    return apiService.put(`${this.baseUrl}/queue/${queueId}/priority`, {
      priority,
    });
  }

  async pauseQueue(userId: string): Promise<ApiResponse<{ success: boolean }>> {
    return apiService.post(`${this.baseUrl}/queue/${userId}/pause`);
  }

  async resumeQueue(
    userId: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    // Prefer user-specific resume if provided; fall back to global resume when available
    try {
      return await apiService.post(`${this.baseUrl}/queue/${userId}/resume`);
    } catch (e) {
      return this.resumeProcessing() as any;
    }
  }

  // Application Results
  async getResults(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<
    ApiResponse<{
      results: ApplicationResult[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    return apiService.get(`${this.baseUrl}/results/${userId}`, { page, limit });
  }

  async getResult(resultId: string): Promise<ApiResponse<ApplicationResult>> {
    return apiService.get(`${this.baseUrl}/results/detail/${resultId}`);
  }

  async retryFailedApplication(
    resultId: string
  ): Promise<ApiResponse<ApplicationQueue>> {
    return apiService.post(`${this.baseUrl}/results/${resultId}/retry`);
  }

  // Statistics and Analytics
  async getStats(userId: string): Promise<ApiResponse<AutoApplicationStats>> {
    return apiService.get(`${this.baseUrl}/stats/${userId}`);
  }

  async getStatus(_userId?: string): Promise<ApiResponse<{
    isEnabled: boolean;
    isActive: boolean;
    currentActivity?: string;
    applicationsToday: number;
    applicationsThisWeek: number;
    lastActivity?: Date;
    pausedReason?: string;
  }>> {
    // Backend exposes only a global status endpoint; per-user inference happens in the store
    return apiService.get(`${this.baseUrl}/status`);
  }

  async getGlobalStats(): Promise<
    ApiResponse<{
      totalUsers: number;
      totalApplications: number;
      averageSuccessRate: number;
      systemUptime: number;
    }>
  > {
    return apiService.get(`${this.baseUrl}/stats/global`);
  }

  // Daily limit management
  async resetDailyLimit(
    userId: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    console.log("🔄 Attempting to reset daily limit for userId:", userId);
    return apiService.post(`${this.baseUrl}/reset-daily-limit`, { userId });
  }

  // Manual Application Submission
  async submitManualApplication(
    userId: string,
    listingId: string,
    applicationData: Record<string, any>
  ): Promise<ApiResponse<ApplicationResult>> {
    return apiService.post(`${this.baseUrl}/submit/manual`, {
      userId,
      listingId,
      applicationData,
    });
  }

  // Test and Validation
  async testCriteria(
    userId: string,
    listing: Listing
  ): Promise<
    ApiResponse<{
      matches: boolean;
      score: number;
      reasons: string[];
      recommendations: string[];
    }>
  > {
    return apiService.post(`${this.baseUrl}/test-criteria`, {
      userId,
      listing,
    });
  }

  async validateSettings(settings: Partial<AutoApplicationSettings>): Promise<
    ApiResponse<{
      valid: boolean;
      errors: string[];
      warnings: string[];
    }>
  > {
    return apiService.post(`${this.baseUrl}/validate-settings`, settings);
  }

  // Document Management
  async uploadDocument(
    userId: string,
    file: File | Blob,
    type: "id" | "income" | "employment" | "bank" | "reference" | "other",
    name: string
  ): Promise<
    ApiResponse<{
      url: string;
      type: string;
      name: string;
      uploadedAt: Date;
    }>
  > {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", type);
    formData.append("name", name);
    formData.append("userId", userId);

    return apiService.post(`${this.baseUrl}/documents/upload`, formData);
  }

  async deleteDocument(
    userId: string,
    documentUrl: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    return apiService.delete(`${this.baseUrl}/documents`, {
      userId,
      documentUrl,
    });
  }

  // Real-time Updates and WebSocket Integration
  async subscribeToUpdates(
    userId: string,
    callback: (update: any) => void
  ): Promise<() => void> {
    // This would integrate with WebSocket service
    // For now, we'll use polling as fallback
    const interval = setInterval(async () => {
      try {
        const queueResponse = await this.getQueue(userId);
        const statsResponse = await this.getStats(userId);

        if (queueResponse.success && statsResponse.success) {
          callback({
            type: "status_update",
            queue: queueResponse.data,
            stats: statsResponse.data,
            timestamp: new Date(),
          });
        }
      } catch (error) {
        console.error("Error polling for updates:", error);
      }
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  }

  // Scraper Integration
  async getScraperIntegrationStats(): Promise<
    ApiResponse<ScraperIntegrationStats>
  > {
    return apiService.get(`${this.baseUrl}/scraper/stats`);
  }

  async triggerScraperIntegration(): Promise<
    ApiResponse<{ success: boolean; message: string }>
  > {
    return apiService.post(`${this.baseUrl}/scraper/trigger`);
  }

  async clearScraperCache(): Promise<ApiResponse<{ success: boolean }>> {
    return apiService.post(`${this.baseUrl}/scraper/clear-cache`);
  }

  // Learning and Optimization
  async getOptimizationSuggestions(userId: string): Promise<
    ApiResponse<{
      suggestions: Array<{
        type: "criteria" | "timing" | "template" | "documents";
        title: string;
        description: string;
        impact: "low" | "medium" | "high";
        implementation: string;
      }>;
      currentPerformance: {
        successRate: number;
        averageResponseTime: number;
        competitiveness: number;
      };
    }>
  > {
    return apiService.get(`${this.baseUrl}/optimization/${userId}`);
  }

  async applyOptimization(
    userId: string,
    optimizationType: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    return apiService.post(`${this.baseUrl}/optimization/${userId}/apply`, {
      type: optimizationType,
    });
  }

  // Daily Limit Management
  async resetDailyLimit(
    userId: string
  ): Promise<ApiResponse<{ success: boolean; newLimit: number }>> {
    console.log("🔄 Attempting to reset daily limit for userId:", userId);
    return apiService.post(`${this.baseUrl}/reset-daily-limit`, { userId });
  }

  // Emergency Controls
  async emergencyStop(
    userId: string,
    reason: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    return apiService.post(`${this.baseUrl}/emergency-stop`, {
      userId,
      reason,
    });
  }

  async getEmergencyStatus(userId: string): Promise<
    ApiResponse<{
      isStopped: boolean;
      reason?: string;
      stoppedAt?: Date;
      canResume: boolean;
    }>
  > {
    return apiService.get(`${this.baseUrl}/emergency-status/${userId}`);
  }

  // Health and Monitoring
  async getSystemHealth(): Promise<
    ApiResponse<{
      status: "healthy" | "degraded" | "unhealthy";
      components: {
        queue: "operational" | "degraded" | "down";
        scraper: "operational" | "degraded" | "down";
        browser: "operational" | "degraded" | "down";
        database: "operational" | "degraded" | "down";
      };
      metrics: {
        queueSize: number;
        processingRate: number;
        errorRate: number;
        averageProcessingTime: number;
      };
      lastHealthCheck: Date;
    }>
  > {
    return apiService.get(`${this.baseUrl}/health`);
  }

  // Utility Methods
  formatApplicationResult(result: ApplicationResult): {
    statusText: string;
    statusColor: string;
    timeAgo: string;
    successRate: string;
  } {
    const statusMap = {
      success: { text: "Successfully Submitted", color: "#10b981" },
      failed: { text: "Submission Failed", color: "#ef4444" },
      cancelled: { text: "Cancelled", color: "#6b7280" },
    };

    const status = statusMap[result.status] || {
      text: "Unknown",
      color: "#6b7280",
    };

    const timeAgo = this.getTimeAgo(result.submittedAt);

    // Choose a real scoring signal from backend
    const normalize = (val: number | undefined | null) => {
      if (val == null || isNaN(val as number)) return null;
      const n = Number(val);
      if (n <= 1) return Math.round(n * 100);
      return Math.round(Math.max(0, Math.min(100, n)));
    };

    const scoreFromSuccessScore = normalize((result as any).successScore as number);
    const scoreFromMetrics = normalize(result.metrics?.successProbability as number);
    const scoreFromAiConfidence = normalize((result as any)?.aiContent?.confidence as number);

    let chosenScore: number | null = null;
    if (scoreFromSuccessScore != null) {
      chosenScore = scoreFromSuccessScore;
    } else if (scoreFromMetrics != null) {
      // Heuristic: treat a flat 50 as a placeholder; prefer AI confidence if available
      chosenScore = (scoreFromMetrics === 50 && scoreFromAiConfidence != null)
        ? scoreFromAiConfidence
        : scoreFromMetrics;
    } else if (scoreFromAiConfidence != null) {
      chosenScore = scoreFromAiConfidence;
    }

    const successRate = chosenScore != null ? `${chosenScore}%` : "N/A";

    return {
      statusText: status.text,
      statusColor: status.color,
      timeAgo,
      successRate,
    };
  }

  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - new Date(date).getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  }

  calculateQualityScore(
    listing: Listing,
    userCriteria: AutoApplicationSettings["criteria"]
  ): number {
    let score = 0.5; // Base score

    // Price match (30%)
    if (userCriteria.maxPrice && listing.price != null) {
      const price =
        typeof listing.price === "number"
          ? listing.price
          : Number(listing.price);
      if (!isNaN(price) && price <= userCriteria.maxPrice) {
        score += 0.3;
      } else if (!isNaN(price) && price <= userCriteria.maxPrice * 1.1) {
        score += 0.15; // Partial credit for close matches
      }
    }

    // Room count match (20%)
    if (
      userCriteria.minRooms &&
      userCriteria.maxRooms &&
      listing.bedrooms != null
    ) {
      const rooms =
        typeof listing.bedrooms === "number"
          ? listing.bedrooms
          : Number(listing.bedrooms);
      if (
        !isNaN(rooms) &&
        rooms >= userCriteria.minRooms &&
        rooms <= userCriteria.maxRooms
      ) {
        score += 0.2;
      }
    }

    // Location match (20%)
    if (userCriteria.locations.length > 0 && listing.location) {
      const listingLocation =
        typeof listing.location === "string"
          ? listing.location.toLowerCase()
          : listing.location.address?.toLowerCase() || "";
      const hasLocationMatch = userCriteria.locations.some((loc) =>
        listingLocation.includes(loc.toLowerCase())
      );
      if (hasLocationMatch) {
        score += 0.2;
      }
    }

    // Property type match (15%)
    if (userCriteria.propertyTypes.length > 0 && listing.propertyType) {
      const hasTypeMatch = userCriteria.propertyTypes.some((type) =>
        listing.propertyType?.toLowerCase().includes(type.toLowerCase())
      );
      if (hasTypeMatch) {
        score += 0.15;
      }
    }

    // Size match (10%)
    if (userCriteria.minSize && userCriteria.maxSize && listing.area != null) {
      const area =
        typeof listing.area === "number" ? listing.area : Number(listing.area);
      if (
        !isNaN(area) &&
        area >= userCriteria.minSize &&
        area <= userCriteria.maxSize
      ) {
        score += 0.1;
      }
    }

    // Keyword exclusion check (penalty)
    if (userCriteria.excludeKeywords.length > 0 && listing.description) {
      const description = listing.description.toLowerCase();
      const hasExcludedKeyword = userCriteria.excludeKeywords.some((keyword) =>
        description.includes(keyword.toLowerCase())
      );
      if (hasExcludedKeyword) {
        score -= 0.2;
      }
    }

    // Keyword inclusion bonus (5%)
    if (userCriteria.includeKeywords.length > 0 && listing.description) {
      const description = listing.description.toLowerCase();
      const hasIncludedKeyword = userCriteria.includeKeywords.some((keyword) =>
        description.includes(keyword.toLowerCase())
      );
      if (hasIncludedKeyword) {
        score += 0.05;
      }
    }

    return Math.max(0, Math.min(1, score)); // Clamp between 0 and 1
  }
}

export const autoApplicationService = new AutoApplicationService();
export default autoApplicationService;
